menu "nrf (C:/ncs/v3.0.2/nrf)"
osource "C:/ncs/v3.0.2/nrf/Kconfig.nrf"
config ZEPHYR_NRF_MODULE
	bool
	default y
endmenu
menu "hostap (C:/ncs/v3.0.2/modules/lib/hostap)"
osource "$(ZEPHYR_HOSTAP_KCONFIG)"
config ZEPHYR_HOSTAP_MODULE
	bool
	default y
endmenu
menu "mcuboot (C:/ncs/v3.0.2/bootloader/mcuboot)"
osource "$(ZEPHYR_MCUBOOT_KCONFIG)"
config ZEPHYR_MCUBOOT_MODULE
	bool
	default y
endmenu
menu "mbedtls (C:/ncs/v3.0.2/modules/crypto/mbedtls)"
osource "$(ZEPHYR_MBEDTLS_KCONFIG)"
config ZEPHYR_MBEDTLS_MODULE
	bool
	default y
endmenu
menu "oberon-psa-crypto (C:/ncs/v3.0.2/modules/crypto/oberon-psa-crypto)"
osource "C:/ncs/v3.0.2/modules/crypto/oberon-psa-crypto/Kconfig.oberon_psa_crypto"
config ZEPHYR_OBERON_PSA_CRYPTO_MODULE
	bool
	default y
endmenu
menu "trusted-firmware-m (C:/ncs/v3.0.2/modules/tee/tf-m/trusted-firmware-m)"
osource "$(ZEPHYR_TRUSTED_FIRMWARE_M_KCONFIG)"
config ZEPHYR_TRUSTED_FIRMWARE_M_MODULE
	bool
	default y
endmenu
config ZEPHYR_PSA_ARCH_TESTS_MODULE
	bool
	default y
menu "cjson (C:/ncs/v3.0.2/modules/lib/cjson)"
osource "$(ZEPHYR_CJSON_KCONFIG)"
config ZEPHYR_CJSON_MODULE
	bool
	default y
endmenu
menu "azure-sdk-for-c (C:/ncs/v3.0.2/modules/lib/azure-sdk-for-c)"
osource "$(ZEPHYR_AZURE_SDK_FOR_C_KCONFIG)"
config ZEPHYR_AZURE_SDK_FOR_C_MODULE
	bool
	default y
endmenu
menu "cirrus-logic (C:/ncs/v3.0.2/modules/hal/cirrus-logic)"
osource "C:/ncs/v3.0.2/modules/hal/cirrus-logic/Kconfig"
config ZEPHYR_CIRRUS_LOGIC_MODULE
	bool
	default y
endmenu
menu "openthread (C:/ncs/v3.0.2/modules/lib/openthread)"
osource "$(ZEPHYR_OPENTHREAD_KCONFIG)"
config ZEPHYR_OPENTHREAD_MODULE
	bool
	default y
endmenu
menu "suit-generator (C:/ncs/v3.0.2/modules/lib/suit-generator)"
osource "C:/ncs/v3.0.2/modules/lib/suit-generator/ncs/Kconfig"
config ZEPHYR_SUIT_GENERATOR_MODULE
	bool
	default y
endmenu
menu "suit-processor (C:/ncs/v3.0.2/modules/lib/suit-processor)"
osource "C:/ncs/v3.0.2/modules/lib/suit-processor/Kconfig"
config ZEPHYR_SUIT_PROCESSOR_MODULE
	bool
	default y
endmenu
menu "memfault-firmware-sdk (C:/ncs/v3.0.2/modules/lib/memfault-firmware-sdk)"
osource "C:/ncs/v3.0.2/modules/lib/memfault-firmware-sdk/ports/zephyr/Kconfig"
config ZEPHYR_MEMFAULT_FIRMWARE_SDK_MODULE
	bool
	default y
endmenu
menu "coremark (C:/ncs/v3.0.2/modules/benchmark/coremark)"
osource "$(ZEPHYR_COREMARK_KCONFIG)"
config ZEPHYR_COREMARK_MODULE
	bool
	default y
endmenu
menu "canopennode (C:/ncs/v3.0.2/modules/lib/canopennode)"
osource "$(ZEPHYR_CANOPENNODE_KCONFIG)"
config ZEPHYR_CANOPENNODE_MODULE
	bool
	default y
endmenu
menu "chre (C:/ncs/v3.0.2/modules/lib/chre)"
osource "C:/ncs/v3.0.2/modules/lib/chre/platform/zephyr/Kconfig"
config ZEPHYR_CHRE_MODULE
	bool
	default y
endmenu
menu "lz4 (C:/ncs/v3.0.2/modules/lib/lz4)"
osource "$(ZEPHYR_LZ4_KCONFIG)"
config ZEPHYR_LZ4_MODULE
	bool
	default y
endmenu
menu "nanopb (C:/ncs/v3.0.2/modules/lib/nanopb)"
osource "$(ZEPHYR_NANOPB_KCONFIG)"
config ZEPHYR_NANOPB_MODULE
	bool
	default y
endmenu
config ZEPHYR_TF_M_TESTS_MODULE
	bool
	default y
menu "zscilib (C:/ncs/v3.0.2/modules/lib/zscilib)"
osource "C:/ncs/v3.0.2/modules/lib/zscilib/Kconfig.zscilib"
config ZEPHYR_ZSCILIB_MODULE
	bool
	default y
endmenu
menu "cmsis (C:/ncs/v3.0.2/modules/hal/cmsis)"
osource "$(ZEPHYR_CMSIS_KCONFIG)"
config ZEPHYR_CMSIS_MODULE
	bool
	default y
endmenu
menu "cmsis-dsp (C:/ncs/v3.0.2/modules/lib/cmsis-dsp)"
osource "$(ZEPHYR_CMSIS_DSP_KCONFIG)"
config ZEPHYR_CMSIS_DSP_MODULE
	bool
	default y
endmenu
menu "cmsis-nn (C:/ncs/v3.0.2/modules/lib/cmsis-nn)"
osource "$(ZEPHYR_CMSIS_NN_KCONFIG)"
config ZEPHYR_CMSIS_NN_MODULE
	bool
	default y
endmenu
menu "fatfs (C:/ncs/v3.0.2/modules/fs/fatfs)"
osource "$(ZEPHYR_FATFS_KCONFIG)"
config ZEPHYR_FATFS_MODULE
	bool
	default y
endmenu
menu "hal_nordic (C:/ncs/v3.0.2/modules/hal/nordic)"
osource "$(ZEPHYR_HAL_NORDIC_KCONFIG)"
config ZEPHYR_HAL_NORDIC_MODULE
	bool
	default y

config ZEPHYR_HAL_NORDIC_MODULE_BLOBS
	bool
endmenu
menu "hal_st (C:/ncs/v3.0.2/modules/hal/st)"
osource "$(ZEPHYR_HAL_ST_KCONFIG)"
config ZEPHYR_HAL_ST_MODULE
	bool
	default y
endmenu
menu "hal_tdk (C:/ncs/v3.0.2/modules/hal/tdk)"
osource "$(ZEPHYR_HAL_TDK_KCONFIG)"
config ZEPHYR_HAL_TDK_MODULE
	bool
	default y
endmenu
config ZEPHYR_HAL_WURTHELEKTRONIK_MODULE
	bool
	default y
menu "liblc3 (C:/ncs/v3.0.2/modules/lib/liblc3)"
osource "$(ZEPHYR_LIBLC3_KCONFIG)"
config ZEPHYR_LIBLC3_MODULE
	bool
	default y
endmenu
config ZEPHYR_LIBMETAL_MODULE
	bool
	default y
menu "littlefs (C:/ncs/v3.0.2/modules/fs/littlefs)"
osource "$(ZEPHYR_LITTLEFS_KCONFIG)"
config ZEPHYR_LITTLEFS_MODULE
	bool
	default y
endmenu
menu "loramac-node (C:/ncs/v3.0.2/modules/lib/loramac-node)"
osource "$(ZEPHYR_LORAMAC_NODE_KCONFIG)"
config ZEPHYR_LORAMAC_NODE_MODULE
	bool
	default y
endmenu
menu "lvgl (C:/ncs/v3.0.2/modules/lib/gui/lvgl)"
osource "C:/ncs/v3.0.2/modules/lib/gui/lvgl/zephyr/Kconfig"
config ZEPHYR_LVGL_MODULE
	bool
	default y
endmenu
config ZEPHYR_MIPI_SYS_T_MODULE
	bool
	default y
menu "nrf_wifi (C:/ncs/v3.0.2/modules/lib/nrf_wifi)"
osource "$(ZEPHYR_NRF_WIFI_KCONFIG)"
config ZEPHYR_NRF_WIFI_MODULE
	bool
	default y

config ZEPHYR_NRF_WIFI_MODULE_BLOBS
	bool
endmenu
config ZEPHYR_OPEN_AMP_MODULE
	bool
	default y
menu "percepio (C:/ncs/v3.0.2/modules/debug/percepio)"
osource "C:/ncs/v3.0.2/modules/debug/percepio/zephyr/Kconfig"
config ZEPHYR_PERCEPIO_MODULE
	bool
	default y
endmenu
menu "picolibc (C:/ncs/v3.0.2/modules/lib/picolibc)"
osource "C:/ncs/v3.0.2/modules/lib/picolibc/zephyr/Kconfig"
config ZEPHYR_PICOLIBC_MODULE
	bool
	default y
endmenu
menu "segger (C:/ncs/v3.0.2/modules/debug/segger)"
osource "$(ZEPHYR_SEGGER_KCONFIG)"
config ZEPHYR_SEGGER_MODULE
	bool
	default y
endmenu
config ZEPHYR_TINYCRYPT_MODULE
	bool
	default y
menu "uoscore-uedhoc (C:/ncs/v3.0.2/modules/lib/uoscore-uedhoc)"
osource "$(ZEPHYR_UOSCORE_UEDHOC_KCONFIG)"
config ZEPHYR_UOSCORE_UEDHOC_MODULE
	bool
	default y
endmenu
menu "zcbor (C:/ncs/v3.0.2/modules/lib/zcbor)"
osource "$(ZEPHYR_ZCBOR_KCONFIG)"
config ZEPHYR_ZCBOR_MODULE
	bool
	default y
endmenu
menu "nrfxlib (C:/ncs/v3.0.2/nrfxlib)"
osource "C:/ncs/v3.0.2/nrfxlib/Kconfig.nrfxlib"
config ZEPHYR_NRFXLIB_MODULE
	bool
	default y
endmenu
config ZEPHYR_NRF_HW_MODELS_MODULE
	bool
	default y
menu "connectedhomeip (C:/ncs/v3.0.2/modules/lib/matter)"
osource "C:/ncs/v3.0.2/modules/lib/matter/config/nrfconnect/chip-module/Kconfig"
config ZEPHYR_CONNECTEDHOMEIP_MODULE
	bool
	default y
endmenu
