"nrf":"C:/ncs/v3.0.2/nrf":"C:/ncs/v3.0.2/nrf"
"hostap":"C:/ncs/v3.0.2/modules/lib/hostap":"${ZEPHYR_HOSTAP_CMAKE_DIR}"
"mcuboot":"C:/ncs/v3.0.2/bootloader/mcuboot":"${ZEPHYR_MCUBOOT_CMAKE_DIR}"
"mbedtls":"C:/ncs/v3.0.2/modules/crypto/mbedtls":"${ZEPHYR_MBEDTLS_CMAKE_DIR}"
"oberon-psa-crypto":"C:/ncs/v3.0.2/modules/crypto/oberon-psa-crypto":"${ZEPHYR_OBERON_PSA_CRYPTO_CMAKE_DIR}"
"trusted-firmware-m":"C:/ncs/v3.0.2/modules/tee/tf-m/trusted-firmware-m":"${ZEPHYR_TRUSTED_FIRMWARE_M_CMAKE_DIR}"
"psa-arch-tests":"C:/ncs/v3.0.2/modules/tee/tf-m/psa-arch-tests":""
"cjson":"C:/ncs/v3.0.2/modules/lib/cjson":"${ZEPHYR_CJSON_CMAKE_DIR}"
"azure-sdk-for-c":"C:/ncs/v3.0.2/modules/lib/azure-sdk-for-c":"${ZEPHYR_AZURE_SDK_FOR_C_CMAKE_DIR}"
"cirrus-logic":"C:/ncs/v3.0.2/modules/hal/cirrus-logic":"C:/ncs/v3.0.2/modules/hal/cirrus-logic"
"openthread":"C:/ncs/v3.0.2/modules/lib/openthread":"${ZEPHYR_OPENTHREAD_CMAKE_DIR}"
"suit-generator":"C:/ncs/v3.0.2/modules/lib/suit-generator":""
"suit-processor":"C:/ncs/v3.0.2/modules/lib/suit-processor":"C:/ncs/v3.0.2/modules/lib/suit-processor/ncs"
"memfault-firmware-sdk":"C:/ncs/v3.0.2/modules/lib/memfault-firmware-sdk":"C:/ncs/v3.0.2/modules/lib/memfault-firmware-sdk/ports/zephyr"
"coremark":"C:/ncs/v3.0.2/modules/benchmark/coremark":"${ZEPHYR_COREMARK_CMAKE_DIR}"
"canopennode":"C:/ncs/v3.0.2/modules/lib/canopennode":"${ZEPHYR_CANOPENNODE_CMAKE_DIR}"
"chre":"C:/ncs/v3.0.2/modules/lib/chre":"C:/ncs/v3.0.2/modules/lib/chre/platform/zephyr"
"lz4":"C:/ncs/v3.0.2/modules/lib/lz4":"${ZEPHYR_LZ4_CMAKE_DIR}"
"nanopb":"C:/ncs/v3.0.2/modules/lib/nanopb":"${ZEPHYR_NANOPB_CMAKE_DIR}"
"tf-m-tests":"C:/ncs/v3.0.2/modules/tee/tf-m/tf-m-tests":""
"zscilib":"C:/ncs/v3.0.2/modules/lib/zscilib":"C:/ncs/v3.0.2/modules/lib/zscilib"
"cmsis":"C:/ncs/v3.0.2/modules/hal/cmsis":"${ZEPHYR_CMSIS_CMAKE_DIR}"
"cmsis-dsp":"C:/ncs/v3.0.2/modules/lib/cmsis-dsp":"${ZEPHYR_CMSIS_DSP_CMAKE_DIR}"
"cmsis-nn":"C:/ncs/v3.0.2/modules/lib/cmsis-nn":"${ZEPHYR_CMSIS_NN_CMAKE_DIR}"
"fatfs":"C:/ncs/v3.0.2/modules/fs/fatfs":"${ZEPHYR_FATFS_CMAKE_DIR}"
"hal_nordic":"C:/ncs/v3.0.2/modules/hal/nordic":"${ZEPHYR_HAL_NORDIC_CMAKE_DIR}"
"hal_st":"C:/ncs/v3.0.2/modules/hal/st":"C:/ncs/v3.0.2/modules/hal/st"
"hal_tdk":"C:/ncs/v3.0.2/modules/hal/tdk":"C:/ncs/v3.0.2/modules/hal/tdk"
"hal_wurthelektronik":"C:/ncs/v3.0.2/modules/hal/wurthelektronik":"C:/ncs/v3.0.2/modules/hal/wurthelektronik"
"liblc3":"C:/ncs/v3.0.2/modules/lib/liblc3":"${ZEPHYR_LIBLC3_CMAKE_DIR}"
"libmetal":"C:/ncs/v3.0.2/modules/hal/libmetal":"C:/ncs/v3.0.2/modules/hal/libmetal"
"littlefs":"C:/ncs/v3.0.2/modules/fs/littlefs":"${ZEPHYR_LITTLEFS_CMAKE_DIR}"
"loramac-node":"C:/ncs/v3.0.2/modules/lib/loramac-node":"${ZEPHYR_LORAMAC_NODE_CMAKE_DIR}"
"lvgl":"C:/ncs/v3.0.2/modules/lib/gui/lvgl":"${ZEPHYR_LVGL_CMAKE_DIR}"
"mipi-sys-t":"C:/ncs/v3.0.2/modules/debug/mipi-sys-t":"C:/ncs/v3.0.2/modules/debug/mipi-sys-t"
"nrf_wifi":"C:/ncs/v3.0.2/modules/lib/nrf_wifi":"${ZEPHYR_NRF_WIFI_CMAKE_DIR}"
"open-amp":"C:/ncs/v3.0.2/modules/lib/open-amp":"C:/ncs/v3.0.2/modules/lib/open-amp"
"percepio":"C:/ncs/v3.0.2/modules/debug/percepio":"${ZEPHYR_PERCEPIO_CMAKE_DIR}"
"picolibc":"C:/ncs/v3.0.2/modules/lib/picolibc":"C:/ncs/v3.0.2/modules/lib/picolibc"
"segger":"C:/ncs/v3.0.2/modules/debug/segger":"${ZEPHYR_SEGGER_CMAKE_DIR}"
"tinycrypt":"C:/ncs/v3.0.2/modules/crypto/tinycrypt":"C:/ncs/v3.0.2/modules/crypto/tinycrypt"
"uoscore-uedhoc":"C:/ncs/v3.0.2/modules/lib/uoscore-uedhoc":"${ZEPHYR_UOSCORE_UEDHOC_CMAKE_DIR}"
"zcbor":"C:/ncs/v3.0.2/modules/lib/zcbor":"${ZEPHYR_ZCBOR_CMAKE_DIR}"
"nrfxlib":"C:/ncs/v3.0.2/nrfxlib":"${ZEPHYR_NRFXLIB_CMAKE_DIR}"
"nrf_hw_models":"C:/ncs/v3.0.2/modules/bsim_hw_models/nrf_hw_models":"C:/ncs/v3.0.2/modules/bsim_hw_models/nrf_hw_models"
"connectedhomeip":"C:/ncs/v3.0.2/modules/lib/matter":"C:/ncs/v3.0.2/modules/lib/matter/config/nrfconnect/chip-module"
