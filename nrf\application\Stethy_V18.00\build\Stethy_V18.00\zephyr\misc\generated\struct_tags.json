{"__net_socket": ["modem_socket", "net_context", "dispatcher_context", "spair", "net_mgmt_socket", "tls_context", "websocket_context"], "__subsystem": ["gpio_driver_api", "i2s_driver_api", "shared_irq_driver_api", "crypto_driver_api", "adc_driver_api", "auxdisplay_driver_api", "bbram_driver_api", "bt_hci_driver_api", "can_driver_api", "cellular_driver_api", "charger_driver_api", "clock_control_driver_api", "comparator_driver_api", "coredump_driver_api", "counter_driver_api", "dac_driver_api", "dai_driver_api", "display_driver_api", "dma_driver_api", "edac_driver_api", "eeprom_driver_api", "emul_bbram_driver_api", "fuel_gauge_emul_driver_api", "emul_sensor_driver_api", "entropy_driver_api", "espi_driver_api", "espi_saf_driver_api", "flash_driver_api", "fpga_driver_api", "fuel_gauge_driver_api", "gnss_driver_api", "haptics_driver_api", "hwspinlock_driver_api", "i2c_driver_api", "i2c_target_driver_api", "i3c_driver_api", "ipm_driver_api", "kscan_driver_api", "led_driver_api", "led_strip_driver_api", "lora_driver_api", "mbox_driver_api", "mdio_driver_api", "mipi_dbi_driver_api", "mipi_dsi_driver_api", "mspi_driver_api", "peci_driver_api", "ps2_driver_api", "ptp_clock_driver_api", "pwm_driver_api", "regulator_parent_driver_api", "regulator_driver_api", "reset_driver_api", "retained_mem_driver_api", "rtc_driver_api", "sdhc_driver_api", "sensor_driver_api", "smbus_driver_api", "spi_driver_api", "stepper_driver_api", "syscon_driver_api", "tee_driver_api", "video_driver_api", "w1_driver_api", "wdt_driver_api", "can_transceiver_driver_api", "nrf_clock_control_driver_api", "i3c_target_driver_api", "its_driver_api", "vtd_driver_api", "tgpio_driver_api", "pcie_ctrl_driver_api", "pcie_ep_driver_api", "svc_driver_api", "uart_driver_api", "bc12_emul_driver_api", "bc12_driver_api", "usbc_ppc_driver_api", "tcpc_driver_api", "usbc_vbus_driver_api", "ivshmem_driver_api", "ethphy_driver_api"]}