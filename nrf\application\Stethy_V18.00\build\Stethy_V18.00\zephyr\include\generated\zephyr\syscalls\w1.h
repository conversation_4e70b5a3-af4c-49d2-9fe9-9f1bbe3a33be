/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_W1_H
#define Z_INCLUDE_SYSCALLS_W1_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_w1_change_bus_lock(const struct device * dev, bool lock);

__pinned_func
static inline int w1_change_bus_lock(const struct device * dev, bool lock)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; bool val; } parm1 = { .val = lock };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_W1_CHANGE_BUS_LOCK);
	}
#endif
	compiler_barrier();
	return z_impl_w1_change_bus_lock(dev, lock);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define w1_change_bus_lock(dev, lock) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_W1_CHANGE_BUS_LOCK, w1_change_bus_lock, dev, lock); 	syscall__retval = w1_change_bus_lock(dev, lock); 	sys_port_trace_syscall_exit(K_SYSCALL_W1_CHANGE_BUS_LOCK, w1_change_bus_lock, dev, lock, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_w1_reset_bus(const struct device * dev);

__pinned_func
static inline int w1_reset_bus(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_W1_RESET_BUS);
	}
#endif
	compiler_barrier();
	return z_impl_w1_reset_bus(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define w1_reset_bus(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_W1_RESET_BUS, w1_reset_bus, dev); 	syscall__retval = w1_reset_bus(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_W1_RESET_BUS, w1_reset_bus, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_w1_read_bit(const struct device * dev);

__pinned_func
static inline int w1_read_bit(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_W1_READ_BIT);
	}
#endif
	compiler_barrier();
	return z_impl_w1_read_bit(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define w1_read_bit(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_W1_READ_BIT, w1_read_bit, dev); 	syscall__retval = w1_read_bit(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_W1_READ_BIT, w1_read_bit, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_w1_write_bit(const struct device * dev, const bool bit);

__pinned_func
static inline int w1_write_bit(const struct device * dev, const bool bit)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; const bool val; } parm1 = { .val = bit };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_W1_WRITE_BIT);
	}
#endif
	compiler_barrier();
	return z_impl_w1_write_bit(dev, bit);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define w1_write_bit(dev, bit) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_W1_WRITE_BIT, w1_write_bit, dev, bit); 	syscall__retval = w1_write_bit(dev, bit); 	sys_port_trace_syscall_exit(K_SYSCALL_W1_WRITE_BIT, w1_write_bit, dev, bit, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_w1_read_byte(const struct device * dev);

__pinned_func
static inline int w1_read_byte(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_W1_READ_BYTE);
	}
#endif
	compiler_barrier();
	return z_impl_w1_read_byte(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define w1_read_byte(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_W1_READ_BYTE, w1_read_byte, dev); 	syscall__retval = w1_read_byte(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_W1_READ_BYTE, w1_read_byte, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_w1_write_byte(const struct device * dev, uint8_t byte);

__pinned_func
static inline int w1_write_byte(const struct device * dev, uint8_t byte)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint8_t val; } parm1 = { .val = byte };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_W1_WRITE_BYTE);
	}
#endif
	compiler_barrier();
	return z_impl_w1_write_byte(dev, byte);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define w1_write_byte(dev, byte) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_W1_WRITE_BYTE, w1_write_byte, dev, byte); 	syscall__retval = w1_write_byte(dev, byte); 	sys_port_trace_syscall_exit(K_SYSCALL_W1_WRITE_BYTE, w1_write_byte, dev, byte, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_w1_read_block(const struct device * dev, uint8_t * buffer, size_t len);

__pinned_func
static inline int w1_read_block(const struct device * dev, uint8_t * buffer, size_t len)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint8_t * val; } parm1 = { .val = buffer };
		union { uintptr_t x; size_t val; } parm2 = { .val = len };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_W1_READ_BLOCK);
	}
#endif
	compiler_barrier();
	return z_impl_w1_read_block(dev, buffer, len);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define w1_read_block(dev, buffer, len) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_W1_READ_BLOCK, w1_read_block, dev, buffer, len); 	syscall__retval = w1_read_block(dev, buffer, len); 	sys_port_trace_syscall_exit(K_SYSCALL_W1_READ_BLOCK, w1_read_block, dev, buffer, len, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_w1_write_block(const struct device * dev, const uint8_t * buffer, size_t len);

__pinned_func
static inline int w1_write_block(const struct device * dev, const uint8_t * buffer, size_t len)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; const uint8_t * val; } parm1 = { .val = buffer };
		union { uintptr_t x; size_t val; } parm2 = { .val = len };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_W1_WRITE_BLOCK);
	}
#endif
	compiler_barrier();
	return z_impl_w1_write_block(dev, buffer, len);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define w1_write_block(dev, buffer, len) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_W1_WRITE_BLOCK, w1_write_block, dev, buffer, len); 	syscall__retval = w1_write_block(dev, buffer, len); 	sys_port_trace_syscall_exit(K_SYSCALL_W1_WRITE_BLOCK, w1_write_block, dev, buffer, len, syscall__retval); 	syscall__retval; })
#endif
#endif


extern size_t z_impl_w1_get_slave_count(const struct device * dev);

__pinned_func
static inline size_t w1_get_slave_count(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (size_t) arch_syscall_invoke1(parm0.x, K_SYSCALL_W1_GET_SLAVE_COUNT);
	}
#endif
	compiler_barrier();
	return z_impl_w1_get_slave_count(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define w1_get_slave_count(dev) ({ 	size_t syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_W1_GET_SLAVE_COUNT, w1_get_slave_count, dev); 	syscall__retval = w1_get_slave_count(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_W1_GET_SLAVE_COUNT, w1_get_slave_count, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_w1_configure(const struct device * dev, enum w1_settings_type type, uint32_t value);

__pinned_func
static inline int w1_configure(const struct device * dev, enum w1_settings_type type, uint32_t value)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; enum w1_settings_type val; } parm1 = { .val = type };
		union { uintptr_t x; uint32_t val; } parm2 = { .val = value };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_W1_CONFIGURE);
	}
#endif
	compiler_barrier();
	return z_impl_w1_configure(dev, type, value);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define w1_configure(dev, type, value) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_W1_CONFIGURE, w1_configure, dev, type, value); 	syscall__retval = w1_configure(dev, type, value); 	sys_port_trace_syscall_exit(K_SYSCALL_W1_CONFIGURE, w1_configure, dev, type, value, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_w1_search_bus(const struct device * dev, uint8_t command, uint8_t family, w1_search_callback_t callback, void * user_data);

__pinned_func
static inline int w1_search_bus(const struct device * dev, uint8_t command, uint8_t family, w1_search_callback_t callback, void * user_data)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint8_t val; } parm1 = { .val = command };
		union { uintptr_t x; uint8_t val; } parm2 = { .val = family };
		union { uintptr_t x; w1_search_callback_t val; } parm3 = { .val = callback };
		union { uintptr_t x; void * val; } parm4 = { .val = user_data };
		return (int) arch_syscall_invoke5(parm0.x, parm1.x, parm2.x, parm3.x, parm4.x, K_SYSCALL_W1_SEARCH_BUS);
	}
#endif
	compiler_barrier();
	return z_impl_w1_search_bus(dev, command, family, callback, user_data);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define w1_search_bus(dev, command, family, callback, user_data) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_W1_SEARCH_BUS, w1_search_bus, dev, command, family, callback, user_data); 	syscall__retval = w1_search_bus(dev, command, family, callback, user_data); 	sys_port_trace_syscall_exit(K_SYSCALL_W1_SEARCH_BUS, w1_search_bus, dev, command, family, callback, user_data, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
