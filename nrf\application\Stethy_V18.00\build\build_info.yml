cmake:
  application:
    source-dir: C:/ncs/v3.0.2/zephyr/share/sysbuild
  board:
    name: nrf5340dk
    path:
     - C:/ncs/v3.0.2/zephyr/boards/nordic/nrf5340dk
    qualifiers: nrf5340/cpuapp
    revision: 
  images:
   - name: Stethy_V18.00
     source-dir: C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00
     type: MAIN
  kconfig:
    files:
     - C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/empty.conf
     - C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/empty.conf
    user-files:
     - C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/empty.conf
  sysbuild: true
version: 0.1.0
west:
  command: C:\ncs\toolchains\0b393f9e1b\opt\bin\Scripts\west build --build-dir c:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build c:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00 --pristine --board nrf5340dk/nrf5340/cpuapp -- -DCONF_FILE=prj.conf -DDTC_OVERLAY_FILE=boards/nrf5340dk_nrf5340_cpuapp.overlay
  topdir: c:\ncs\v3.0.2
  version: 1.2.0
