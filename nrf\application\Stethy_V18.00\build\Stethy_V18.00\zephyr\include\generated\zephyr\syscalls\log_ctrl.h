/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_LOG_CTRL_H
#define Z_INCLUDE_SYSCALLS_LOG_CTRL_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern void z_impl_log_panic(void);

__pinned_func
static inline void log_panic(void)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		(void) arch_syscall_invoke0(K_SYSCALL_LOG_PANIC);
		return;
	}
#endif
	compiler_barrier();
	z_impl_log_panic();
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define log_panic() do { 	sys_port_trace_syscall_enter(K_SYSCALL_LOG_PANIC, log_panic); 	log_panic(); 	sys_port_trace_syscall_exit(K_SYSCALL_LOG_PANIC, log_panic); } while(false)
#endif
#endif


extern bool z_impl_log_process(void);

__pinned_func
static inline bool log_process(void)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		return (bool) arch_syscall_invoke0(K_SYSCALL_LOG_PROCESS);
	}
#endif
	compiler_barrier();
	return z_impl_log_process();
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define log_process() ({ 	bool syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_LOG_PROCESS, log_process); 	syscall__retval = log_process(); 	sys_port_trace_syscall_exit(K_SYSCALL_LOG_PROCESS, log_process, syscall__retval); 	syscall__retval; })
#endif
#endif


extern uint32_t z_impl_log_buffered_cnt(void);

__pinned_func
static inline uint32_t log_buffered_cnt(void)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		return (uint32_t) arch_syscall_invoke0(K_SYSCALL_LOG_BUFFERED_CNT);
	}
#endif
	compiler_barrier();
	return z_impl_log_buffered_cnt();
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define log_buffered_cnt() ({ 	uint32_t syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_LOG_BUFFERED_CNT, log_buffered_cnt); 	syscall__retval = log_buffered_cnt(); 	sys_port_trace_syscall_exit(K_SYSCALL_LOG_BUFFERED_CNT, log_buffered_cnt, syscall__retval); 	syscall__retval; })
#endif
#endif


extern uint32_t z_impl_log_filter_set(struct log_backend const *const backend, uint32_t domain_id, int16_t source_id, uint32_t level);

__pinned_func
static inline uint32_t log_filter_set(struct log_backend const *const backend, uint32_t domain_id, int16_t source_id, uint32_t level)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct log_backend const *const val; } parm0 = { .val = backend };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = domain_id };
		union { uintptr_t x; int16_t val; } parm2 = { .val = source_id };
		union { uintptr_t x; uint32_t val; } parm3 = { .val = level };
		return (uint32_t) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_LOG_FILTER_SET);
	}
#endif
	compiler_barrier();
	return z_impl_log_filter_set(backend, domain_id, source_id, level);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define log_filter_set(backend, domain_id, source_id, level) ({ 	uint32_t syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_LOG_FILTER_SET, log_filter_set, backend, domain_id, source_id, level); 	syscall__retval = log_filter_set(backend, domain_id, source_id, level); 	sys_port_trace_syscall_exit(K_SYSCALL_LOG_FILTER_SET, log_filter_set, backend, domain_id, source_id, level, syscall__retval); 	syscall__retval; })
#endif
#endif


extern uint32_t z_impl_log_frontend_filter_set(int16_t source_id, uint32_t level);

__pinned_func
static inline uint32_t log_frontend_filter_set(int16_t source_id, uint32_t level)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; int16_t val; } parm0 = { .val = source_id };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = level };
		return (uint32_t) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_LOG_FRONTEND_FILTER_SET);
	}
#endif
	compiler_barrier();
	return z_impl_log_frontend_filter_set(source_id, level);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define log_frontend_filter_set(source_id, level) ({ 	uint32_t syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_LOG_FRONTEND_FILTER_SET, log_frontend_filter_set, source_id, level); 	syscall__retval = log_frontend_filter_set(source_id, level); 	sys_port_trace_syscall_exit(K_SYSCALL_LOG_FRONTEND_FILTER_SET, log_frontend_filter_set, source_id, level, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
