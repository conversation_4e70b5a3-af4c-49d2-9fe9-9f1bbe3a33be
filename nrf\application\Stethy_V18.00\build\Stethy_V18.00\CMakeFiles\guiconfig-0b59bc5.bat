@echo off
cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\Stethy_V18.00\zephyr\kconfig || (set FAIL_LINE=2& goto :ABORT)
C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E env ZEPHYR_BASE=C:/ncs/v3.0.2/zephyr PYTHON_EXECUTABLE=C:/ncs/toolchains/0b393f9e1b/opt/bin/python.exe srctree=C:/ncs/v3.0.2/zephyr KERNELVERSION=0x4006300 APPVERSION= APP_VERSION_EXTENDED_STRING= APP_VERSION_TWEAK_STRING= CONFIG_=CONFIG_ KCONFIG_CONFIG=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/.config KCONFIG_BOARD_DIR=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/Kconfig/boards BOARD=nrf5340dk BOARD_REVISION= BOARD_QUALIFIERS=/nrf5340/cpuapp HWM_SCHEME=v2 KCONFIG_BINARY_DIR=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/Kconfig APPLICATION_SOURCE_DIR=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00 ZEPHYR_TOOLCHAIN_VARIANT=zephyr TOOLCHAIN_KCONFIG_DIR=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/cmake/zephyr TOOLCHAIN_HAS_NEWLIB=y TOOLCHAIN_HAS_PICOLIBC=y EDT_PICKLE=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/edt.pickle NCS_MEMFAULT_FIRMWARE_SDK_KCONFIG=C:/ncs/v3.0.2/nrf/modules/memfault-firmware-sdk/Kconfig ZEPHYR_NRF_MODULE_DIR=C:/ncs/v3.0.2/nrf ZEPHYR_HOSTAP_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/hostap ZEPHYR_HOSTAP_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/hostap/Kconfig ZEPHYR_MCUBOOT_MODULE_DIR=C:/ncs/v3.0.2/bootloader/mcuboot ZEPHYR_MCUBOOT_KCONFIG=C:/ncs/v3.0.2/nrf/modules/mcuboot/Kconfig ZEPHYR_MBEDTLS_MODULE_DIR=C:/ncs/v3.0.2/modules/crypto/mbedtls ZEPHYR_MBEDTLS_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/mbedtls/Kconfig ZEPHYR_OBERON_PSA_CRYPTO_MODULE_DIR=C:/ncs/v3.0.2/modules/crypto/oberon-psa-crypto ZEPHYR_TRUSTED_FIRMWARE_M_MODULE_DIR=C:/ncs/v3.0.2/modules/tee/tf-m/trusted-firmware-m ZEPHYR_TRUSTED_FIRMWARE_M_KCONFIG=C:/ncs/v3.0.2/nrf/modules/trusted-firmware-m/Kconfig ZEPHYR_PSA_ARCH_TESTS_MODULE_DIR=C:/ncs/v3.0.2/modules/tee/tf-m/psa-arch-tests ZEPHYR_CJSON_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/cjson ZEPHYR_CJSON_KCONFIG=C:/ncs/v3.0.2/nrf/modules/cjson/Kconfig ZEPHYR_AZURE_SDK_FOR_C_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/azure-sdk-for-c ZEPHYR_AZURE_SDK_FOR_C_KCONFIG=C:/ncs/v3.0.2/nrf/modules/azure-sdk-for-c/Kconfig ZEPHYR_CIRRUS_LOGIC_MODULE_DIR=C:/ncs/v3.0.2/modules/hal/cirrus-logic ZEPHYR_OPENTHREAD_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/openthread ZEPHYR_OPENTHREAD_KCONFIG=C:/ncs/v3.0.2/nrf/modules/openthread/Kconfig ZEPHYR_SUIT_GENERATOR_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/suit-generator ZEPHYR_SUIT_PROCESSOR_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/suit-processor ZEPHYR_MEMFAULT_FIRMWARE_SDK_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/memfault-firmware-sdk ZEPHYR_COREMARK_MODULE_DIR=C:/ncs/v3.0.2/modules/benchmark/coremark ZEPHYR_COREMARK_KCONFIG=C:/ncs/v3.0.2/nrf/modules/coremark/Kconfig ZEPHYR_CANOPENNODE_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/canopennode ZEPHYR_CANOPENNODE_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/canopennode/Kconfig ZEPHYR_CHRE_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/chre ZEPHYR_LZ4_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/lz4 ZEPHYR_LZ4_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/lz4/Kconfig ZEPHYR_NANOPB_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/nanopb ZEPHYR_NANOPB_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/nanopb/Kconfig ZEPHYR_TF_M_TESTS_MODULE_DIR=C:/ncs/v3.0.2/modules/tee/tf-m/tf-m-tests ZEPHYR_ZSCILIB_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/zscilib ZEPHYR_CMSIS_MODULE_DIR=C:/ncs/v3.0.2/modules/hal/cmsis ZEPHYR_CMSIS_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/cmsis/Kconfig ZEPHYR_CMSIS_DSP_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/cmsis-dsp ZEPHYR_CMSIS_DSP_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/cmsis-dsp/Kconfig ZEPHYR_CMSIS_NN_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/cmsis-nn ZEPHYR_CMSIS_NN_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/cmsis-nn/Kconfig ZEPHYR_FATFS_MODULE_DIR=C:/ncs/v3.0.2/modules/fs/fatfs ZEPHYR_FATFS_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/fatfs/Kconfig ZEPHYR_HAL_NORDIC_MODULE_DIR=C:/ncs/v3.0.2/modules/hal/nordic ZEPHYR_HAL_NORDIC_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/hal_nordic/Kconfig ZEPHYR_HAL_ST_MODULE_DIR=C:/ncs/v3.0.2/modules/hal/st ZEPHYR_HAL_ST_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/hal_st/Kconfig ZEPHYR_HAL_TDK_MODULE_DIR=C:/ncs/v3.0.2/modules/hal/tdk ZEPHYR_HAL_TDK_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/hal_tdk/Kconfig ZEPHYR_HAL_WURTHELEKTRONIK_MODULE_DIR=C:/ncs/v3.0.2/modules/hal/wurthelektronik ZEPHYR_LIBLC3_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/liblc3 ZEPHYR_LIBLC3_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/liblc3/Kconfig ZEPHYR_LIBMETAL_MODULE_DIR=C:/ncs/v3.0.2/modules/hal/libmetal ZEPHYR_LITTLEFS_MODULE_DIR=C:/ncs/v3.0.2/modules/fs/littlefs ZEPHYR_LITTLEFS_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/littlefs/Kconfig ZEPHYR_LORAMAC_NODE_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/loramac-node ZEPHYR_LORAMAC_NODE_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/loramac-node/Kconfig ZEPHYR_LVGL_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/gui/lvgl ZEPHYR_LVGL_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/lvgl/Kconfig ZEPHYR_MIPI_SYS_T_MODULE_DIR=C:/ncs/v3.0.2/modules/debug/mipi-sys-t ZEPHYR_NRF_WIFI_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/nrf_wifi ZEPHYR_NRF_WIFI_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/nrf_wifi/Kconfig ZEPHYR_OPEN_AMP_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/open-amp ZEPHYR_PERCEPIO_MODULE_DIR=C:/ncs/v3.0.2/modules/debug/percepio ZEPHYR_PERCEPIO_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/percepio/Kconfig ZEPHYR_PICOLIBC_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/picolibc ZEPHYR_SEGGER_MODULE_DIR=C:/ncs/v3.0.2/modules/debug/segger ZEPHYR_SEGGER_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/segger/Kconfig ZEPHYR_TINYCRYPT_MODULE_DIR=C:/ncs/v3.0.2/modules/crypto/tinycrypt ZEPHYR_UOSCORE_UEDHOC_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/uoscore-uedhoc ZEPHYR_UOSCORE_UEDHOC_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/uoscore-uedhoc/Kconfig ZEPHYR_ZCBOR_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/zcbor ZEPHYR_ZCBOR_KCONFIG=C:/ncs/v3.0.2/zephyr/modules/zcbor/Kconfig ZEPHYR_NRFXLIB_MODULE_DIR=C:/ncs/v3.0.2/nrfxlib ZEPHYR_NRF_HW_MODELS_MODULE_DIR=C:/ncs/v3.0.2/modules/bsim_hw_models/nrf_hw_models ZEPHYR_CONNECTEDHOMEIP_MODULE_DIR=C:/ncs/v3.0.2/modules/lib/matter ARCH=* ARCH_DIR=C:/ncs/v3.0.2/zephyr/arch SHIELD_AS_LIST= DTS_POST_CPP=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/zephyr.dts.pre DTS_ROOT_BINDINGS=C:/ncs/v3.0.2/nrf/dts/bindings?C:/ncs/v3.0.2/zephyr/dts/bindings C:/ncs/toolchains/0b393f9e1b/opt/bin/python.exe C:/ncs/v3.0.2/zephyr/scripts/kconfig/guiconfig.py C:/ncs/v3.0.2/zephyr/Kconfig || (set FAIL_LINE=3& goto :ABORT)
goto :EOF

:ABORT
set ERROR_CODE=%ERRORLEVEL%
echo Batch file failed at line %FAIL_LINE% with errorcode %ERRORLEVEL%
exit /b %ERROR_CODE%