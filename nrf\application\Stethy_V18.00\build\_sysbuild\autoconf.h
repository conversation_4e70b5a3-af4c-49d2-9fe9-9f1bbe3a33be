#define SB_CONFIG_BOARD "nrf5340dk"
#define SB_CONFIG_BOARD_REVISION ""
#define SB_CONFIG_BOARD_NRF5340DK 1
#define SB_CONFIG_BOARD_NRF5340DK_NRF5340_CPUAPP 1
#define SB_CONFIG_BOARD_QUALIFIERS "nrf5340/cpuapp"
#define SB_CONFIG_SOC "nrf5340"
#define SB_CONFIG_SOC_SERIES "nrf53"
#define SB_CONFIG_SOC_FAMILY "nordic_nrf"
#define SB_CONFIG_SOC_TOOLCHAIN_NAME "amd_acp_6_0_adsp"
#define SB_CONFIG_SOC_FAMILY_NORDIC_NRF 1
#define SB_CONFIG_SOC_SERIES_NRF53X 1
#define SB_CONFIG_SOC_NRF5340_CPUAPP 1
#define SB_CONFIG_SOC_NRF5340_CPUAPP_QKAA 1
#define SB_CONFIG_PARTITION_MANAGER 1
#define SB_CONFIG_BUILD_OUTPUT_BIN 1
#define SB_CONFIG_BUILD_OUTPUT_HEX 1
#define SB_CONFIG_APPCORE_REMOTE_BOARD_TARGET_CPUCLUSTER "cpuapp"
#define SB_CONFIG_APPCORE_REMOTE_DOMAIN "CPUAPP"
#define SB_CONFIG_SUPPORT_NETCORE 1
#define SB_CONFIG_NETCORE_REMOTE_BOARD_TARGET_CPUCLUSTER "cpunet"
#define SB_CONFIG_NETCORE_REMOTE_DOMAIN "CPUNET"
#define SB_CONFIG_SUPPORT_NETCORE_EMPTY 1
#define SB_CONFIG_SUPPORT_NETCORE_HCI_IPC 1
#define SB_CONFIG_SUPPORT_NETCORE_RPC_HOST 1
#define SB_CONFIG_SUPPORT_NETCORE_802154_RPMSG 1
#define SB_CONFIG_SUPPORT_NETCORE_IPC_RADIO 1
#define SB_CONFIG_NETCORE_NONE 1
#define SB_CONFIG_SUPPORT_QSPI_XIP 1
#define SB_CONFIG_BT_FAST_PAIR_MODEL_ID 0x1000000
#define SB_CONFIG_BT_FAST_PAIR_ANTI_SPOOFING_PRIVATE_KEY ""
#define SB_CONFIG_APPROTECT_NO_SYSBUILD 1
#define SB_CONFIG_SECURE_APPROTECT_NO_SYSBUILD 1
#define SB_CONFIG_ZEPHYR_NRF_MODULE 1
#define SB_CONFIG_ZEPHYR_HOSTAP_MODULE 1
#define SB_CONFIG_ZEPHYR_MCUBOOT_MODULE 1
#define SB_CONFIG_ZEPHYR_MBEDTLS_MODULE 1
#define SB_CONFIG_ZEPHYR_OBERON_PSA_CRYPTO_MODULE 1
#define SB_CONFIG_ZEPHYR_TRUSTED_FIRMWARE_M_MODULE 1
#define SB_CONFIG_ZEPHYR_PSA_ARCH_TESTS_MODULE 1
#define SB_CONFIG_ZEPHYR_CJSON_MODULE 1
#define SB_CONFIG_ZEPHYR_AZURE_SDK_FOR_C_MODULE 1
#define SB_CONFIG_ZEPHYR_CIRRUS_LOGIC_MODULE 1
#define SB_CONFIG_ZEPHYR_OPENTHREAD_MODULE 1
#define SB_CONFIG_ZEPHYR_SUIT_GENERATOR_MODULE 1
#define SB_CONFIG_ZEPHYR_SUIT_PROCESSOR_MODULE 1
#define SB_CONFIG_ZEPHYR_MEMFAULT_FIRMWARE_SDK_MODULE 1
#define SB_CONFIG_ZEPHYR_COREMARK_MODULE 1
#define SB_CONFIG_ZEPHYR_CANOPENNODE_MODULE 1
#define SB_CONFIG_ZEPHYR_CHRE_MODULE 1
#define SB_CONFIG_ZEPHYR_LZ4_MODULE 1
#define SB_CONFIG_ZEPHYR_NANOPB_MODULE 1
#define SB_CONFIG_ZEPHYR_TF_M_TESTS_MODULE 1
#define SB_CONFIG_ZEPHYR_ZSCILIB_MODULE 1
#define SB_CONFIG_ZEPHYR_CMSIS_MODULE 1
#define SB_CONFIG_ZEPHYR_CMSIS_DSP_MODULE 1
#define SB_CONFIG_ZEPHYR_CMSIS_NN_MODULE 1
#define SB_CONFIG_ZEPHYR_FATFS_MODULE 1
#define SB_CONFIG_ZEPHYR_HAL_NORDIC_MODULE 1
#define SB_CONFIG_ZEPHYR_HAL_ST_MODULE 1
#define SB_CONFIG_ZEPHYR_HAL_TDK_MODULE 1
#define SB_CONFIG_ZEPHYR_HAL_WURTHELEKTRONIK_MODULE 1
#define SB_CONFIG_ZEPHYR_LIBLC3_MODULE 1
#define SB_CONFIG_ZEPHYR_LIBMETAL_MODULE 1
#define SB_CONFIG_ZEPHYR_LITTLEFS_MODULE 1
#define SB_CONFIG_ZEPHYR_LORAMAC_NODE_MODULE 1
#define SB_CONFIG_ZEPHYR_LVGL_MODULE 1
#define SB_CONFIG_ZEPHYR_MIPI_SYS_T_MODULE 1
#define SB_CONFIG_ZEPHYR_NRF_WIFI_MODULE 1
#define SB_CONFIG_ZEPHYR_OPEN_AMP_MODULE 1
#define SB_CONFIG_ZEPHYR_PERCEPIO_MODULE 1
#define SB_CONFIG_ZEPHYR_PICOLIBC_MODULE 1
#define SB_CONFIG_ZEPHYR_SEGGER_MODULE 1
#define SB_CONFIG_ZEPHYR_TINYCRYPT_MODULE 1
#define SB_CONFIG_ZEPHYR_UOSCORE_UEDHOC_MODULE 1
#define SB_CONFIG_ZEPHYR_ZCBOR_MODULE 1
#define SB_CONFIG_ZEPHYR_NRFXLIB_MODULE 1
#define SB_CONFIG_ZEPHYR_NRF_HW_MODELS_MODULE 1
#define SB_CONFIG_ZEPHYR_CONNECTEDHOMEIP_MODULE 1
#define SB_CONFIG_WARN_DEPRECATED 1
#define SB_CONFIG_SUPPORT_BOOTLOADER 1
#define SB_CONFIG_SUPPORT_BOOTLOADER_MCUBOOT_ZEPHYR 1
#define SB_CONFIG_BOOTLOADER_NONE 1
