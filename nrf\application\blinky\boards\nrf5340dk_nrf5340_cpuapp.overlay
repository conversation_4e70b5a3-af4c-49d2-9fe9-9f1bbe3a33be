#include <zephyr/dt-bindings/led/led.h>

&pinctrl {
    i2s0_default_alt: i2s0_default_alt {
		group1 {
			psels = <NRF_PSEL(I2S_SCK_M, 1, 15)>,
                   <NRF_PSEL(I2S_LRCK_M, 1, 12)>,
                   <NRF_PSEL(I2S_SDOUT, 1, 13)>,
                   <NRF_PSEL(I2S_SDIN, 1, 14)>;
		};
	};
};

i2s_led: &i2s0 {
    pinctrl-0 = <&i2s0_default_alt>;
    pinctrl-names = "default";

    led_strip: ws2812b@0 {
        compatible = "worldsemi,ws2812b";

        reg = <0>;
        chain-length = <10>;
        color-mapping = <LED_COLOR_ID_GREEN
                    LED_COLOR_ID_RED 
                    LED_COLOR_ID_BLUE>;
        reset-delay = <500>;
    };
};

/ {
    aliases {
        led-strip = &led_strip;
    };
};