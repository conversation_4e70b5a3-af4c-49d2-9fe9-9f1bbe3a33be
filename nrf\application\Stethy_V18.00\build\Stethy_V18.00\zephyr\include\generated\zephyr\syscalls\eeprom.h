/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_EEPROM_H
#define Z_INCLUDE_SYSCALLS_EEPROM_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_eeprom_read(const struct device * dev, off_t offset, void * data, size_t len);

__pinned_func
static inline int eeprom_read(const struct device * dev, off_t offset, void * data, size_t len)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; off_t val; } parm1 = { .val = offset };
		union { uintptr_t x; void * val; } parm2 = { .val = data };
		union { uintptr_t x; size_t val; } parm3 = { .val = len };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_EEPROM_READ);
	}
#endif
	compiler_barrier();
	return z_impl_eeprom_read(dev, offset, data, len);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define eeprom_read(dev, offset, data, len) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_EEPROM_READ, eeprom_read, dev, offset, data, len); 	syscall__retval = eeprom_read(dev, offset, data, len); 	sys_port_trace_syscall_exit(K_SYSCALL_EEPROM_READ, eeprom_read, dev, offset, data, len, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_eeprom_write(const struct device * dev, off_t offset, const void * data, size_t len);

__pinned_func
static inline int eeprom_write(const struct device * dev, off_t offset, const void * data, size_t len)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; off_t val; } parm1 = { .val = offset };
		union { uintptr_t x; const void * val; } parm2 = { .val = data };
		union { uintptr_t x; size_t val; } parm3 = { .val = len };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_EEPROM_WRITE);
	}
#endif
	compiler_barrier();
	return z_impl_eeprom_write(dev, offset, data, len);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define eeprom_write(dev, offset, data, len) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_EEPROM_WRITE, eeprom_write, dev, offset, data, len); 	syscall__retval = eeprom_write(dev, offset, data, len); 	sys_port_trace_syscall_exit(K_SYSCALL_EEPROM_WRITE, eeprom_write, dev, offset, data, len, syscall__retval); 	syscall__retval; })
#endif
#endif


extern size_t z_impl_eeprom_get_size(const struct device * dev);

__pinned_func
static inline size_t eeprom_get_size(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (size_t) arch_syscall_invoke1(parm0.x, K_SYSCALL_EEPROM_GET_SIZE);
	}
#endif
	compiler_barrier();
	return z_impl_eeprom_get_size(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define eeprom_get_size(dev) ({ 	size_t syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_EEPROM_GET_SIZE, eeprom_get_size, dev); 	syscall__retval = eeprom_get_size(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_EEPROM_GET_SIZE, eeprom_get_size, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
