/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_DEVICE_H
#define Z_INCLUDE_SYSCALLS_DEVICE_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern const struct device * z_impl_device_get_binding(const char * name);

__pinned_func
static inline const struct device * device_get_binding(const char * name)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const char * val; } parm0 = { .val = name };
		return (const struct device *) arch_syscall_invoke1(parm0.x, K_SYSCALL_DEVICE_GET_BINDING);
	}
#endif
	compiler_barrier();
	return z_impl_device_get_binding(name);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define device_get_binding(name) ({ 	const struct device * syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_DEVICE_GET_BINDING, device_get_binding, name); 	syscall__retval = device_get_binding(name); 	sys_port_trace_syscall_exit(K_SYSCALL_DEVICE_GET_BINDING, device_get_binding, name, syscall__retval); 	syscall__retval; })
#endif
#endif


extern bool z_impl_device_is_ready(const struct device * dev);

__pinned_func
static inline bool device_is_ready(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (bool) arch_syscall_invoke1(parm0.x, K_SYSCALL_DEVICE_IS_READY);
	}
#endif
	compiler_barrier();
	return z_impl_device_is_ready(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define device_is_ready(dev) ({ 	bool syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_DEVICE_IS_READY, device_is_ready, dev); 	syscall__retval = device_is_ready(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_DEVICE_IS_READY, device_is_ready, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_device_init(const struct device * dev);

__pinned_func
static inline int device_init(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_DEVICE_INIT);
	}
#endif
	compiler_barrier();
	return z_impl_device_init(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define device_init(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_DEVICE_INIT, device_init, dev); 	syscall__retval = device_init(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_DEVICE_INIT, device_init, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern const struct device * z_impl_device_get_by_dt_nodelabel(const char * nodelabel);

__pinned_func
static inline const struct device * device_get_by_dt_nodelabel(const char * nodelabel)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const char * val; } parm0 = { .val = nodelabel };
		return (const struct device *) arch_syscall_invoke1(parm0.x, K_SYSCALL_DEVICE_GET_BY_DT_NODELABEL);
	}
#endif
	compiler_barrier();
	return z_impl_device_get_by_dt_nodelabel(nodelabel);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define device_get_by_dt_nodelabel(nodelabel) ({ 	const struct device * syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_DEVICE_GET_BY_DT_NODELABEL, device_get_by_dt_nodelabel, nodelabel); 	syscall__retval = device_get_by_dt_nodelabel(nodelabel); 	sys_port_trace_syscall_exit(K_SYSCALL_DEVICE_GET_BY_DT_NODELABEL, device_get_by_dt_nodelabel, nodelabel, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
