/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_AUXDISPLAY_H
#define Z_INCLUDE_SYSCALLS_AUXDISPLAY_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_auxdisplay_display_on(const struct device * dev);

__pinned_func
static inline int auxdisplay_display_on(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_AUXDISPLAY_DISPLAY_ON);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_display_on(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_display_on(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_DISPLAY_ON, auxdisplay_display_on, dev); 	syscall__retval = auxdisplay_display_on(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_DISPLAY_ON, auxdisplay_display_on, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_display_off(const struct device * dev);

__pinned_func
static inline int auxdisplay_display_off(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_AUXDISPLAY_DISPLAY_OFF);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_display_off(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_display_off(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_DISPLAY_OFF, auxdisplay_display_off, dev); 	syscall__retval = auxdisplay_display_off(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_DISPLAY_OFF, auxdisplay_display_off, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_cursor_set_enabled(const struct device * dev, bool enabled);

__pinned_func
static inline int auxdisplay_cursor_set_enabled(const struct device * dev, bool enabled)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; bool val; } parm1 = { .val = enabled };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_AUXDISPLAY_CURSOR_SET_ENABLED);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_cursor_set_enabled(dev, enabled);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_cursor_set_enabled(dev, enabled) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_CURSOR_SET_ENABLED, auxdisplay_cursor_set_enabled, dev, enabled); 	syscall__retval = auxdisplay_cursor_set_enabled(dev, enabled); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_CURSOR_SET_ENABLED, auxdisplay_cursor_set_enabled, dev, enabled, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_position_blinking_set_enabled(const struct device * dev, bool enabled);

__pinned_func
static inline int auxdisplay_position_blinking_set_enabled(const struct device * dev, bool enabled)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; bool val; } parm1 = { .val = enabled };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_AUXDISPLAY_POSITION_BLINKING_SET_ENABLED);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_position_blinking_set_enabled(dev, enabled);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_position_blinking_set_enabled(dev, enabled) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_POSITION_BLINKING_SET_ENABLED, auxdisplay_position_blinking_set_enabled, dev, enabled); 	syscall__retval = auxdisplay_position_blinking_set_enabled(dev, enabled); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_POSITION_BLINKING_SET_ENABLED, auxdisplay_position_blinking_set_enabled, dev, enabled, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_cursor_shift_set(const struct device * dev, uint8_t direction, bool display_shift);

__pinned_func
static inline int auxdisplay_cursor_shift_set(const struct device * dev, uint8_t direction, bool display_shift)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint8_t val; } parm1 = { .val = direction };
		union { uintptr_t x; bool val; } parm2 = { .val = display_shift };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_AUXDISPLAY_CURSOR_SHIFT_SET);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_cursor_shift_set(dev, direction, display_shift);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_cursor_shift_set(dev, direction, display_shift) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_CURSOR_SHIFT_SET, auxdisplay_cursor_shift_set, dev, direction, display_shift); 	syscall__retval = auxdisplay_cursor_shift_set(dev, direction, display_shift); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_CURSOR_SHIFT_SET, auxdisplay_cursor_shift_set, dev, direction, display_shift, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_cursor_position_set(const struct device * dev, enum auxdisplay_position type, int16_t x, int16_t y);

__pinned_func
static inline int auxdisplay_cursor_position_set(const struct device * dev, enum auxdisplay_position type, int16_t x, int16_t y)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; enum auxdisplay_position val; } parm1 = { .val = type };
		union { uintptr_t x; int16_t val; } parm2 = { .val = x };
		union { uintptr_t x; int16_t val; } parm3 = { .val = y };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_AUXDISPLAY_CURSOR_POSITION_SET);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_cursor_position_set(dev, type, x, y);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_cursor_position_set(dev, type, x, y) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_CURSOR_POSITION_SET, auxdisplay_cursor_position_set, dev, type, x, y); 	syscall__retval = auxdisplay_cursor_position_set(dev, type, x, y); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_CURSOR_POSITION_SET, auxdisplay_cursor_position_set, dev, type, x, y, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_cursor_position_get(const struct device * dev, int16_t * x, int16_t * y);

__pinned_func
static inline int auxdisplay_cursor_position_get(const struct device * dev, int16_t * x, int16_t * y)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; int16_t * val; } parm1 = { .val = x };
		union { uintptr_t x; int16_t * val; } parm2 = { .val = y };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_AUXDISPLAY_CURSOR_POSITION_GET);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_cursor_position_get(dev, x, y);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_cursor_position_get(dev, x, y) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_CURSOR_POSITION_GET, auxdisplay_cursor_position_get, dev, x, y); 	syscall__retval = auxdisplay_cursor_position_get(dev, x, y); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_CURSOR_POSITION_GET, auxdisplay_cursor_position_get, dev, x, y, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_display_position_set(const struct device * dev, enum auxdisplay_position type, int16_t x, int16_t y);

__pinned_func
static inline int auxdisplay_display_position_set(const struct device * dev, enum auxdisplay_position type, int16_t x, int16_t y)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; enum auxdisplay_position val; } parm1 = { .val = type };
		union { uintptr_t x; int16_t val; } parm2 = { .val = x };
		union { uintptr_t x; int16_t val; } parm3 = { .val = y };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_AUXDISPLAY_DISPLAY_POSITION_SET);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_display_position_set(dev, type, x, y);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_display_position_set(dev, type, x, y) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_DISPLAY_POSITION_SET, auxdisplay_display_position_set, dev, type, x, y); 	syscall__retval = auxdisplay_display_position_set(dev, type, x, y); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_DISPLAY_POSITION_SET, auxdisplay_display_position_set, dev, type, x, y, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_display_position_get(const struct device * dev, int16_t * x, int16_t * y);

__pinned_func
static inline int auxdisplay_display_position_get(const struct device * dev, int16_t * x, int16_t * y)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; int16_t * val; } parm1 = { .val = x };
		union { uintptr_t x; int16_t * val; } parm2 = { .val = y };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_AUXDISPLAY_DISPLAY_POSITION_GET);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_display_position_get(dev, x, y);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_display_position_get(dev, x, y) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_DISPLAY_POSITION_GET, auxdisplay_display_position_get, dev, x, y); 	syscall__retval = auxdisplay_display_position_get(dev, x, y); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_DISPLAY_POSITION_GET, auxdisplay_display_position_get, dev, x, y, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_capabilities_get(const struct device * dev, struct auxdisplay_capabilities * capabilities);

__pinned_func
static inline int auxdisplay_capabilities_get(const struct device * dev, struct auxdisplay_capabilities * capabilities)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct auxdisplay_capabilities * val; } parm1 = { .val = capabilities };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_AUXDISPLAY_CAPABILITIES_GET);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_capabilities_get(dev, capabilities);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_capabilities_get(dev, capabilities) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_CAPABILITIES_GET, auxdisplay_capabilities_get, dev, capabilities); 	syscall__retval = auxdisplay_capabilities_get(dev, capabilities); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_CAPABILITIES_GET, auxdisplay_capabilities_get, dev, capabilities, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_clear(const struct device * dev);

__pinned_func
static inline int auxdisplay_clear(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_AUXDISPLAY_CLEAR);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_clear(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_clear(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_CLEAR, auxdisplay_clear, dev); 	syscall__retval = auxdisplay_clear(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_CLEAR, auxdisplay_clear, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_brightness_get(const struct device * dev, uint8_t * brightness);

__pinned_func
static inline int auxdisplay_brightness_get(const struct device * dev, uint8_t * brightness)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint8_t * val; } parm1 = { .val = brightness };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_AUXDISPLAY_BRIGHTNESS_GET);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_brightness_get(dev, brightness);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_brightness_get(dev, brightness) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_BRIGHTNESS_GET, auxdisplay_brightness_get, dev, brightness); 	syscall__retval = auxdisplay_brightness_get(dev, brightness); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_BRIGHTNESS_GET, auxdisplay_brightness_get, dev, brightness, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_brightness_set(const struct device * dev, uint8_t brightness);

__pinned_func
static inline int auxdisplay_brightness_set(const struct device * dev, uint8_t brightness)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint8_t val; } parm1 = { .val = brightness };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_AUXDISPLAY_BRIGHTNESS_SET);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_brightness_set(dev, brightness);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_brightness_set(dev, brightness) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_BRIGHTNESS_SET, auxdisplay_brightness_set, dev, brightness); 	syscall__retval = auxdisplay_brightness_set(dev, brightness); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_BRIGHTNESS_SET, auxdisplay_brightness_set, dev, brightness, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_backlight_get(const struct device * dev, uint8_t * backlight);

__pinned_func
static inline int auxdisplay_backlight_get(const struct device * dev, uint8_t * backlight)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint8_t * val; } parm1 = { .val = backlight };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_AUXDISPLAY_BACKLIGHT_GET);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_backlight_get(dev, backlight);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_backlight_get(dev, backlight) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_BACKLIGHT_GET, auxdisplay_backlight_get, dev, backlight); 	syscall__retval = auxdisplay_backlight_get(dev, backlight); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_BACKLIGHT_GET, auxdisplay_backlight_get, dev, backlight, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_backlight_set(const struct device * dev, uint8_t backlight);

__pinned_func
static inline int auxdisplay_backlight_set(const struct device * dev, uint8_t backlight)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint8_t val; } parm1 = { .val = backlight };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_AUXDISPLAY_BACKLIGHT_SET);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_backlight_set(dev, backlight);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_backlight_set(dev, backlight) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_BACKLIGHT_SET, auxdisplay_backlight_set, dev, backlight); 	syscall__retval = auxdisplay_backlight_set(dev, backlight); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_BACKLIGHT_SET, auxdisplay_backlight_set, dev, backlight, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_is_busy(const struct device * dev);

__pinned_func
static inline int auxdisplay_is_busy(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_AUXDISPLAY_IS_BUSY);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_is_busy(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_is_busy(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_IS_BUSY, auxdisplay_is_busy, dev); 	syscall__retval = auxdisplay_is_busy(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_IS_BUSY, auxdisplay_is_busy, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_custom_character_set(const struct device * dev, struct auxdisplay_character * character);

__pinned_func
static inline int auxdisplay_custom_character_set(const struct device * dev, struct auxdisplay_character * character)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct auxdisplay_character * val; } parm1 = { .val = character };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_AUXDISPLAY_CUSTOM_CHARACTER_SET);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_custom_character_set(dev, character);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_custom_character_set(dev, character) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_CUSTOM_CHARACTER_SET, auxdisplay_custom_character_set, dev, character); 	syscall__retval = auxdisplay_custom_character_set(dev, character); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_CUSTOM_CHARACTER_SET, auxdisplay_custom_character_set, dev, character, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_write(const struct device * dev, const uint8_t * data, uint16_t len);

__pinned_func
static inline int auxdisplay_write(const struct device * dev, const uint8_t * data, uint16_t len)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; const uint8_t * val; } parm1 = { .val = data };
		union { uintptr_t x; uint16_t val; } parm2 = { .val = len };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_AUXDISPLAY_WRITE);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_write(dev, data, len);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_write(dev, data, len) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_WRITE, auxdisplay_write, dev, data, len); 	syscall__retval = auxdisplay_write(dev, data, len); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_WRITE, auxdisplay_write, dev, data, len, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_auxdisplay_custom_command(const struct device * dev, struct auxdisplay_custom_data * data);

__pinned_func
static inline int auxdisplay_custom_command(const struct device * dev, struct auxdisplay_custom_data * data)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; struct auxdisplay_custom_data * val; } parm1 = { .val = data };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_AUXDISPLAY_CUSTOM_COMMAND);
	}
#endif
	compiler_barrier();
	return z_impl_auxdisplay_custom_command(dev, data);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define auxdisplay_custom_command(dev, data) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_AUXDISPLAY_CUSTOM_COMMAND, auxdisplay_custom_command, dev, data); 	syscall__retval = auxdisplay_custom_command(dev, data); 	sys_port_trace_syscall_exit(K_SYSCALL_AUXDISPLAY_CUSTOM_COMMAND, auxdisplay_custom_command, dev, data, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
