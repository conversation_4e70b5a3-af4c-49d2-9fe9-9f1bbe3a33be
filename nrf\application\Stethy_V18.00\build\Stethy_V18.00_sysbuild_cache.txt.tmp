APPLICATION_BINARY_DIR:PATH=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build
APPLICATION_SOURCE_DIR:PATH=C:/ncs/v3.0.2/zephyr/share/sysbuild/template
APP_DIR:PATH=c:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00
BOARD_DIR:PATH=C:/ncs/v3.0.2/zephyr/boards/nordic/nrf5340dk
BOARD_DIRECTORIES:INTERNAL=C:/ncs/v3.0.2/zephyr/boards/nordic/nrf5340dk
BOARD_EXTENSIONS:BOOL=ON
CACHED_BOARD:STRING=nrf5340dk/nrf5340/cpuapp
CACHED_SHIELD:STRING=
CACHED_SNIPPET:STRING=
CONF_FILE:UNINITIALIZED=prj.conf
DTC_OVERLAY_FILE:UNINITIALIZED=boards/nrf5340dk_nrf5340_cpuapp.overlay
FIND_PACKAGE_MESSAGE_DETAILS_Python3:INTERNAL=[C:/ncs/toolchains/0b393f9e1b/opt/bin/python.exe][cfound components: Interpreter ][v3.12.4(3.10)]
HWM:INTERNAL=v2
HWMv2:INTERNAL=True
Stethy_V18.00_NCS_RUNNER_HEX:INTERNAL=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/merged.hex
WEST:INTERNAL=C:/ncs/toolchains/0b393f9e1b/opt/bin/python.exe;-m;west
WEST_PYTHON:UNINITIALIZED=C:/ncs/toolchains/0b393f9e1b/opt/bin/python.exe
ZEPHYR_BASE:PATH=C:/ncs/v3.0.2/zephyr
Zephyr_DIR:PATH=C:/ncs/v3.0.2/zephyr/share/zephyr-package/cmake
_Python3_EXECUTABLE:INTERNAL=C:/ncs/toolchains/0b393f9e1b/opt/bin/python.exe
_Python3_INTERPRETER_PROPERTIES:INTERNAL=Python;3;12;4;64;;cp312-win_amd64;C:\ncs\toolchains\0b393f9e1b\opt\bin\Lib;C:\ncs\toolchains\0b393f9e1b\opt\bin\Lib;C:\ncs\toolchains\0b393f9e1b\opt\bin\Lib\site-packages;C:\ncs\toolchains\0b393f9e1b\opt\bin\Lib\site-packages
_Python3_INTERPRETER_SIGNATURE:INTERNAL=789456af6c61fc011023bd90a8135e32
sysbuild_toplevel_BINARY_DIR:STATIC=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build
sysbuild_toplevel_IS_TOP_LEVEL:STATIC=ON
sysbuild_toplevel_SOURCE_DIR:STATIC=C:/ncs/v3.0.2/zephyr/share/sysbuild
BOARD:STRING=nrf5340dk/nrf5340/cpuapp
SYSBUILD_NAME:STRING=Stethy_V18.00
SYSBUILD_MAIN_APP:BOOL=True
