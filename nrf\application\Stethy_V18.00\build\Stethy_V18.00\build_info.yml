cmake:
  application:
    configuration-dir: C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00
    source-dir: C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00
  board:
    name: nrf5340dk
    path:
     - C:/ncs/v3.0.2/zephyr/boards/nordic/nrf5340dk
    qualifiers: nrf5340/cpuapp
    revision: 
  devicetree:
    bindings-dirs:
     - C:/ncs/v3.0.2/nrf/dts/bindings
     - C:/ncs/v3.0.2/zephyr/dts/bindings
    files:
     - C:/ncs/v3.0.2/zephyr/boards/nordic/nrf5340dk/nrf5340dk_nrf5340_cpuapp.dts
     - boards/nrf5340dk_nrf5340_cpuapp.overlay
    include-dirs:
     - C:/ncs/v3.0.2/nrf/include
     - C:/ncs/v3.0.2/nrf/dts/common
     - C:/ncs/v3.0.2/nrf/dts/riscv
     - C:/ncs/v3.0.2/nrf/dts/arm
     - C:/ncs/v3.0.2/nrf/dts
     - C:/ncs/v3.0.2/zephyr/include
     - C:/ncs/v3.0.2/zephyr/include/zephyr
     - C:/ncs/v3.0.2/zephyr/dts/common
     - C:/ncs/v3.0.2/zephyr/dts/x86
     - C:/ncs/v3.0.2/zephyr/dts/xtensa
     - C:/ncs/v3.0.2/zephyr/dts/sparc
     - C:/ncs/v3.0.2/zephyr/dts/riscv
     - C:/ncs/v3.0.2/zephyr/dts/posix
     - C:/ncs/v3.0.2/zephyr/dts/nios2
     - C:/ncs/v3.0.2/zephyr/dts/arm64
     - C:/ncs/v3.0.2/zephyr/dts/arm
     - C:/ncs/v3.0.2/zephyr/dts/arc
     - C:/ncs/v3.0.2/zephyr/dts
    user-files:
     - boards/nrf5340dk_nrf5340_cpuapp.overlay
  kconfig:
    files:
     - C:/ncs/v3.0.2/zephyr/boards/nordic/nrf5340dk/nrf5340dk_nrf5340_cpuapp_defconfig
     - C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/prj.conf
     - C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/.config.sysbuild
    user-files:
     - prj.conf
  toolchain:
    name: zephyr
    path: C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk
  vendor-specific:
    nordic:
      svdfile: C:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk/nrf5340_application.svd
  zephyr:
    version: 4.0.99
    zephyr-base: C:/ncs/v3.0.2/zephyr
version: 0.1.0
