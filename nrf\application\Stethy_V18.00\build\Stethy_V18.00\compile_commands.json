[{"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o CMakeFiles\\app.dir\\src\\main.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\nrf\\application\\Stethy_V18.00\\src\\main.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\nrf\\application\\Stethy_V18.00\\src\\main.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/inc -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o CMakeFiles\\app.dir\\src\\button.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\nrf\\application\\Stethy_V18.00\\src\\button.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\nrf\\application\\Stethy_V18.00\\src\\button.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr_pre0.dir\\misc\\empty_file.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\misc\\empty_file.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\misc\\empty_file.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -fno-lto -o zephyr\\CMakeFiles\\offsets.dir\\arch\\arm\\core\\offsets\\offsets.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\offsets\\offsets.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\offsets\\offsets.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\heap\\heap.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\heap\\heap.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\heap\\heap.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\cbprintf_packaged.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\os\\cbprintf_packaged.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\os\\cbprintf_packaged.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\printk.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\os\\printk.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\os\\printk.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\sem.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\os\\sem.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\os\\sem.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\thread_entry.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\os\\thread_entry.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\os\\thread_entry.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\cbprintf_complete.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\os\\cbprintf_complete.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\os\\cbprintf_complete.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\assert.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\os\\assert.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\os\\assert.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\os\\mpsc_pbuf.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\os\\mpsc_pbuf.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\os\\mpsc_pbuf.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\dec.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\utils\\dec.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\utils\\dec.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\hex.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\utils\\hex.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\utils\\hex.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\rb.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\utils\\rb.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\utils\\rb.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\timeutil.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\utils\\timeutil.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\utils\\timeutil.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\bitarray.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\utils\\bitarray.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\utils\\bitarray.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\onoff.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\utils\\onoff.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\utils\\onoff.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\notify.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\utils\\notify.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\utils\\notify.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\lib\\utils\\last_section_id.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\utils\\last_section_id.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\utils\\last_section_id.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\misc\\generated\\configs.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\nrf\\application\\Stethy_V18.00\\build\\Stethy_V18.00\\zephyr\\misc\\generated\\configs.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\nrf\\application\\Stethy_V18.00\\build\\Stethy_V18.00\\zephyr\\misc\\generated\\configs.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\log_core.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\subsys\\logging\\log_core.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\subsys\\logging\\log_core.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\log_mgmt.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\subsys\\logging\\log_mgmt.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\subsys\\logging\\log_mgmt.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\log_cache.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\subsys\\logging\\log_cache.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\subsys\\logging\\log_cache.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\log_msg.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\subsys\\logging\\log_msg.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\subsys\\logging\\log_msg.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\log_output.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\subsys\\logging\\log_output.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\subsys\\logging\\log_output.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\logging\\backends\\log_backend_uart.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\subsys\\logging\\backends\\log_backend_uart.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\subsys\\logging\\backends\\log_backend_uart.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\mem_mgmt\\mem_attr.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\subsys\\mem_mgmt\\mem_attr.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\subsys\\mem_mgmt\\mem_attr.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\subsys\\tracing\\tracing_none.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\subsys\\tracing\\tracing_none.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\subsys\\tracing\\tracing_none.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\C_\\ncs\\v3.0.2\\nrf\\lib\\boot_banner\\banner.c.obj -c C:\\ncs\\v3.0.2\\nrf\\lib\\boot_banner\\banner.c", "file": "C:\\ncs\\v3.0.2\\nrf\\lib\\boot_banner\\banner.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\C_\\ncs\\v3.0.2\\nrf\\subsys\\partition_manager\\flash_map_partition_manager.c.obj -c C:\\ncs\\v3.0.2\\nrf\\subsys\\partition_manager\\flash_map_partition_manager.c", "file": "C:\\ncs\\v3.0.2\\nrf\\subsys\\partition_manager\\flash_map_partition_manager.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\C_\\ncs\\v3.0.2\\nrfxlib\\crypto\\nrf_cc312_platform\\src\\nrf_cc3xx_platform_abort_zephyr.c.obj -c C:\\ncs\\v3.0.2\\nrfxlib\\crypto\\nrf_cc312_platform\\src\\nrf_cc3xx_platform_abort_zephyr.c", "file": "C:\\ncs\\v3.0.2\\nrfxlib\\crypto\\nrf_cc312_platform\\src\\nrf_cc3xx_platform_abort_zephyr.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr.dir\\C_\\ncs\\v3.0.2\\nrfxlib\\crypto\\nrf_cc312_platform\\src\\nrf_cc3xx_platform_mutex_zephyr.c.obj -c C:\\ncs\\v3.0.2\\nrfxlib\\crypto\\nrf_cc312_platform\\src\\nrf_cc3xx_platform_mutex_zephyr.c", "file": "C:\\ncs\\v3.0.2\\nrfxlib\\crypto\\nrf_cc312_platform\\src\\nrf_cc3xx_platform_mutex_zephyr.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr_final.dir\\misc\\empty_file.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\misc\\empty_file.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\misc\\empty_file.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\CMakeFiles\\zephyr_final.dir\\isr_tables.c.obj -c C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\nrf\\application\\Stethy_V18.00\\build\\Stethy_V18.00\\zephyr\\isr_tables.c", "file": "C:\\Users\\<USER>\\OneDrive\\Documents\\Projects\\nrf\\application\\Stethy_V18.00\\build\\Stethy_V18.00\\zephyr\\isr_tables.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/ncs/v3.0.2/zephyr/arch/common/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\common\\CMakeFiles\\arch__common.dir\\sw_isr_common.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\common\\sw_isr_common.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\common\\sw_isr_common.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\common\\CMakeFiles\\isr_tables.dir\\isr_tables.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\common\\isr_tables.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\common\\isr_tables.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\CMakeFiles\\arch__arm__core.dir\\fatal.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\fatal.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\fatal.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\CMakeFiles\\arch__arm__core.dir\\nmi.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\nmi.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\nmi.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -xassembler-with-cpp -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -D_ASMLANGUAGE -Wno-unused-but-set-variable -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -o zephyr\\arch\\arch\\arm\\core\\CMakeFiles\\arch__arm__core.dir\\nmi_on_reset.S.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\nmi_on_reset.S", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\nmi_on_reset.S"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\CMakeFiles\\arch__arm__core.dir\\tls.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\tls.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\tls.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\exc_exit.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\exc_exit.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\exc_exit.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\fault.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\fault.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\fault.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -xassembler-with-cpp -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -D_ASMLANGUAGE -Wno-unused-but-set-variable -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\fault_s.S.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\fault_s.S", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\fault_s.S"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\fpu.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\fpu.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\fpu.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -xassembler-with-cpp -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -D_ASMLANGUAGE -Wno-unused-but-set-variable -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\reset.S.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\reset.S", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\reset.S"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\scb.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\scb.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\scb.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\thread_abort.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\thread_abort.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\thread_abort.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -xassembler-with-cpp -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -D_ASMLANGUAGE -Wno-unused-but-set-variable -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\vector_table.S.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\vector_table.S", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\vector_table.S"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -xassembler-with-cpp -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -D_ASMLANGUAGE -Wno-unused-but-set-variable -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\swap_helper.S.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\swap_helper.S", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\swap_helper.S"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\irq_manage.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\irq_manage.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\irq_manage.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\prep_c.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\prep_c.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\prep_c.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\thread.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\thread.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\thread.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\cpu_idle.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\cpu_idle.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\cpu_idle.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\irq_init.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\irq_init.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\irq_init.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\isr_wrapper.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\isr_wrapper.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\isr_wrapper.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -xassembler-with-cpp -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -D_ASMLANGUAGE -Wno-unused-but-set-variable -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\CMakeFiles\\arch__arm__core__cortex_m.dir\\__aeabi_read_tp.S.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\__aeabi_read_tp.S", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\__aeabi_read_tp.S"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\cortex_m\\cmse\\CMakeFiles\\arch__arm__core__cortex_m__cmse.dir\\arm_core_cmse.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\cmse\\arm_core_cmse.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\cortex_m\\cmse\\arm_core_cmse.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/ncs/v3.0.2/zephyr/arch/arm/core/mpu/cortex_m -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\mpu\\CMakeFiles\\arch__arm__core__mpu.dir\\arm_core_mpu.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\mpu\\arm_core_mpu.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\mpu\\arm_core_mpu.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/ncs/v3.0.2/zephyr/arch/arm/core/mpu/cortex_m -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\mpu\\CMakeFiles\\arch__arm__core__mpu.dir\\arm_mpu.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\mpu\\arm_mpu.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\mpu\\arm_mpu.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/ncs/v3.0.2/zephyr/arch/arm/core/mpu/cortex_m -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\arch\\arch\\arm\\core\\mpu\\CMakeFiles\\arch__arm__core__mpu.dir\\arm_mpu_regions.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\mpu\\arm_mpu_regions.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\arch\\arm\\core\\mpu\\arm_mpu_regions.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -fno-lto -o zephyr\\lib\\libc\\picolibc\\CMakeFiles\\lib__libc__picolibc.dir\\assert.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\picolibc\\assert.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\picolibc\\assert.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -fno-lto -o zephyr\\lib\\libc\\picolibc\\CMakeFiles\\lib__libc__picolibc.dir\\cbprintf.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\picolibc\\cbprintf.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\picolibc\\cbprintf.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -fno-lto -o zephyr\\lib\\libc\\picolibc\\CMakeFiles\\lib__libc__picolibc.dir\\chk_fail.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\picolibc\\chk_fail.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\picolibc\\chk_fail.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -fno-lto -o zephyr\\lib\\libc\\picolibc\\CMakeFiles\\lib__libc__picolibc.dir\\errno_wrap.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\picolibc\\errno_wrap.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\picolibc\\errno_wrap.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -fno-lto -o zephyr\\lib\\libc\\picolibc\\CMakeFiles\\lib__libc__picolibc.dir\\exit.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\picolibc\\exit.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\picolibc\\exit.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -fno-lto -o zephyr\\lib\\libc\\picolibc\\CMakeFiles\\lib__libc__picolibc.dir\\locks.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\picolibc\\locks.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\picolibc\\locks.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -fno-lto -o zephyr\\lib\\libc\\picolibc\\CMakeFiles\\lib__libc__picolibc.dir\\stdio.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\picolibc\\stdio.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\picolibc\\stdio.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -fno-builtin-malloc -o zephyr\\lib\\libc\\common\\CMakeFiles\\lib__libc__common.dir\\source\\stdlib\\abort.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\common\\source\\stdlib\\abort.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\common\\source\\stdlib\\abort.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -fno-builtin-malloc -o zephyr\\lib\\libc\\common\\CMakeFiles\\lib__libc__common.dir\\source\\stdlib\\malloc.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\common\\source\\stdlib\\malloc.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\lib\\libc\\common\\source\\stdlib\\malloc.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -U_POSIX_C_SOURCE -D_POSIX_C_SOURCE=200809L -o zephyr\\lib\\posix\\options\\CMakeFiles\\lib__posix__options.dir\\C_\\ncs\\v3.0.2\\zephyr\\misc\\empty_file.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\misc\\empty_file.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\misc\\empty_file.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\validate_base_addresses.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\soc\\nordic\\validate_base_addresses.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\soc\\nordic\\validate_base_addresses.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -include C:/ncs/v3.0.2/zephyr/include/zephyr/dt-bindings/adc/adc.h -include C:/ncs/v3.0.2/zephyr/include/zephyr/dt-bindings/dt-util.h -include C:/ncs/v3.0.2/zephyr/include/zephyr/dt-bindings/gpio/gpio.h -include C:/ncs/v3.0.2/zephyr/include/zephyr/dt-bindings/i2c/i2c.h -include C:/ncs/v3.0.2/zephyr/include/zephyr/dt-bindings/input/input-event-codes.h -include C:/ncs/v3.0.2/zephyr/include/zephyr/dt-bindings/pinctrl/nrf-pinctrl.h -include C:/ncs/v3.0.2/zephyr/include/zephyr/dt-bindings/pwm/pwm.h -include C:/ncs/v3.0.2/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc-v3.h -include C:/ncs/v3.0.2/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc-v2.h -include C:/ncs/v3.0.2/zephyr/include/zephyr/dt-bindings/adc/nrf-saadc.h -include C:/ncs/v3.0.2/zephyr/include/zephyr/dt-bindings/regulator/nrf5x.h -include C:/ncs/v3.0.2/zephyr/include/zephyr/dt-bindings/led/led.h -o zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\validate_binding_headers.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\soc\\nordic\\validate_binding_headers.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\soc\\nordic\\validate_binding_headers.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\validate_enabled_instances.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\soc\\nordic\\validate_enabled_instances.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\soc\\nordic\\validate_enabled_instances.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\nrf53\\soc.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\soc\\nordic\\nrf53\\soc.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\soc\\nordic\\nrf53\\soc.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\nrf53\\nrf53_cpunet_mgmt.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\soc\\nordic\\nrf53\\nrf53_cpunet_mgmt.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\soc\\nordic\\nrf53\\nrf53_cpunet_mgmt.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\soc\\soc\\nrf5340\\CMakeFiles\\soc__nordic.dir\\common\\reboot.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\soc\\nordic\\common\\reboot.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\soc\\nordic\\common\\reboot.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/arch/common/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\drivers\\interrupt_controller\\CMakeFiles\\drivers__interrupt_controller.dir\\C_\\ncs\\v3.0.2\\zephyr\\misc\\empty_file.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\misc\\empty_file.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\misc\\empty_file.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\drivers\\clock_control\\CMakeFiles\\drivers__clock_control.dir\\clock_control_nrf.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\drivers\\clock_control\\clock_control_nrf.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\drivers\\clock_control\\clock_control_nrf.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\drivers\\console\\CMakeFiles\\drivers__console.dir\\uart_console.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\drivers\\console\\uart_console.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\drivers\\console\\uart_console.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\drivers\\gpio\\CMakeFiles\\drivers__gpio.dir\\gpio_nrfx.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\drivers\\gpio\\gpio_nrfx.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\drivers\\gpio\\gpio_nrfx.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\drivers\\i2s\\CMakeFiles\\drivers__i2s.dir\\i2s_common.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\drivers\\i2s\\i2s_common.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\drivers\\i2s\\i2s_common.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\drivers\\i2s\\CMakeFiles\\drivers__i2s.dir\\i2s_nrfx.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\drivers\\i2s\\i2s_nrfx.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\drivers\\i2s\\i2s_nrfx.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\drivers\\led_strip\\CMakeFiles\\drivers__led_strip.dir\\ws2812_i2s.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\drivers\\led_strip\\ws2812_i2s.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\drivers\\led_strip\\ws2812_i2s.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\drivers\\pinctrl\\CMakeFiles\\drivers__pinctrl.dir\\common.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\drivers\\pinctrl\\common.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\drivers\\pinctrl\\common.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\drivers\\pinctrl\\CMakeFiles\\drivers__pinctrl.dir\\pinctrl_nrf.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\drivers\\pinctrl\\pinctrl_nrf.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\drivers\\pinctrl\\pinctrl_nrf.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\drivers\\serial\\CMakeFiles\\drivers__serial.dir\\uart_nrfx_uarte.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\drivers\\serial\\uart_nrfx_uarte.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\drivers\\serial\\uart_nrfx_uarte.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\drivers\\timer\\CMakeFiles\\drivers__timer.dir\\sys_clock_init.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\drivers\\timer\\sys_clock_init.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\drivers\\timer\\sys_clock_init.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\drivers\\timer\\CMakeFiles\\drivers__timer.dir\\nrf_rtc_timer.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\drivers\\timer\\nrf_rtc_timer.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\drivers\\timer\\nrf_rtc_timer.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o modules\\nrf\\drivers\\hw_cc3xx\\CMakeFiles\\..__nrf__drivers__hw_cc3xx.dir\\hw_cc3xx.c.obj -c C:\\ncs\\v3.0.2\\nrf\\drivers\\hw_cc3xx\\hw_cc3xx.c", "file": "C:\\ncs\\v3.0.2\\nrf\\drivers\\hw_cc3xx\\hw_cc3xx.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o modules\\hal_nordic\\modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\86877764a34bccd472b78685f735974f\\mdk\\system_nrf5340_application.c.obj -c C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\mdk\\system_nrf5340_application.c", "file": "C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\mdk\\system_nrf5340_application.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o modules\\hal_nordic\\modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\nrfx_glue.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\modules\\hal_nordic\\nrfx\\nrfx_glue.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\modules\\hal_nordic\\nrfx\\nrfx_glue.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o modules\\hal_nordic\\modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\86877764a34bccd472b78685f735974f\\helpers\\nrfx_flag32_allocator.c.obj -c C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_flag32_allocator.c", "file": "C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_flag32_allocator.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o modules\\hal_nordic\\modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_ram_ctrl.c.obj -c C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_ram_ctrl.c", "file": "C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_ram_ctrl.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o modules\\hal_nordic\\modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_gppi_dppi.c.obj -c C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_gppi_dppi.c", "file": "C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_gppi_dppi.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o modules\\hal_nordic\\modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_gppi_ppi.c.obj -c C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_gppi_ppi.c", "file": "C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\helpers\\nrfx_gppi_ppi.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o modules\\hal_nordic\\modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_clock.c.obj -c C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_clock.c", "file": "C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_clock.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o modules\\hal_nordic\\modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_dppi.c.obj -c C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_dppi.c", "file": "C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_dppi.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o modules\\hal_nordic\\modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_gpiote.c.obj -c C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_gpiote.c", "file": "C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_gpiote.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR__=1 -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o modules\\hal_nordic\\modules\\hal_nordic\\nrfx\\CMakeFiles\\modules__hal_nordic__nrfx.dir\\C_\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_i2s.c.obj -c C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_i2s.c", "file": "C:\\ncs\\v3.0.2\\modules\\hal\\nordic\\nrfx\\drivers\\src\\nrfx_i2s.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\main_weak.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\main_weak.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\main_weak.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\banner.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\banner.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\banner.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\busy_wait.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\busy_wait.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\busy_wait.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\device.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\device.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\device.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\errno.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\errno.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\errno.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\fatal.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\fatal.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\fatal.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\init.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\init.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\init.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\init_static.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\init_static.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\init_static.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\kheap.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\kheap.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\kheap.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\mem_slab.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\mem_slab.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\mem_slab.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\float.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\float.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\float.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\version.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\version.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\version.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\idle.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\idle.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\idle.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\mailbox.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\mailbox.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\mailbox.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\msg_q.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\msg_q.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\msg_q.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\mutex.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\mutex.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\mutex.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\queue.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\queue.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\queue.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\sem.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\sem.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\sem.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\stack.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\stack.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\stack.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\system_work_q.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\system_work_q.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\system_work_q.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\work.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\work.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\work.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\condvar.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\condvar.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\condvar.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\priority_queues.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\priority_queues.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\priority_queues.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\thread.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\thread.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\thread.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\sched.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\sched.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\sched.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\timeslicing.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\timeslicing.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\timeslicing.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\xip.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\xip.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\xip.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\timeout.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\timeout.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\timeout.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\timer.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\timer.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\timer.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\mempool.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\mempool.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\mempool.c"}, {"directory": "C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00", "command": "C:\\ncs\\toolchains\\0b393f9e1b\\opt\\zephyr-sdk\\arm-zephyr-eabi\\bin\\arm-zephyr-eabi-gcc.exe -DKERNEL -DK_HEAP_MEM_POOL_SIZE=0 -DNRF5340_XXAA_APPLICATION -DNRF_SKIP_FICR_NS_COPY_TO_RAM -DPICOLIBC_LONG_LONG_PRINTF_SCANF -DUSE_PARTITION_MANAGER=1 -D__LINUX_ERRNO_EXTENSIONS__ -D__PROGRAM_START -D__ZEPHYR_SUPERVISOR__ -D__ZEPHYR__=1 -IC:/ncs/v3.0.2/zephyr/kernel/include -IC:/ncs/v3.0.2/zephyr/arch/arm/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr -IC:/ncs/v3.0.2/zephyr/include -IC:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated -IC:/ncs/v3.0.2/zephyr/soc/nordic -IC:/ncs/v3.0.2/zephyr/soc/nordic/nrf53/. -IC:/ncs/v3.0.2/zephyr/soc/nordic/common/. -IC:/ncs/v3.0.2/nrf/include -IC:/ncs/v3.0.2/nrf/tests/include -IC:/ncs/v3.0.2/modules/hal/cmsis/CMSIS/Core/Include -IC:/ncs/v3.0.2/zephyr/modules/cmsis/. -IC:/ncs/v3.0.2/nrf/modules/hal_nordic/. -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/drivers/include -IC:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk -IC:/ncs/v3.0.2/zephyr/modules/hal_nordic/nrfx/. -isystem C:/ncs/v3.0.2/zephyr/lib/libc/common/include -isystem C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/include -Wshadow -fno-strict-aliasing -Os -imacros C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/include/generated/zephyr/autoconf.h -fno-printf-return-value -fno-common -g -gdwarf-4 -fdiagnostics-color=always -mcpu=cortex-m33 -mthumb -mabi=aapcs -mfp16-format=ieee -mtp=soft --sysroot=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi -imacros C:/ncs/v3.0.2/zephyr/include/zephyr/toolchain/zephyr_stdint.h -Wall -Wformat -Wformat-security -Wno-format-zero-length -Wdouble-promotion -Wno-pointer-sign -Wpointer-arith -Wexpansion-to-defined -Wno-unused-but-set-variable -Werror=implicit-int -fno-pic -fno-pie -fno-asynchronous-unwind-tables -ftls-model=local-exec -fno-reorder-functions --param=min-pagesize=0 -fno-defer-pop -fmacro-prefix-map=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00=CMAKE_SOURCE_DIR -fmacro-prefix-map=C:/ncs/v3.0.2/zephyr=ZEPHYR_BASE -fmacro-prefix-map=C:/ncs/v3.0.2=WEST_TOPDIR -ffunction-sections -fdata-sections -specs=picolibc.specs -std=c99 -o zephyr\\kernel\\CMakeFiles\\kernel.dir\\dynamic_disabled.c.obj -c C:\\ncs\\v3.0.2\\zephyr\\kernel\\dynamic_disabled.c", "file": "C:\\ncs\\v3.0.2\\zephyr\\kernel\\dynamic_disabled.c"}]