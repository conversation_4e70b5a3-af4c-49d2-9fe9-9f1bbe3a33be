SB_CONFIG_BOARD="nrf5340dk"
SB_CONFIG_BOARD_REVISION=""
SB_CONFIG_BOARD_NRF5340DK=y
SB_CONFIG_BOARD_NRF5340DK_NRF5340_CPUAPP=y
SB_CONFIG_BOARD_QUALIFIERS="nrf5340/cpuapp"
SB_CONFIG_SOC="nrf5340"
SB_CONFIG_SOC_SERIES="nrf53"
SB_CONFIG_SOC_FAMILY="nordic_nrf"
SB_CONFIG_SOC_TOOLCHAIN_NAME="amd_acp_6_0_adsp"
SB_CONFIG_SOC_FAMILY_NORDIC_NRF=y
SB_CONFIG_SOC_SERIES_NRF53X=y
SB_CONFIG_SOC_NRF5340_CPUAPP=y
SB_CONFIG_SOC_NRF5340_CPUAPP_QKAA=y

#
# Sysbuild image configuration
#

#
# Modules
#

#
# Available modules.
#

#
# nrf (C:/ncs/v3.0.2/nrf)
#
SB_CONFIG_PARTITION_MANAGER=y
# SB_CONFIG_PM_OVERRIDE_EXTERNAL_DRIVER_CHECK is not set
SB_CONFIG_BUILD_OUTPUT_BIN=y
SB_CONFIG_BUILD_OUTPUT_HEX=y
SB_CONFIG_APPCORE_REMOTE_BOARD_TARGET_CPUCLUSTER="cpuapp"
SB_CONFIG_APPCORE_REMOTE_DOMAIN="CPUAPP"
SB_CONFIG_SUPPORT_NETCORE=y
SB_CONFIG_NETCORE_REMOTE_BOARD_TARGET_CPUCLUSTER="cpunet"
SB_CONFIG_NETCORE_REMOTE_DOMAIN="CPUNET"

#
# Network core configuration
#
SB_CONFIG_SUPPORT_NETCORE_EMPTY=y
SB_CONFIG_SUPPORT_NETCORE_HCI_IPC=y
SB_CONFIG_SUPPORT_NETCORE_RPC_HOST=y
SB_CONFIG_SUPPORT_NETCORE_802154_RPMSG=y
SB_CONFIG_SUPPORT_NETCORE_IPC_RADIO=y
SB_CONFIG_NETCORE_NONE=y
# SB_CONFIG_NETCORE_EMPTY is not set
# SB_CONFIG_NETCORE_HCI_IPC is not set
# SB_CONFIG_NETCORE_RPC_HOST is not set
# SB_CONFIG_NETCORE_802154_RPMSG is not set
# SB_CONFIG_NETCORE_IPC_RADIO is not set
# end of Network core configuration

#
# Secure Bootloader
#
# SB_CONFIG_SECURE_BOOT_APPCORE is not set
# SB_CONFIG_SECURE_BOOT_NETCORE is not set
# end of Secure Bootloader

SB_CONFIG_SUPPORT_QSPI_XIP=y

#
# Bluetooth Fair Pair
#
SB_CONFIG_BT_FAST_PAIR_MODEL_ID=0x1000000
SB_CONFIG_BT_FAST_PAIR_ANTI_SPOOFING_PRIVATE_KEY=""
# end of Bluetooth Fair Pair

# SB_CONFIG_MATTER is not set

#
# Wi-Fi
#
# SB_CONFIG_WIFI_NRF70 is not set
# end of Wi-Fi

#
# SUIT
#
# SB_CONFIG_SUIT_ENVELOPE is not set
# SB_CONFIG_SUIT_BUILD_RECOVERY is not set
# SB_CONFIG_SUIT_BUILD_FLASH_COMPANION is not set
# SB_CONFIG_SUIT_BUILD_AB_UPDATE is not set
# SB_CONFIG_SUIT_SOC_FLASH_NRF_RADIO_SYNC_RPC is not set
# end of SUIT

#
# SUIT provisioning
#
# SB_CONFIG_SUIT_MPI_GENERATE is not set
# end of SUIT provisioning

# SB_CONFIG_HPF is not set
# SB_CONFIG_APPROTECT_USE_UICR is not set
# SB_CONFIG_APPROTECT_LOCK is not set
# SB_CONFIG_APPROTECT_USER_HANDLING is not set
SB_CONFIG_APPROTECT_NO_SYSBUILD=y
# SB_CONFIG_SECURE_APPROTECT_USE_UICR is not set
# SB_CONFIG_SECURE_APPROTECT_LOCK is not set
# SB_CONFIG_SECURE_APPROTECT_USER_HANDLING is not set
SB_CONFIG_SECURE_APPROTECT_NO_SYSBUILD=y
SB_CONFIG_ZEPHYR_NRF_MODULE=y
# end of nrf (C:/ncs/v3.0.2/nrf)

SB_CONFIG_ZEPHYR_HOSTAP_MODULE=y
SB_CONFIG_ZEPHYR_MCUBOOT_MODULE=y
SB_CONFIG_ZEPHYR_MBEDTLS_MODULE=y
SB_CONFIG_ZEPHYR_OBERON_PSA_CRYPTO_MODULE=y
SB_CONFIG_ZEPHYR_TRUSTED_FIRMWARE_M_MODULE=y
SB_CONFIG_ZEPHYR_PSA_ARCH_TESTS_MODULE=y
SB_CONFIG_ZEPHYR_CJSON_MODULE=y
SB_CONFIG_ZEPHYR_AZURE_SDK_FOR_C_MODULE=y
SB_CONFIG_ZEPHYR_CIRRUS_LOGIC_MODULE=y
SB_CONFIG_ZEPHYR_OPENTHREAD_MODULE=y
SB_CONFIG_ZEPHYR_SUIT_GENERATOR_MODULE=y
SB_CONFIG_ZEPHYR_SUIT_PROCESSOR_MODULE=y
SB_CONFIG_ZEPHYR_MEMFAULT_FIRMWARE_SDK_MODULE=y
SB_CONFIG_ZEPHYR_COREMARK_MODULE=y
SB_CONFIG_ZEPHYR_CANOPENNODE_MODULE=y
SB_CONFIG_ZEPHYR_CHRE_MODULE=y
SB_CONFIG_ZEPHYR_LZ4_MODULE=y
SB_CONFIG_ZEPHYR_NANOPB_MODULE=y
SB_CONFIG_ZEPHYR_TF_M_TESTS_MODULE=y
SB_CONFIG_ZEPHYR_ZSCILIB_MODULE=y
SB_CONFIG_ZEPHYR_CMSIS_MODULE=y
SB_CONFIG_ZEPHYR_CMSIS_DSP_MODULE=y
SB_CONFIG_ZEPHYR_CMSIS_NN_MODULE=y
SB_CONFIG_ZEPHYR_FATFS_MODULE=y
SB_CONFIG_ZEPHYR_HAL_NORDIC_MODULE=y
SB_CONFIG_ZEPHYR_HAL_ST_MODULE=y
SB_CONFIG_ZEPHYR_HAL_TDK_MODULE=y
SB_CONFIG_ZEPHYR_HAL_WURTHELEKTRONIK_MODULE=y
SB_CONFIG_ZEPHYR_LIBLC3_MODULE=y
SB_CONFIG_ZEPHYR_LIBMETAL_MODULE=y
SB_CONFIG_ZEPHYR_LITTLEFS_MODULE=y
SB_CONFIG_ZEPHYR_LORAMAC_NODE_MODULE=y
SB_CONFIG_ZEPHYR_LVGL_MODULE=y
SB_CONFIG_ZEPHYR_MIPI_SYS_T_MODULE=y
SB_CONFIG_ZEPHYR_NRF_WIFI_MODULE=y
SB_CONFIG_ZEPHYR_OPEN_AMP_MODULE=y
SB_CONFIG_ZEPHYR_PERCEPIO_MODULE=y
SB_CONFIG_ZEPHYR_PICOLIBC_MODULE=y
SB_CONFIG_ZEPHYR_SEGGER_MODULE=y
SB_CONFIG_ZEPHYR_TINYCRYPT_MODULE=y
SB_CONFIG_ZEPHYR_UOSCORE_UEDHOC_MODULE=y
SB_CONFIG_ZEPHYR_ZCBOR_MODULE=y
SB_CONFIG_ZEPHYR_NRFXLIB_MODULE=y
SB_CONFIG_ZEPHYR_NRF_HW_MODELS_MODULE=y
SB_CONFIG_ZEPHYR_CONNECTEDHOMEIP_MODULE=y

#
# Unavailable modules, please install those via the project manifest.
#
# end of Modules

# SB_CONFIG_WARN_EXPERIMENTAL is not set
SB_CONFIG_WARN_DEPRECATED=y
SB_CONFIG_SUPPORT_BOOTLOADER=y
SB_CONFIG_SUPPORT_BOOTLOADER_MCUBOOT_ZEPHYR=y
SB_CONFIG_BOOTLOADER_NONE=y
# SB_CONFIG_BOOTLOADER_MCUBOOT is not set

#
# Build options
#
# SB_CONFIG_COMPILER_WARNINGS_AS_ERRORS is not set
# end of Build options
