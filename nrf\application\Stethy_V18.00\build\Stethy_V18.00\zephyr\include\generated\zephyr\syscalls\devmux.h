/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_DEVMUX_H
#define Z_INCLUDE_SYSCALLS_DEVMUX_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_devmux_select_get(const struct device * dev);

__pinned_func
static inline int devmux_select_get(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (int) arch_syscall_invoke1(parm0.x, K_SYSCALL_DEVMUX_SELECT_GET);
	}
#endif
	compiler_barrier();
	return z_impl_devmux_select_get(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define devmux_select_get(dev) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_DEVMUX_SELECT_GET, devmux_select_get, dev); 	syscall__retval = devmux_select_get(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_DEVMUX_SELECT_GET, devmux_select_get, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_devmux_select_set(struct device * dev, size_t index);

__pinned_func
static inline int devmux_select_set(struct device * dev, size_t index)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; size_t val; } parm1 = { .val = index };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_DEVMUX_SELECT_SET);
	}
#endif
	compiler_barrier();
	return z_impl_devmux_select_set(dev, index);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define devmux_select_set(dev, index) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_DEVMUX_SELECT_SET, devmux_select_set, dev, index); 	syscall__retval = devmux_select_set(dev, index); 	sys_port_trace_syscall_exit(K_SYSCALL_DEVMUX_SELECT_SET, devmux_select_set, dev, index, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
