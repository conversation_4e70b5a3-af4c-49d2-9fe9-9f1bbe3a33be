# This is the CMakeCache file.
# For build in directory: c:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build
# It was generated by CMake: C:/ncs/toolchains/0b393f9e1b/opt/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Application Binary Directory
APPLICATION_BINARY_DIR:PATH=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build

//Application Source Directory
APPLICATION_SOURCE_DIR:PATH=C:/ncs/v3.0.2/zephyr/share/sysbuild/template

//No help, variable specified on the command line.
APP_DIR:PATH=c:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00

//Selected board
BOARD:STRING=nrf5340dk/nrf5340/cpuapp

//Main board directory for board (nrf5340dk)
BOARD_DIR:PATH=C:/ncs/v3.0.2/zephyr/boards/nordic/nrf5340dk

//Support board extensions
BOARD_EXTENSIONS:BOOL=ON

//Selected board
CACHED_BOARD:STRING=nrf5340dk/nrf5340/cpuapp

//Selected shield
CACHED_SHIELD:STRING=

//Selected snippet
CACHED_SNIPPET:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=ON

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/sysbuild_toplevel

//Program used to build from build.ninja files.
CMAKE_MAKE_PROGRAM:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/bin/ninja.exe

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=sysbuild_toplevel

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//No help, variable specified on the command line.
CONF_FILE:UNINITIALIZED=prj.conf

//No help, variable specified on the command line.
DTC_OVERLAY_FILE:UNINITIALIZED=boards/nrf5340dk_nrf5340_cpuapp.overlay

//No help, variable specified on the command line.
WEST_PYTHON:UNINITIALIZED=C:/ncs/toolchains/0b393f9e1b/opt/bin/python.exe

//Zephyr base
ZEPHYR_BASE:PATH=C:/ncs/v3.0.2/zephyr

//Path to merged image in Intel Hex format
ZEPHYR_RUNNER_CONFIG_KERNEL_HEX:STRING=

//The directory containing a CMake configuration file for Zephyr.
Zephyr_DIR:PATH=C:/ncs/v3.0.2/zephyr/share/zephyr-package/cmake

//Value Computed by CMake
sysbuild_BINARY_DIR:STATIC=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild

//Value Computed by CMake
sysbuild_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
sysbuild_SOURCE_DIR:STATIC=C:/ncs/v3.0.2/zephyr/share/sysbuild/template

//Value Computed by CMake
sysbuild_toplevel_BINARY_DIR:STATIC=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build

//Value Computed by CMake
sysbuild_toplevel_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
sysbuild_toplevel_SOURCE_DIR:STATIC=C:/ncs/v3.0.2/zephyr/share/sysbuild


########################
# INTERNAL cache entries
########################

//List of board directories for board (nrf5340dk)
BOARD_DIRECTORIES:INTERNAL=C:/ncs/v3.0.2/zephyr/boards/nordic/nrf5340dk
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=c:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=21
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=0
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/ncs/toolchains/0b393f9e1b/opt/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/ncs/toolchains/0b393f9e1b/opt/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/ncs/toolchains/0b393f9e1b/opt/bin/ctest.exe
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=C:/ncs/v3.0.2/zephyr/share/sysbuild
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=8
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/ncs/toolchains/0b393f9e1b/opt/share/cmake-3.21
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding Python3
FIND_PACKAGE_MESSAGE_DETAILS_Python3:INTERNAL=[C:/ncs/toolchains/0b393f9e1b/opt/bin/python.exe][cfound components: Interpreter ][v3.12.4(3.10)]
//Zephyr hardware model version
HWM:INTERNAL=v2
//Zephyr hardware model
HWMv2:INTERNAL=True
//nRF Connect SDK partition managere controlled hex file
Stethy_V18.00_NCS_RUNNER_HEX:INTERNAL=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/merged.hex
//West
WEST:INTERNAL=C:/ncs/toolchains/0b393f9e1b/opt/bin/python.exe;-m;west
_Python3_EXECUTABLE:INTERNAL=C:/ncs/toolchains/0b393f9e1b/opt/bin/python.exe
//Python3 Properties
_Python3_INTERPRETER_PROPERTIES:INTERNAL=Python;3;12;4;64;;cp312-win_amd64;C:\ncs\toolchains\0b393f9e1b\opt\bin\Lib;C:\ncs\toolchains\0b393f9e1b\opt\bin\Lib;C:\ncs\toolchains\0b393f9e1b\opt\bin\Lib\site-packages;C:\ncs\toolchains\0b393f9e1b\opt\bin\Lib\site-packages
_Python3_INTERPRETER_SIGNATURE:INTERNAL=789456af6c61fc011023bd90a8135e32

