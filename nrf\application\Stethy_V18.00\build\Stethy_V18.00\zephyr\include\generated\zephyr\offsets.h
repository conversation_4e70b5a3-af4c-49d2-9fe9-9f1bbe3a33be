/* THIS FILE IS AUTO GENERATED.  PLEASE DO NOT EDIT.
 *
 * This header file provides macros for the offsets of various structure
 * members.  These offset macros are primarily intended to be used in
 * assembly code.
 */

#ifndef __GEN_OFFSETS_H__
#define __GEN_OFFSETS_H__

#define ___cpu_t_current_OFFSET 0x8
#define ___cpu_t_nested_OFFSET 0x0
#define ___cpu_t_irq_stack_OFFSET 0x4
#define ___cpu_t_arch_OFFSET 0x11
#define ___kernel_t_cpus_OFFSET 0x0
#define ___kernel_t_ready_q_OFFSET 0x14
#define ___ready_q_t_cache_OFFSET 0x0
#define ___thread_base_t_user_options_OFFSET 0xc
#define ___thread_t_base_OFFSET 0x0
#define ___thread_t_callee_saved_OFFSET 0x30
#define ___thread_t_arch_OFFSET 0x74
#define ___thread_t_stack_info_OFFSET 0x60
#define ___thread_t_tls_OFFSET 0x70
#define __z_interrupt_stack_SIZEOF 0x800
#define __z_interrupt_all_stacks_SIZEOF 0x800
#define _PM_DEVICE_STRUCT_FLAGS_OFFSET 0x0
#define ___thread_arch_t_basepri_OFFSET 0x0
#define ___thread_arch_t_swap_return_value_OFFSET 0x4
#define ___basic_sf_t_pc_OFFSET 0x18
#define ___basic_sf_t_xpsr_OFFSET 0x1c
#define ___esf_t_SIZEOF 0x20
#define ___callee_saved_t_SIZEOF 0x24
#define ___thread_stack_info_t_start_OFFSET 0x0

#endif /* __GEN_OFFSETS_H__ */
