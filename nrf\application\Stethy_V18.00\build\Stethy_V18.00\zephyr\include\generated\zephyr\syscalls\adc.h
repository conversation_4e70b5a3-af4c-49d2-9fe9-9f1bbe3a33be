/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_ADC_H
#define Z_INCLUDE_SYSCALLS_ADC_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_adc_channel_setup(const struct device * dev, const struct adc_channel_cfg * channel_cfg);

__pinned_func
static inline int adc_channel_setup(const struct device * dev, const struct adc_channel_cfg * channel_cfg)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; const struct adc_channel_cfg * val; } parm1 = { .val = channel_cfg };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_ADC_CHANNEL_SETUP);
	}
#endif
	compiler_barrier();
	return z_impl_adc_channel_setup(dev, channel_cfg);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define adc_channel_setup(dev, channel_cfg) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_ADC_CHANNEL_SETUP, adc_channel_setup, dev, channel_cfg); 	syscall__retval = adc_channel_setup(dev, channel_cfg); 	sys_port_trace_syscall_exit(K_SYSCALL_ADC_CHANNEL_SETUP, adc_channel_setup, dev, channel_cfg, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_adc_read(const struct device * dev, const struct adc_sequence * sequence);

__pinned_func
static inline int adc_read(const struct device * dev, const struct adc_sequence * sequence)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; const struct adc_sequence * val; } parm1 = { .val = sequence };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_ADC_READ);
	}
#endif
	compiler_barrier();
	return z_impl_adc_read(dev, sequence);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define adc_read(dev, sequence) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_ADC_READ, adc_read, dev, sequence); 	syscall__retval = adc_read(dev, sequence); 	sys_port_trace_syscall_exit(K_SYSCALL_ADC_READ, adc_read, dev, sequence, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_adc_read_async(const struct device * dev, const struct adc_sequence * sequence, struct k_poll_signal * async);

__pinned_func
static inline int adc_read_async(const struct device * dev, const struct adc_sequence * sequence, struct k_poll_signal * async)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; const struct adc_sequence * val; } parm1 = { .val = sequence };
		union { uintptr_t x; struct k_poll_signal * val; } parm2 = { .val = async };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_ADC_READ_ASYNC);
	}
#endif
	compiler_barrier();
	return z_impl_adc_read_async(dev, sequence, async);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define adc_read_async(dev, sequence, async) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_ADC_READ_ASYNC, adc_read_async, dev, sequence, async); 	syscall__retval = adc_read_async(dev, sequence, async); 	sys_port_trace_syscall_exit(K_SYSCALL_ADC_READ_ASYNC, adc_read_async, dev, sequence, async, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
