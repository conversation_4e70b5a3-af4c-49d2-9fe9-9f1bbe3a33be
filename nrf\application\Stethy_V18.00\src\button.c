#include "button.h"
#include "main.h"

static const struct gpio_dt_spec button0 = GPIO_DT_SPEC_GET(SW0_NODE, gpios);
static const struct gpio_dt_spec button1 = GPIO_DT_SPEC_GET(SW1_NODE, gpios);
static const struct gpio_dt_spec button2 = GPIO_DT_SPEC_GET(SW2_NODE, gpios);
static const struct gpio_dt_spec button3 = GPIO_DT_SPEC_GET(SW3_NODE, gpios);

static struct gpio_callback button_cb_data;

int buttons_init(void)
{
    int ret = 0;
    ret = gpio_pin_interrupt_configure_dt(&button0, GPIO_INT_EDGE_TO_ACTIVE);
        if (ret < 0) {
                return ret;
        }

        ret = gpio_pin_interrupt_configure_dt(&button1, GPIO_INT_EDGE_TO_ACTIVE);
        if (ret < 0) {
                return ret;
        }

        ret = gpio_pin_interrupt_configure_dt(&button2, GPIO_INT_EDGE_TO_ACTIVE);
        if (ret < 0) {
                return ret;
        }

        ret = gpio_pin_interrupt_configure_dt(&button3, GPIO_INT_EDGE_TO_ACTIVE);
        if (ret < 0) {
                return ret;
        }

    gpio_init_callback(&button_cb_data, button_pressed, BIT(button0.pin) | BIT(button1.pin) | BIT(button2.pin) | BIT(button3.pin));
        gpio_add_callback(button0.port, &button_cb_data);

    return ret;
}

void button_pressed(const struct device *dev, struct gpio_callback *cb, uint32_t pins)
{
    led_toggle();
}