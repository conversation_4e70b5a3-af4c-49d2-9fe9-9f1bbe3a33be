/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_LED_H
#define Z_INCLUDE_SYSCALLS_LED_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_led_blink(const struct device * dev, uint32_t led, uint32_t delay_on, uint32_t delay_off);

__pinned_func
static inline int led_blink(const struct device * dev, uint32_t led, uint32_t delay_on, uint32_t delay_off)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = led };
		union { uintptr_t x; uint32_t val; } parm2 = { .val = delay_on };
		union { uintptr_t x; uint32_t val; } parm3 = { .val = delay_off };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_LED_BLINK);
	}
#endif
	compiler_barrier();
	return z_impl_led_blink(dev, led, delay_on, delay_off);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define led_blink(dev, led, delay_on, delay_off) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_LED_BLINK, led_blink, dev, led, delay_on, delay_off); 	syscall__retval = led_blink(dev, led, delay_on, delay_off); 	sys_port_trace_syscall_exit(K_SYSCALL_LED_BLINK, led_blink, dev, led, delay_on, delay_off, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_led_get_info(const struct device * dev, uint32_t led, const struct led_info ** info);

__pinned_func
static inline int led_get_info(const struct device * dev, uint32_t led, const struct led_info ** info)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = led };
		union { uintptr_t x; const struct led_info ** val; } parm2 = { .val = info };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_LED_GET_INFO);
	}
#endif
	compiler_barrier();
	return z_impl_led_get_info(dev, led, info);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define led_get_info(dev, led, info) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_LED_GET_INFO, led_get_info, dev, led, info); 	syscall__retval = led_get_info(dev, led, info); 	sys_port_trace_syscall_exit(K_SYSCALL_LED_GET_INFO, led_get_info, dev, led, info, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_led_set_brightness(const struct device * dev, uint32_t led, uint8_t value);

__pinned_func
static inline int led_set_brightness(const struct device * dev, uint32_t led, uint8_t value)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = led };
		union { uintptr_t x; uint8_t val; } parm2 = { .val = value };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_LED_SET_BRIGHTNESS);
	}
#endif
	compiler_barrier();
	return z_impl_led_set_brightness(dev, led, value);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define led_set_brightness(dev, led, value) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_LED_SET_BRIGHTNESS, led_set_brightness, dev, led, value); 	syscall__retval = led_set_brightness(dev, led, value); 	sys_port_trace_syscall_exit(K_SYSCALL_LED_SET_BRIGHTNESS, led_set_brightness, dev, led, value, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_led_write_channels(const struct device * dev, uint32_t start_channel, uint32_t num_channels, const uint8_t * buf);

__pinned_func
static inline int led_write_channels(const struct device * dev, uint32_t start_channel, uint32_t num_channels, const uint8_t * buf)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = start_channel };
		union { uintptr_t x; uint32_t val; } parm2 = { .val = num_channels };
		union { uintptr_t x; const uint8_t * val; } parm3 = { .val = buf };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_LED_WRITE_CHANNELS);
	}
#endif
	compiler_barrier();
	return z_impl_led_write_channels(dev, start_channel, num_channels, buf);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define led_write_channels(dev, start_channel, num_channels, buf) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_LED_WRITE_CHANNELS, led_write_channels, dev, start_channel, num_channels, buf); 	syscall__retval = led_write_channels(dev, start_channel, num_channels, buf); 	sys_port_trace_syscall_exit(K_SYSCALL_LED_WRITE_CHANNELS, led_write_channels, dev, start_channel, num_channels, buf, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_led_set_channel(const struct device * dev, uint32_t channel, uint8_t value);

__pinned_func
static inline int led_set_channel(const struct device * dev, uint32_t channel, uint8_t value)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = channel };
		union { uintptr_t x; uint8_t val; } parm2 = { .val = value };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_LED_SET_CHANNEL);
	}
#endif
	compiler_barrier();
	return z_impl_led_set_channel(dev, channel, value);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define led_set_channel(dev, channel, value) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_LED_SET_CHANNEL, led_set_channel, dev, channel, value); 	syscall__retval = led_set_channel(dev, channel, value); 	sys_port_trace_syscall_exit(K_SYSCALL_LED_SET_CHANNEL, led_set_channel, dev, channel, value, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_led_set_color(const struct device * dev, uint32_t led, uint8_t num_colors, const uint8_t * color);

__pinned_func
static inline int led_set_color(const struct device * dev, uint32_t led, uint8_t num_colors, const uint8_t * color)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = led };
		union { uintptr_t x; uint8_t val; } parm2 = { .val = num_colors };
		union { uintptr_t x; const uint8_t * val; } parm3 = { .val = color };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_LED_SET_COLOR);
	}
#endif
	compiler_barrier();
	return z_impl_led_set_color(dev, led, num_colors, color);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define led_set_color(dev, led, num_colors, color) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_LED_SET_COLOR, led_set_color, dev, led, num_colors, color); 	syscall__retval = led_set_color(dev, led, num_colors, color); 	sys_port_trace_syscall_exit(K_SYSCALL_LED_SET_COLOR, led_set_color, dev, led, num_colors, color, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_led_on(const struct device * dev, uint32_t led);

__pinned_func
static inline int led_on(const struct device * dev, uint32_t led)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = led };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_LED_ON);
	}
#endif
	compiler_barrier();
	return z_impl_led_on(dev, led);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define led_on(dev, led) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_LED_ON, led_on, dev, led); 	syscall__retval = led_on(dev, led); 	sys_port_trace_syscall_exit(K_SYSCALL_LED_ON, led_on, dev, led, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_led_off(const struct device * dev, uint32_t led);

__pinned_func
static inline int led_off(const struct device * dev, uint32_t led)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = led };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_LED_OFF);
	}
#endif
	compiler_barrier();
	return z_impl_led_off(dev, led);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define led_off(dev, led) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_LED_OFF, led_off, dev, led); 	syscall__retval = led_off(dev, led); 	sys_port_trace_syscall_exit(K_SYSCALL_LED_OFF, led_off, dev, led, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
