# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.21

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: sysbuild_toplevel
# Configurations: 
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5

# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles\rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = C$:\Users\Kagen\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\

#############################################
# Utility command for edit_cache

build CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake-gui.exe -SC:\ncs\v3.0.2\zephyr\share\sysbuild -BC:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe --regenerate-during-build -SC:\ncs\v3.0.2\zephyr\share\sysbuild -BC:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles\rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/ncs/v3.0.2/zephyr/share/sysbuild/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for sysbuild_guiconfig

build _sysbuild\sysbuild_guiconfig: phony _sysbuild\CMakeFiles\sysbuild_guiconfig


#############################################
# Utility command for boards

build _sysbuild\boards: phony _sysbuild\CMakeFiles\boards


#############################################
# Utility command for shields

build _sysbuild\shields: phony _sysbuild\CMakeFiles\shields


#############################################
# Utility command for snippets

build _sysbuild\snippets: phony _sysbuild\CMakeFiles\snippets


#############################################
# Utility command for config-twister

build _sysbuild\config-twister: phony _sysbuild\CMakeFiles\config-twister


#############################################
# Utility command for sysbuild_menuconfig

build _sysbuild\sysbuild_menuconfig: phony _sysbuild\CMakeFiles\sysbuild_menuconfig


#############################################
# Utility command for build_info_yaml_saved

build _sysbuild\build_info_yaml_saved: phony _sysbuild\CMakeFiles\build_info_yaml_saved


#############################################
# Utility command for Stethy_V18.00_cache

build _sysbuild\Stethy_V18.00_cache: phony


#############################################
# Utility command for Stethy_V18.00_devicetree_target

build _sysbuild\Stethy_V18.00_devicetree_target: phony


#############################################
# Utility command for Stethy_V18.00_extra_byproducts

build _sysbuild\Stethy_V18.00_extra_byproducts: phony _sysbuild\CMakeFiles\Stethy_V18.00_extra_byproducts Stethy_V18.00\zephyr\zephyr.bin Stethy_V18.00\zephyr\zephyr.elf Stethy_V18.00\zephyr\zephyr.hex _sysbuild\sysbuild\images\Stethy_V18.00


#############################################
# Utility command for partition_manager

build _sysbuild\partition_manager: phony


#############################################
# Utility command for merged_hex

build _sysbuild\merged_hex: phony _sysbuild\CMakeFiles\merged_hex merged.hex _sysbuild\Stethy_V18.00_extra_byproducts _sysbuild\sysbuild\images\Stethy_V18.00


#############################################
# Utility command for edit_cache

build _sysbuild\CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake-gui.exe -SC:\ncs\v3.0.2\zephyr\share\sysbuild -BC:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _sysbuild\edit_cache: phony _sysbuild\CMakeFiles\edit_cache.util


#############################################
# Utility command for partition_manager_report

build _sysbuild\partition_manager_report: phony _sysbuild\CMakeFiles\partition_manager_report


#############################################
# Utility command for rebuild_cache

build _sysbuild\CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe --regenerate-during-build -SC:\ncs\v3.0.2\zephyr\share\sysbuild -BC:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _sysbuild\rebuild_cache: phony _sysbuild\CMakeFiles\rebuild_cache.util


#############################################
# Custom command for _sysbuild\CMakeFiles\sysbuild_guiconfig

build _sysbuild\CMakeFiles\sysbuild_guiconfig | ${cmake_ninja_workdir}_sysbuild\CMakeFiles\sysbuild_guiconfig: CUSTOM_COMMAND
  COMMAND = _sysbuild\CMakeFiles\sysbuild_guiconfig-015c191.bat e0830e8de492750a
  pool = console


#############################################
# Custom command for _sysbuild\CMakeFiles\boards

build _sysbuild\CMakeFiles\boards | ${cmake_ninja_workdir}_sysbuild\CMakeFiles\boards: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild && C:\ncs\toolchains\0b393f9e1b\opt\bin\python.exe C:/ncs/v3.0.2/zephyr/scripts/list_boards.py --board-root=C:/ncs/v3.0.2/nrf --board-root=C:/ncs/v3.0.2/zephyr --arch-root=C:/ncs/v3.0.2/zephyr --soc-root=C:/ncs/v3.0.2/nrf --soc-root=C:/ncs/v3.0.2/zephyr"
  pool = console


#############################################
# Custom command for _sysbuild\CMakeFiles\shields

build _sysbuild\CMakeFiles\shields | ${cmake_ninja_workdir}_sysbuild\CMakeFiles\shields: CUSTOM_COMMAND
  COMMAND = _sysbuild\CMakeFiles\shields-a20ad5f.bat 283a1758efead54c
  pool = console


#############################################
# Custom command for _sysbuild\CMakeFiles\snippets

build _sysbuild\CMakeFiles\snippets | ${cmake_ninja_workdir}_sysbuild\CMakeFiles\snippets: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo bt-ll-sw-split && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo cdc-acm-console && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo ci-shell && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo diagnostic-logs && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo hpf-gpio-icbmsg && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo hpf-gpio-icmsg && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo hpf-gpio-mbox && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo hpf-mspi && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo hw-flow-control && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo matter-debug && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo nordic-bt-rpc && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo nordic-flpr && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo nordic-flpr-xip && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo nordic-log-stm && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo nordic-log-stm-dict && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo nordic-ppr && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo nordic-ppr-xip && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo nrf54l09-switch-uart && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo nrf70-driver-debug && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo nrf70-driver-verbose-debug && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo nrf70-fw-patch-ext-flash && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo nrf70-wifi && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo nrf91-modem-trace-ext-flash && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo nrf91-modem-trace-ram && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo nrf91-modem-trace-rtt && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo nrf91-modem-trace-uart && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo nus-console && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo power-consumption-tests && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo ram-console && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo rtt-console && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo rtt-tracing && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo serial-console && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo tfm-enable-share-uart && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo wifi-enterprise && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo wifi-ipv4 && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo wpa-supplicant-debug && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo xen_dom0 && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo zperf"
  pool = console


#############################################
# Phony custom command for _sysbuild\CMakeFiles\config-twister

build _sysbuild\CMakeFiles\config-twister | ${cmake_ninja_workdir}_sysbuild\CMakeFiles\config-twister: phony zephyr\.config


#############################################
# Custom command for _sysbuild\CMakeFiles\sysbuild_menuconfig

build _sysbuild\CMakeFiles\sysbuild_menuconfig | ${cmake_ninja_workdir}_sysbuild\CMakeFiles\sysbuild_menuconfig: CUSTOM_COMMAND
  COMMAND = _sysbuild\CMakeFiles\sysbuild_menuconfig-1e014e8.bat 8fa575830cc3a6bd
  pool = console


#############################################
# Phony custom command for _sysbuild\CMakeFiles\build_info_yaml_saved

build _sysbuild\CMakeFiles\build_info_yaml_saved | ${cmake_ninja_workdir}_sysbuild\CMakeFiles\build_info_yaml_saved: phony build_info.yml


#############################################
# Custom command for _sysbuild\CMakeFiles\Stethy_V18.00_extra_byproducts

build _sysbuild\CMakeFiles\Stethy_V18.00_extra_byproducts Stethy_V18.00\zephyr\zephyr.bin Stethy_V18.00\zephyr\zephyr.elf Stethy_V18.00\zephyr\zephyr.hex | ${cmake_ninja_workdir}_sysbuild\CMakeFiles\Stethy_V18.00_extra_byproducts ${cmake_ninja_workdir}Stethy_V18.00\zephyr\zephyr.bin ${cmake_ninja_workdir}Stethy_V18.00\zephyr\zephyr.elf ${cmake_ninja_workdir}Stethy_V18.00\zephyr\zephyr.hex: CUSTOM_COMMAND || _sysbuild\sysbuild\images\Stethy_V18.00
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E true"
  restat = 1


#############################################
# Phony custom command for _sysbuild\CMakeFiles\merged_hex

build _sysbuild\CMakeFiles\merged_hex | ${cmake_ninja_workdir}_sysbuild\CMakeFiles\merged_hex: phony merged.hex || _sysbuild\Stethy_V18.00_extra_byproducts _sysbuild\sysbuild\images\Stethy_V18.00


#############################################
# Custom command for merged.hex

build merged.hex | ${cmake_ninja_workdir}merged.hex: CUSTOM_COMMAND Stethy_V18.00\zephyr\zephyr.hex || _sysbuild\Stethy_V18.00_extra_byproducts _sysbuild\sysbuild\images\Stethy_V18.00
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild && C:\ncs\toolchains\0b393f9e1b\opt\bin\python.exe C:/ncs/v3.0.2/zephyr/scripts/build/mergehex.py -o C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/merged.hex --overlap=replace C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/zephyr.hex"
  DESC = Generating ../merged.hex
  restat = 1


#############################################
# Custom command for _sysbuild\CMakeFiles\partition_manager_report

build _sysbuild\CMakeFiles\partition_manager_report | ${cmake_ninja_workdir}_sysbuild\CMakeFiles\partition_manager_report: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild && C:\ncs\toolchains\0b393f9e1b\opt\bin\python.exe C:/ncs/v3.0.2/nrf/scripts/partition_manager_report.py --input C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/partitions.yml"

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/ncs/v3.0.2/zephyr/share/sysbuild/cmake/modules/sysbuild_images.cmake
# =============================================================================


#############################################
# Utility command for menuconfig

build _sysbuild\sysbuild\images\menuconfig: phony _sysbuild\sysbuild\images\CMakeFiles\menuconfig


#############################################
# Utility command for rebuild_cache

build _sysbuild\sysbuild\images\CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild\sysbuild\images && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe --regenerate-during-build -SC:\ncs\v3.0.2\zephyr\share\sysbuild -BC:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _sysbuild\sysbuild\images\rebuild_cache: phony _sysbuild\sysbuild\images\CMakeFiles\rebuild_cache.util


#############################################
# Utility command for hardenconfig

build _sysbuild\sysbuild\images\hardenconfig: phony _sysbuild\sysbuild\images\CMakeFiles\hardenconfig


#############################################
# Utility command for guiconfig

build _sysbuild\sysbuild\images\guiconfig: phony _sysbuild\sysbuild\images\CMakeFiles\guiconfig


#############################################
# Utility command for Stethy_V18.00

build _sysbuild\sysbuild\images\Stethy_V18.00: phony _sysbuild\sysbuild\images\CMakeFiles\Stethy_V18.00 _sysbuild\sysbuild\images\CMakeFiles\Stethy_V18.00-complete _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-done _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-build _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-configure _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-download _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-install _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-mkdir _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-patch _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-update


#############################################
# Utility command for edit_cache

build _sysbuild\sysbuild\images\CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild\sysbuild\images && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake-gui.exe -SC:\ncs\v3.0.2\zephyr\share\sysbuild -BC:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _sysbuild\sysbuild\images\edit_cache: phony _sysbuild\sysbuild\images\CMakeFiles\edit_cache.util


#############################################
# Custom command for _sysbuild\sysbuild\images\CMakeFiles\menuconfig

build _sysbuild\sysbuild\images\CMakeFiles\menuconfig | ${cmake_ninja_workdir}_sysbuild\sysbuild\images\CMakeFiles\menuconfig: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\Stethy_V18.00 && C:\ncs\toolchains\0b393f9e1b\opt\bin\ninja.exe menuconfig"
  pool = console


#############################################
# Custom command for _sysbuild\sysbuild\images\CMakeFiles\hardenconfig

build _sysbuild\sysbuild\images\CMakeFiles\hardenconfig | ${cmake_ninja_workdir}_sysbuild\sysbuild\images\CMakeFiles\hardenconfig: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\Stethy_V18.00 && C:\ncs\toolchains\0b393f9e1b\opt\bin\ninja.exe hardenconfig"
  pool = console


#############################################
# Custom command for _sysbuild\sysbuild\images\CMakeFiles\guiconfig

build _sysbuild\sysbuild\images\CMakeFiles\guiconfig | ${cmake_ninja_workdir}_sysbuild\sysbuild\images\CMakeFiles\guiconfig: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\Stethy_V18.00 && C:\ncs\toolchains\0b393f9e1b\opt\bin\ninja.exe guiconfig"
  pool = console


#############################################
# Phony custom command for _sysbuild\sysbuild\images\CMakeFiles\Stethy_V18.00

build _sysbuild\sysbuild\images\CMakeFiles\Stethy_V18.00 | ${cmake_ninja_workdir}_sysbuild\sysbuild\images\CMakeFiles\Stethy_V18.00: phony _sysbuild\sysbuild\images\CMakeFiles\Stethy_V18.00-complete


#############################################
# Custom command for _sysbuild\sysbuild\images\CMakeFiles\Stethy_V18.00-complete

build _sysbuild\sysbuild\images\CMakeFiles\Stethy_V18.00-complete _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-done | ${cmake_ninja_workdir}_sysbuild\sysbuild\images\CMakeFiles\Stethy_V18.00-complete ${cmake_ninja_workdir}_sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-done: CUSTOM_COMMAND _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-install _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-mkdir _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-download _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-update _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-patch _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-configure _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-build _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-install
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild\sysbuild\images && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E make_directory C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images/CMakeFiles && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E touch C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images/CMakeFiles/Stethy_V18.00-complete && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E touch C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images/Stethy_V18.00-prefix/src/Stethy_V18.00-stamp/Stethy_V18.00-done"
  DESC = Completed 'Stethy_V18.00'
  restat = 1


#############################################
# Custom command for _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-build

build _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-build | ${cmake_ninja_workdir}_sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-build: CUSTOM_COMMAND _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-configure
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\Stethy_V18.00 && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe --build ."
  DESC = Performing build step for 'Stethy_V18.00'
  pool = console


#############################################
# Custom command for _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-configure

build _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-configure | ${cmake_ninja_workdir}_sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-configure: CUSTOM_COMMAND _sysbuild\sysbuild\images\Stethy_V18.00-prefix\tmp\Stethy_V18.00-cfgcmd.txt _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-patch
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\Stethy_V18.00 && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo_append && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E touch C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images/Stethy_V18.00-prefix/src/Stethy_V18.00-stamp/Stethy_V18.00-configure"
  DESC = No configure step for 'Stethy_V18.00'
  restat = 1


#############################################
# Custom command for _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-download

build _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-download | ${cmake_ninja_workdir}_sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-download: CUSTOM_COMMAND _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-mkdir
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild\sysbuild\images && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo_append && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E touch C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images/Stethy_V18.00-prefix/src/Stethy_V18.00-stamp/Stethy_V18.00-download"
  DESC = No download step for 'Stethy_V18.00'
  restat = 1


#############################################
# Custom command for _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-install

build _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-install | ${cmake_ninja_workdir}_sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-install: CUSTOM_COMMAND _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-build
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\Stethy_V18.00 && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo_append && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E touch C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images/Stethy_V18.00-prefix/src/Stethy_V18.00-stamp/Stethy_V18.00-install"
  DESC = No install step for 'Stethy_V18.00'
  restat = 1


#############################################
# Custom command for _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-mkdir

build _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-mkdir | ${cmake_ninja_workdir}_sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-mkdir: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild\sysbuild\images && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E make_directory C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00 && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E make_directory C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00 && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E make_directory C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images/Stethy_V18.00-prefix && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E make_directory C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images/Stethy_V18.00-prefix/tmp && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E make_directory C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images/Stethy_V18.00-prefix/src/Stethy_V18.00-stamp && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E make_directory C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images/Stethy_V18.00-prefix/src && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E make_directory C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images/Stethy_V18.00-prefix/src/Stethy_V18.00-stamp && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E touch C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images/Stethy_V18.00-prefix/src/Stethy_V18.00-stamp/Stethy_V18.00-mkdir"
  DESC = Creating directories for 'Stethy_V18.00'
  restat = 1


#############################################
# Custom command for _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-patch

build _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-patch | ${cmake_ninja_workdir}_sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-patch: CUSTOM_COMMAND _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-update
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild\sysbuild\images && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo_append && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E touch C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images/Stethy_V18.00-prefix/src/Stethy_V18.00-stamp/Stethy_V18.00-patch"
  DESC = No patch step for 'Stethy_V18.00'
  restat = 1


#############################################
# Custom command for _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-update

build _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-update | ${cmake_ninja_workdir}_sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-update: CUSTOM_COMMAND _sysbuild\sysbuild\images\Stethy_V18.00-prefix\src\Stethy_V18.00-stamp\Stethy_V18.00-download
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild\sysbuild\images && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E echo_append && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe -E touch C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images/Stethy_V18.00-prefix/src/Stethy_V18.00-stamp/Stethy_V18.00-update"
  DESC = No update step for 'Stethy_V18.00'
  restat = 1

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/ncs/v3.0.2/zephyr/share/sysbuild/images/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build _sysbuild\sysbuild\images\bootloader\CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild\sysbuild\images\bootloader && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake-gui.exe -SC:\ncs\v3.0.2\zephyr\share\sysbuild -BC:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _sysbuild\sysbuild\images\bootloader\edit_cache: phony _sysbuild\sysbuild\images\bootloader\CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build _sysbuild\sysbuild\images\bootloader\CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild\sysbuild\images\bootloader && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe --regenerate-during-build -SC:\ncs\v3.0.2\zephyr\share\sysbuild -BC:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _sysbuild\sysbuild\images\bootloader\rebuild_cache: phony _sysbuild\sysbuild\images\bootloader\CMakeFiles\rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/ncs/v3.0.2/zephyr/share/sysbuild/images/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build modules\nrf\CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\modules\nrf && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake-gui.exe -SC:\ncs\v3.0.2\zephyr\share\sysbuild -BC:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build modules\nrf\edit_cache: phony modules\nrf\CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build modules\nrf\CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\modules\nrf && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe --regenerate-during-build -SC:\ncs\v3.0.2\zephyr\share\sysbuild -BC:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build modules\nrf\rebuild_cache: phony modules\nrf\CMakeFiles\rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/ncs/v3.0.2/zephyr/share/sysbuild/images/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build modules\mcuboot\CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\modules\mcuboot && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake-gui.exe -SC:\ncs\v3.0.2\zephyr\share\sysbuild -BC:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build modules\mcuboot\edit_cache: phony modules\mcuboot\CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build modules\mcuboot\CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\modules\mcuboot && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe --regenerate-during-build -SC:\ncs\v3.0.2\zephyr\share\sysbuild -BC:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build modules\mcuboot\rebuild_cache: phony modules\mcuboot\CMakeFiles\rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/ncs/v3.0.2/zephyr/share/sysbuild/images/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build _sysbuild\sysbuild\images\boards\CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild\sysbuild\images\boards && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake-gui.exe -SC:\ncs\v3.0.2\zephyr\share\sysbuild -BC:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _sysbuild\sysbuild\images\boards\edit_cache: phony _sysbuild\sysbuild\images\boards\CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build _sysbuild\sysbuild\images\boards\CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild\sysbuild\images\boards && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe --regenerate-during-build -SC:\ncs\v3.0.2\zephyr\share\sysbuild -BC:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _sysbuild\sysbuild\images\boards\rebuild_cache: phony _sysbuild\sysbuild\images\boards\CMakeFiles\rebuild_cache.util

# =============================================================================
# Write statements declared in CMakeLists.txt:
# C:/ncs/v3.0.2/zephyr/share/sysbuild/images/CMakeLists.txt
# =============================================================================


#############################################
# Utility command for edit_cache

build _sysbuild\sysbuild\images\soc\CMakeFiles\edit_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild\sysbuild\images\soc && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake-gui.exe -SC:\ncs\v3.0.2\zephyr\share\sysbuild -BC:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build"
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build _sysbuild\sysbuild\images\soc\edit_cache: phony _sysbuild\sysbuild\images\soc\CMakeFiles\edit_cache.util


#############################################
# Utility command for rebuild_cache

build _sysbuild\sysbuild\images\soc\CMakeFiles\rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cmd.exe /C "cd /D C:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build\_sysbuild\sysbuild\images\soc && C:\ncs\toolchains\0b393f9e1b\opt\bin\cmake.exe --regenerate-during-build -SC:\ncs\v3.0.2\zephyr\share\sysbuild -BC:\Users\<USER>\OneDrive\Documents\Projects\nrf\application\Stethy_V18.00\build"
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build _sysbuild\sysbuild\images\soc\rebuild_cache: phony _sysbuild\sysbuild\images\soc\CMakeFiles\rebuild_cache.util

# =============================================================================
# Target aliases.

build Stethy_V18.00: phony _sysbuild\sysbuild\images\Stethy_V18.00

build Stethy_V18.00_cache: phony _sysbuild\Stethy_V18.00_cache

build Stethy_V18.00_devicetree_target: phony _sysbuild\Stethy_V18.00_devicetree_target

build Stethy_V18.00_extra_byproducts: phony _sysbuild\Stethy_V18.00_extra_byproducts

build boards: phony _sysbuild\boards

build build_info_yaml_saved: phony _sysbuild\build_info_yaml_saved

build config-twister: phony _sysbuild\config-twister

build guiconfig: phony _sysbuild\sysbuild\images\guiconfig

build hardenconfig: phony _sysbuild\sysbuild\images\hardenconfig

build menuconfig: phony _sysbuild\sysbuild\images\menuconfig

build merged_hex: phony _sysbuild\merged_hex

build partition_manager: phony _sysbuild\partition_manager

build partition_manager_report: phony _sysbuild\partition_manager_report

build shields: phony _sysbuild\shields

build snippets: phony _sysbuild\snippets

build sysbuild_guiconfig: phony _sysbuild\sysbuild_guiconfig

build sysbuild_menuconfig: phony _sysbuild\sysbuild_menuconfig

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build

build all: phony _sysbuild\all

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild

build _sysbuild\all: phony _sysbuild\build_info_yaml_saved _sysbuild\merged_hex _sysbuild\sysbuild\images\all

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images

build _sysbuild\sysbuild\images\all: phony _sysbuild\sysbuild\images\Stethy_V18.00 _sysbuild\sysbuild\images\bootloader\all modules\nrf\all modules\mcuboot\all _sysbuild\sysbuild\images\boards\all _sysbuild\sysbuild\images\soc\all

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images/boards

build _sysbuild\sysbuild\images\boards\all: phony

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images/bootloader

build _sysbuild\sysbuild\images\bootloader\all: phony

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/_sysbuild/sysbuild/images/soc

build _sysbuild\sysbuild\images\soc\all: phony

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/modules/mcuboot

build modules\mcuboot\all: phony

# =============================================================================

#############################################
# Folder: C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/modules/nrf

build modules\nrf\all: phony

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CMakeDetermineSystem.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CMakeGenericSystem.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CMakeInitializeConfigs.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CMakeNinjaFindMake.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CMakeSystem.cmake.in C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CMakeSystemSpecificInformation.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CMakeSystemSpecificInitialize.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CheckCCompilerFlag.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CheckCSourceCompiles.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CheckCXXCompilerFlag.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CheckCXXSourceCompiles.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\ExternalProject.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\FindPackageHandleStandardArgs.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\FindPackageMessage.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\FindPython3.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\FindPython\Support.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\Internal\CheckCompilerFlag.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\Internal\CheckSourceCompiles.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\Platform\Windows.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\Platform\WindowsPaths.cmake C$:\ncs\v3.0.2\bootloader\mcuboot\boot\zephyr\sysbuild\CMakeLists.txt C$:\ncs\v3.0.2\nrf\cmake\extensions.cmake C$:\ncs\v3.0.2\nrf\cmake\sysbuild\b0_mcuboot_signing.cmake C$:\ncs\v3.0.2\nrf\cmake\sysbuild\modules\ncs_sysbuild_extensions.cmake C$:\ncs\v3.0.2\nrf\cmake\sysbuild\partition_manager.cmake C$:\ncs\v3.0.2\nrf\cmake\sysbuild\suit.cmake C$:\ncs\v3.0.2\nrf\cmake\sysbuild\suit_provisioning.cmake C$:\ncs\v3.0.2\nrf\cmake\sysbuild\suit_utilities.cmake C$:\ncs\v3.0.2\nrf\modules\modules.cmake C$:\ncs\v3.0.2\nrf\snippets\ci-shell\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\hpf\gpio\icbmsg\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\hpf\gpio\icmsg\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\hpf\gpio\mbox\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\hpf\mspi\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\hw-flow-control\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\matter-debug\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\matter-diagnostic-logs\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\matter-power-consumption-tests\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nordic-bt-rpc\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf54l09-switch-uart\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf70-driver-debug\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf70-driver-verbose-debug\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf70-fw-patch-ext-flash\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf70-wifi\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf91-modem-trace-ext-flash\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf91-modem-trace-ram\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf91-modem-trace-rtt\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf91-modem-trace-uart\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\tfm-enable-share-uart\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\wpa-supplicant-debug\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\zperf\snippet.yml C$:\ncs\v3.0.2\nrf\soc\nordic\Kconfig.soc C$:\ncs\v3.0.2\nrf\soc\nordic\nrf54l\Kconfig.soc C$:\ncs\v3.0.2\nrf\soc\nordic\nrf71\Kconfig.soc C$:\ncs\v3.0.2\nrf\subsys\bootloader\cmake\packaging.cmake C$:\ncs\v3.0.2\nrf\sysbuild\CMakeLists.txt C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.appcore C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.approtect C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.bt_fast_pair C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.cracen C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.dfu C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.flprcore C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.hpf C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.lwm2m_carrier C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.matter C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.mcuboot C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.netcore C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.pprcore C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.secureboot C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.suit C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.suit_provisioning C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.sysbuild C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.tfm C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.wifi C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.xip C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.zip C$:\ncs\v3.0.2\nrf\sysbuild\appcore.cmake C$:\ncs\v3.0.2\nrf\sysbuild\extensions.cmake C$:\ncs\v3.0.2\nrf\sysbuild\hpf.cmake C$:\ncs\v3.0.2\nrf\sysbuild\mcuboot.cmake C$:\ncs\v3.0.2\nrf\sysbuild\netcore.cmake C$:\ncs\v3.0.2\nrf\sysbuild\secureboot.cmake C$:\ncs\v3.0.2\nrf\sysbuild\suit.cmake C$:\ncs\v3.0.2\nrf\sysbuild\suit_provisioning\Kconfig.nrf54h20 C$:\ncs\v3.0.2\nrf\sysbuild\suit_provisioning\Kconfig.nrf9280 C$:\ncs\v3.0.2\nrf\sysbuild\suit_provisioning\Kconfig.template.manifest_config C$:\ncs\v3.0.2\zephyr\VERSION C$:\ncs\v3.0.2\zephyr\boards\Kconfig.v2 C$:\ncs\v3.0.2\zephyr\boards\deprecated.cmake C$:\ncs\v3.0.2\zephyr\boards\nordic\nrf5340dk\Kconfig.nrf5340dk C$:\ncs\v3.0.2\zephyr\boards\nordic\nrf5340dk\board.yml C$:\ncs\v3.0.2\zephyr\cmake\modules\FindDeprecated.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\boards.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\extensions.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\hwm_v2.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\kconfig.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\python.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\shields.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\snippets.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\user_cache.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\version.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\west.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\yaml.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\zephyr_module.cmake C$:\ncs\v3.0.2\zephyr\modules\Kconfig.sysbuild C$:\ncs\v3.0.2\zephyr\modules\modules.cmake C$:\ncs\v3.0.2\zephyr\scripts\snippets.py C$:\ncs\v3.0.2\zephyr\share\sysbuild-package\cmake\SysbuildConfig.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild-package\cmake\SysbuildConfigVersion.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\CMakeLists.txt C$:\ncs\v3.0.2\zephyr\share\sysbuild\Kconfig C$:\ncs\v3.0.2\zephyr\share\sysbuild\Kconfig.v2 C$:\ncs\v3.0.2\zephyr\share\sysbuild\build\Kconfig C$:\ncs\v3.0.2\zephyr\share\sysbuild\cmake\domains.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\cmake\modules\native_simulator_sb_extensions.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\cmake\modules\sysbuild_default.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\cmake\modules\sysbuild_extensions.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\cmake\modules\sysbuild_images.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\cmake\modules\sysbuild_kconfig.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\cmake\modules\sysbuild_root.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\cmake\modules\sysbuild_snippets.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\image_configurations\ALL_image_default.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\image_configurations\MAIN_image_default.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\images\CMakeLists.txt C$:\ncs\v3.0.2\zephyr\share\sysbuild\images\Kconfig C$:\ncs\v3.0.2\zephyr\share\sysbuild\images\boards\CMakeLists.txt C$:\ncs\v3.0.2\zephyr\share\sysbuild\images\bootloader\CMakeLists.txt C$:\ncs\v3.0.2\zephyr\share\sysbuild\images\bootloader\Kconfig C$:\ncs\v3.0.2\zephyr\share\sysbuild\images\soc\CMakeLists.txt C$:\ncs\v3.0.2\zephyr\share\sysbuild\template\CMakeLists.txt C$:\ncs\v3.0.2\zephyr\share\zephyr-package\cmake\ZephyrConfig.cmake C$:\ncs\v3.0.2\zephyr\share\zephyr-package\cmake\ZephyrConfigVersion.cmake C$:\ncs\v3.0.2\zephyr\share\zephyr-package\cmake\zephyr_package_search.cmake C$:\ncs\v3.0.2\zephyr\snippets\bt-ll-sw-split\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\cdc-acm-console\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\nordic-flpr-xip\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\nordic-flpr\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\nordic-log-stm-dict\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\nordic-log-stm\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\nordic-ppr-xip\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\nordic-ppr\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\nus-console\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\ram-console\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\rtt-console\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\rtt-tracing\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\serial-console\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\wifi-enterprise\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\wifi-ipv4\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\xen_dom0\snippet.yml C$:\ncs\v3.0.2\zephyr\soc\Kconfig.v2 C$:\ncs\v3.0.2\zephyr\soc\adi\max32\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\altr\qemu_nios2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\altr\zephyr_nios2f\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ambiq\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ambiq\apollo3x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ambiq\apollo4x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\amd\acp_6_0\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\andestech\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\andestech\ae350\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\beetle\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\designstart\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\fvp_aemv8a\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\fvp_aemv8r\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\mps2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\mps3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\musca\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\qemu_cortex_a53\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\qemu_virt_arm64\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\aspeed\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\aspeed\ast10x0\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\samc20\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\samc21\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\samd20\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\samd21\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\samd51\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\same51\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\same53\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\same54\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\saml21\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\samr21\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\samr34\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\samr35\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam\sam3x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam\sam4e\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam\sam4l\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam\sam4s\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam\same70\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam\samv71\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\brcm\bcm2711\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\brcm\bcm2712\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\brcm\bcmvk\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\brcm\bcmvk\valkyrie\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\brcm\bcmvk\viper\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\cdns\dc233c\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\cdns\sample_controller32\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\cdns\xtensa_sample_controller\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\efinix\sapphire\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ene\kb1200\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\espressif\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\espressif\Kconfig.sysbuild C$:\ncs\v3.0.2\zephyr\soc\espressif\esp32\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\espressif\esp32c2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\espressif\esp32c3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\espressif\esp32c6\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\espressif\esp32s2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\espressif\esp32s3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gaisler\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gaisler\gr716a\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gaisler\leon3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\gd32a50x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\gd32e10x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\gd32e50x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\gd32f3x0\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\gd32f403\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\gd32f4xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\gd32l23x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\gd32vf103\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat1a\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat1a\psoc6_01\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat1a\psoc6_02\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat1a\psoc6_03\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat1a\psoc6_04\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat1a\psoc6_legacy\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat1b\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat1b\cyw20829\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat3\xmc4xxx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\alder_lake\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\apollo_lake\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\atom\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\elkhart_lake\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_adsp\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_adsp\ace\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_adsp\cavs\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_ish\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_ish\intel_ish5\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_niosv\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_niosv\niosv\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_socfpga\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_socfpga\agilex5\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_socfpga\agilex\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_socfpga_std\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_socfpga_std\cyclonev\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\lakemont\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\raptor_lake\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ite\ec\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ite\ec\it8xxx2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\litex\litex_vexriscv\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\lowrisc\opentitan\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\mediatek\mt8xxx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\mec\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\mec\mec15xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\mec\mec172x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\mec\mec174x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\mec\mec175x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\mec\mech172x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\miv\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\miv\miv\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\miv\polarfire\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\native\inf_clock\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\neorv32\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\Kconfig.sysbuild C$:\ncs\v3.0.2\zephyr\soc\nordic\common\vpr\Kconfig.sysbuild C$:\ncs\v3.0.2\zephyr\soc\nordic\nrf51\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\nrf52\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\nrf53\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\nrf54h\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\nrf54l\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\nrf91\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\nrf92\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\sysbuild.cmake C$:\ncs\v3.0.2\zephyr\soc\nuvoton\npcm\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\npcm\npcm4\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\npcx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\npcx\npcx4\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\npcx\npcx7\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\npcx\npcx9\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\numaker\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\numaker\m2l31x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\numaker\m46x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\numicro\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\numicro\m48x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx6sx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx7d\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx8\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx8m\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx8ulp\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx8x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx9\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx9\imx93\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx9\imx95\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imxrt\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imxrt\Kconfig.sysbuild C$:\ncs\v3.0.2\zephyr\soc\nxp\imxrt\imxrt10xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imxrt\imxrt118x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imxrt\imxrt11xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imxrt\imxrt5xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imxrt\imxrt6xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\k2x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\k6x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\k8x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\ke1xf\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\ke1xz\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\kl2x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\kv5x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\kwx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\layerscape\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\layerscape\ls1046a\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\lpc\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\lpc\lpc11u6x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\lpc\lpc51u68\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\lpc\lpc54xxx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\lpc\lpc55xxx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\mcx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\mcx\mcxa\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\mcx\mcxc\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\mcx\mcxn\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\mcx\mcxw\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\rw\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\s32\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\s32\s32k1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\s32\s32k3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\s32\s32ze\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\openisa\rv32m1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\qemu\malta\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\qemu\virt_riscv\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\qemu\virt_riscv\qemu_virt_riscv32\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\qemu\virt_riscv\qemu_virt_riscv32e\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\qemu\virt_riscv\qemu_virt_riscv64\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\quicklogic\eos_s3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\raspberrypi\rpi_pico\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\raspberrypi\rpi_pico\rp2040\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\raspberrypi\rpi_pico\rp2350\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\realtek\ec\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\realtek\ec\rts5912\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra2a1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra4e2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra4m1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra4m2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra4m3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra4w1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra6e1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra6e2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra6m1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra6m2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra6m3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra6m4\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra6m5\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra8d1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra8m1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra8t1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\rcar\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\rcar\rcar_gen3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\rcar\rcar_gen4\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\rz\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\rz\rzg3s\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\rzt2m\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\smartbond\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\smartbond\da1469x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renode\cortex_r8_virtual\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renode\riscv_virtual\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\rockchip\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\rockchip\rk3399\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\rockchip\rk3568\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\sensry\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\sensry\ganymed\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\sensry\ganymed\sy1xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\sifive\sifive_freedom\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\sifive\sifive_freedom\fe300\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\sifive\sifive_freedom\fu500\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\sifive\sifive_freedom\fu700\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s0\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s0\efm32hg\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s0\efm32wg\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efm32gg11b\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efm32gg12b\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efm32jg12b\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efm32pg12b\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efm32pg1b\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efr32bg13p\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efr32fg13p\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efr32fg1p\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efr32mg12p\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s2\efr32bg22\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s2\efr32bg27\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s2\efr32mg21\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s2\efr32mg24\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s2\efr32zg23\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_sim3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_sim3\sim3u\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\arc_iot\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\emsdp\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\emsk\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\hsdk4xd\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\hsdk\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\nsim\arc_classic\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\nsim\arc_classic\em\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\nsim\arc_classic\hs\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\nsim\arc_classic\sem\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\nsim\arc_classic\vpx5\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\nsim\arc_v\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\nsim\arc_v\rmx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\qemu_arc\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32c0x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32f0x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32f1x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32f2x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32f3x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32f4x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32f7x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32g0x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32g4x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32h5x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32h7rsx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32h7x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32l0x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32l1x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32l4x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32l5x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32mp1x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32u0x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32u5x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32wb0x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32wbax\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32wbx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32wlx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\starfive\jh71xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\telink\tlsr\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\telink\tlsr\tlsr951x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ti\k3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ti\k3\am6x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ti\lm3s6965\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ti\simplelink\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ti\simplelink\cc13x2_cc26x2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ti\simplelink\cc13x2x7_cc26x2x7\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ti\simplelink\cc32xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ti\simplelink\msp432p4xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\wch\ch32v00x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\xen\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\xlnx\zynq7000\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\xlnx\zynq7000\xc7zxxx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\xlnx\zynq7000\xc7zxxxs\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\xlnx\zynqmp\Kconfig.soc CMakeCache.txt CMakeFiles\3.21.0\CMakeSystem.cmake Kconfig\Kconfig.sysbuild.modules Kconfig\boards\Kconfig.nrf5340dk Kconfig\boards\Kconfig.sysbuild Kconfig\soc\Kconfig.soc Kconfig\soc\Kconfig.sysbuild Stethy_V18.00\CMakeCache.tmp Stethy_V18.00\zephyr\.config Stethy_V18.00\zephyr\dts.cmake _sysbuild\autoconf.h _sysbuild\empty.conf _sysbuild\sysbuild\images\Stethy_V18.00-prefix\tmp\Stethy_V18.00-cfgcmd.txt.in zephyr\.config zephyr\snippets_generated.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CMakeDetermineSystem.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CMakeGenericSystem.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CMakeInitializeConfigs.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CMakeNinjaFindMake.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CMakeSystem.cmake.in C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CMakeSystemSpecificInformation.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CMakeSystemSpecificInitialize.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CheckCCompilerFlag.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CheckCSourceCompiles.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CheckCXXCompilerFlag.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\CheckCXXSourceCompiles.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\ExternalProject.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\FindPackageHandleStandardArgs.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\FindPackageMessage.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\FindPython3.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\FindPython\Support.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\Internal\CheckCompilerFlag.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\Internal\CheckSourceCompiles.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\Platform\Windows.cmake C$:\ncs\toolchains\0b393f9e1b\opt\share\cmake-3.21\Modules\Platform\WindowsPaths.cmake C$:\ncs\v3.0.2\bootloader\mcuboot\boot\zephyr\sysbuild\CMakeLists.txt C$:\ncs\v3.0.2\nrf\cmake\extensions.cmake C$:\ncs\v3.0.2\nrf\cmake\sysbuild\b0_mcuboot_signing.cmake C$:\ncs\v3.0.2\nrf\cmake\sysbuild\modules\ncs_sysbuild_extensions.cmake C$:\ncs\v3.0.2\nrf\cmake\sysbuild\partition_manager.cmake C$:\ncs\v3.0.2\nrf\cmake\sysbuild\suit.cmake C$:\ncs\v3.0.2\nrf\cmake\sysbuild\suit_provisioning.cmake C$:\ncs\v3.0.2\nrf\cmake\sysbuild\suit_utilities.cmake C$:\ncs\v3.0.2\nrf\modules\modules.cmake C$:\ncs\v3.0.2\nrf\snippets\ci-shell\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\hpf\gpio\icbmsg\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\hpf\gpio\icmsg\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\hpf\gpio\mbox\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\hpf\mspi\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\hw-flow-control\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\matter-debug\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\matter-diagnostic-logs\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\matter-power-consumption-tests\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nordic-bt-rpc\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf54l09-switch-uart\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf70-driver-debug\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf70-driver-verbose-debug\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf70-fw-patch-ext-flash\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf70-wifi\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf91-modem-trace-ext-flash\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf91-modem-trace-ram\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf91-modem-trace-rtt\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\nrf91-modem-trace-uart\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\tfm-enable-share-uart\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\wpa-supplicant-debug\snippet.yml C$:\ncs\v3.0.2\nrf\snippets\zperf\snippet.yml C$:\ncs\v3.0.2\nrf\soc\nordic\Kconfig.soc C$:\ncs\v3.0.2\nrf\soc\nordic\nrf54l\Kconfig.soc C$:\ncs\v3.0.2\nrf\soc\nordic\nrf71\Kconfig.soc C$:\ncs\v3.0.2\nrf\subsys\bootloader\cmake\packaging.cmake C$:\ncs\v3.0.2\nrf\sysbuild\CMakeLists.txt C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.appcore C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.approtect C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.bt_fast_pair C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.cracen C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.dfu C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.flprcore C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.hpf C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.lwm2m_carrier C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.matter C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.mcuboot C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.netcore C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.pprcore C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.secureboot C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.suit C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.suit_provisioning C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.sysbuild C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.tfm C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.wifi C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.xip C$:\ncs\v3.0.2\nrf\sysbuild\Kconfig.zip C$:\ncs\v3.0.2\nrf\sysbuild\appcore.cmake C$:\ncs\v3.0.2\nrf\sysbuild\extensions.cmake C$:\ncs\v3.0.2\nrf\sysbuild\hpf.cmake C$:\ncs\v3.0.2\nrf\sysbuild\mcuboot.cmake C$:\ncs\v3.0.2\nrf\sysbuild\netcore.cmake C$:\ncs\v3.0.2\nrf\sysbuild\secureboot.cmake C$:\ncs\v3.0.2\nrf\sysbuild\suit.cmake C$:\ncs\v3.0.2\nrf\sysbuild\suit_provisioning\Kconfig.nrf54h20 C$:\ncs\v3.0.2\nrf\sysbuild\suit_provisioning\Kconfig.nrf9280 C$:\ncs\v3.0.2\nrf\sysbuild\suit_provisioning\Kconfig.template.manifest_config C$:\ncs\v3.0.2\zephyr\VERSION C$:\ncs\v3.0.2\zephyr\boards\Kconfig.v2 C$:\ncs\v3.0.2\zephyr\boards\deprecated.cmake C$:\ncs\v3.0.2\zephyr\boards\nordic\nrf5340dk\Kconfig.nrf5340dk C$:\ncs\v3.0.2\zephyr\boards\nordic\nrf5340dk\board.yml C$:\ncs\v3.0.2\zephyr\cmake\modules\FindDeprecated.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\boards.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\extensions.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\hwm_v2.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\kconfig.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\python.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\shields.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\snippets.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\user_cache.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\version.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\west.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\yaml.cmake C$:\ncs\v3.0.2\zephyr\cmake\modules\zephyr_module.cmake C$:\ncs\v3.0.2\zephyr\modules\Kconfig.sysbuild C$:\ncs\v3.0.2\zephyr\modules\modules.cmake C$:\ncs\v3.0.2\zephyr\scripts\snippets.py C$:\ncs\v3.0.2\zephyr\share\sysbuild-package\cmake\SysbuildConfig.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild-package\cmake\SysbuildConfigVersion.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\CMakeLists.txt C$:\ncs\v3.0.2\zephyr\share\sysbuild\Kconfig C$:\ncs\v3.0.2\zephyr\share\sysbuild\Kconfig.v2 C$:\ncs\v3.0.2\zephyr\share\sysbuild\build\Kconfig C$:\ncs\v3.0.2\zephyr\share\sysbuild\cmake\domains.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\cmake\modules\native_simulator_sb_extensions.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\cmake\modules\sysbuild_default.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\cmake\modules\sysbuild_extensions.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\cmake\modules\sysbuild_images.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\cmake\modules\sysbuild_kconfig.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\cmake\modules\sysbuild_root.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\cmake\modules\sysbuild_snippets.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\image_configurations\ALL_image_default.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\image_configurations\MAIN_image_default.cmake C$:\ncs\v3.0.2\zephyr\share\sysbuild\images\CMakeLists.txt C$:\ncs\v3.0.2\zephyr\share\sysbuild\images\Kconfig C$:\ncs\v3.0.2\zephyr\share\sysbuild\images\boards\CMakeLists.txt C$:\ncs\v3.0.2\zephyr\share\sysbuild\images\bootloader\CMakeLists.txt C$:\ncs\v3.0.2\zephyr\share\sysbuild\images\bootloader\Kconfig C$:\ncs\v3.0.2\zephyr\share\sysbuild\images\soc\CMakeLists.txt C$:\ncs\v3.0.2\zephyr\share\sysbuild\template\CMakeLists.txt C$:\ncs\v3.0.2\zephyr\share\zephyr-package\cmake\ZephyrConfig.cmake C$:\ncs\v3.0.2\zephyr\share\zephyr-package\cmake\ZephyrConfigVersion.cmake C$:\ncs\v3.0.2\zephyr\share\zephyr-package\cmake\zephyr_package_search.cmake C$:\ncs\v3.0.2\zephyr\snippets\bt-ll-sw-split\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\cdc-acm-console\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\nordic-flpr-xip\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\nordic-flpr\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\nordic-log-stm-dict\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\nordic-log-stm\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\nordic-ppr-xip\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\nordic-ppr\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\nus-console\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\ram-console\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\rtt-console\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\rtt-tracing\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\serial-console\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\wifi-enterprise\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\wifi-ipv4\snippet.yml C$:\ncs\v3.0.2\zephyr\snippets\xen_dom0\snippet.yml C$:\ncs\v3.0.2\zephyr\soc\Kconfig.v2 C$:\ncs\v3.0.2\zephyr\soc\adi\max32\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\altr\qemu_nios2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\altr\zephyr_nios2f\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ambiq\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ambiq\apollo3x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ambiq\apollo4x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\amd\acp_6_0\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\andestech\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\andestech\ae350\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\beetle\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\designstart\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\fvp_aemv8a\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\fvp_aemv8r\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\mps2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\mps3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\musca\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\qemu_cortex_a53\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\arm\qemu_virt_arm64\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\aspeed\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\aspeed\ast10x0\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\samc20\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\samc21\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\samd20\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\samd21\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\samd51\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\same51\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\same53\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\same54\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\saml21\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\samr21\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\samr34\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam0\samr35\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam\sam3x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam\sam4e\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam\sam4l\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam\sam4s\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam\same70\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\atmel\sam\samv71\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\brcm\bcm2711\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\brcm\bcm2712\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\brcm\bcmvk\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\brcm\bcmvk\valkyrie\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\brcm\bcmvk\viper\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\cdns\dc233c\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\cdns\sample_controller32\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\cdns\xtensa_sample_controller\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\efinix\sapphire\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ene\kb1200\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\espressif\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\espressif\Kconfig.sysbuild C$:\ncs\v3.0.2\zephyr\soc\espressif\esp32\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\espressif\esp32c2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\espressif\esp32c3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\espressif\esp32c6\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\espressif\esp32s2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\espressif\esp32s3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gaisler\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gaisler\gr716a\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gaisler\leon3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\gd32a50x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\gd32e10x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\gd32e50x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\gd32f3x0\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\gd32f403\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\gd32f4xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\gd32l23x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\gd\gd32\gd32vf103\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat1a\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat1a\psoc6_01\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat1a\psoc6_02\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat1a\psoc6_03\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat1a\psoc6_04\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat1a\psoc6_legacy\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat1b\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat1b\cyw20829\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\infineon\cat3\xmc4xxx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\alder_lake\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\apollo_lake\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\atom\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\elkhart_lake\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_adsp\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_adsp\ace\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_adsp\cavs\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_ish\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_ish\intel_ish5\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_niosv\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_niosv\niosv\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_socfpga\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_socfpga\agilex5\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_socfpga\agilex\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_socfpga_std\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\intel_socfpga_std\cyclonev\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\lakemont\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\intel\raptor_lake\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ite\ec\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ite\ec\it8xxx2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\litex\litex_vexriscv\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\lowrisc\opentitan\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\mediatek\mt8xxx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\mec\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\mec\mec15xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\mec\mec172x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\mec\mec174x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\mec\mec175x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\mec\mech172x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\miv\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\miv\miv\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\microchip\miv\polarfire\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\native\inf_clock\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\neorv32\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\Kconfig.sysbuild C$:\ncs\v3.0.2\zephyr\soc\nordic\common\vpr\Kconfig.sysbuild C$:\ncs\v3.0.2\zephyr\soc\nordic\nrf51\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\nrf52\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\nrf53\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\nrf54h\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\nrf54l\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\nrf91\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\nrf92\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nordic\sysbuild.cmake C$:\ncs\v3.0.2\zephyr\soc\nuvoton\npcm\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\npcm\npcm4\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\npcx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\npcx\npcx4\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\npcx\npcx7\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\npcx\npcx9\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\numaker\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\numaker\m2l31x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\numaker\m46x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\numicro\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nuvoton\numicro\m48x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx6sx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx7d\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx8\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx8m\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx8ulp\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx8x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx9\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx9\imx93\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imx\imx9\imx95\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imxrt\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imxrt\Kconfig.sysbuild C$:\ncs\v3.0.2\zephyr\soc\nxp\imxrt\imxrt10xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imxrt\imxrt118x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imxrt\imxrt11xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imxrt\imxrt5xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\imxrt\imxrt6xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\k2x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\k6x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\k8x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\ke1xf\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\ke1xz\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\kl2x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\kv5x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\kinetis\kwx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\layerscape\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\layerscape\ls1046a\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\lpc\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\lpc\lpc11u6x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\lpc\lpc51u68\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\lpc\lpc54xxx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\lpc\lpc55xxx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\mcx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\mcx\mcxa\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\mcx\mcxc\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\mcx\mcxn\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\mcx\mcxw\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\rw\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\s32\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\s32\s32k1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\s32\s32k3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\nxp\s32\s32ze\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\openisa\rv32m1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\qemu\malta\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\qemu\virt_riscv\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\qemu\virt_riscv\qemu_virt_riscv32\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\qemu\virt_riscv\qemu_virt_riscv32e\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\qemu\virt_riscv\qemu_virt_riscv64\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\quicklogic\eos_s3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\raspberrypi\rpi_pico\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\raspberrypi\rpi_pico\rp2040\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\raspberrypi\rpi_pico\rp2350\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\realtek\ec\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\realtek\ec\rts5912\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra2a1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra4e2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra4m1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra4m2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra4m3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra4w1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra6e1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra6e2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra6m1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra6m2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra6m3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra6m4\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra6m5\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra8d1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra8m1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\ra\ra8t1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\rcar\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\rcar\rcar_gen3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\rcar\rcar_gen4\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\rz\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\rz\rzg3s\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\rzt2m\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\smartbond\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renesas\smartbond\da1469x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renode\cortex_r8_virtual\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\renode\riscv_virtual\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\rockchip\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\rockchip\rk3399\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\rockchip\rk3568\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\sensry\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\sensry\ganymed\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\sensry\ganymed\sy1xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\sifive\sifive_freedom\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\sifive\sifive_freedom\fe300\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\sifive\sifive_freedom\fu500\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\sifive\sifive_freedom\fu700\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s0\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s0\efm32hg\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s0\efm32wg\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efm32gg11b\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efm32gg12b\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efm32jg12b\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efm32pg12b\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efm32pg1b\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efr32bg13p\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efr32fg13p\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efr32fg1p\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s1\efr32mg12p\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s2\efr32bg22\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s2\efr32bg27\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s2\efr32mg21\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s2\efr32mg24\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_s2\efr32zg23\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_sim3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\silabs\silabs_sim3\sim3u\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\arc_iot\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\emsdp\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\emsk\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\hsdk4xd\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\hsdk\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\nsim\arc_classic\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\nsim\arc_classic\em\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\nsim\arc_classic\hs\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\nsim\arc_classic\sem\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\nsim\arc_classic\vpx5\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\nsim\arc_v\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\nsim\arc_v\rmx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\snps\qemu_arc\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32c0x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32f0x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32f1x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32f2x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32f3x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32f4x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32f7x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32g0x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32g4x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32h5x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32h7rsx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32h7x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32l0x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32l1x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32l4x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32l5x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32mp1x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32u0x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32u5x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32wb0x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32wbax\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32wbx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\st\stm32\stm32wlx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\starfive\jh71xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\telink\tlsr\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\telink\tlsr\tlsr951x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ti\k3\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ti\k3\am6x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ti\lm3s6965\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ti\simplelink\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ti\simplelink\cc13x2_cc26x2\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ti\simplelink\cc13x2x7_cc26x2x7\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ti\simplelink\cc32xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\ti\simplelink\msp432p4xx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\wch\ch32v00x\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\xen\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\xlnx\zynq7000\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\xlnx\zynq7000\xc7zxxx\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\xlnx\zynq7000\xc7zxxxs\Kconfig.soc C$:\ncs\v3.0.2\zephyr\soc\xlnx\zynqmp\Kconfig.soc CMakeCache.txt CMakeFiles\3.21.0\CMakeSystem.cmake Kconfig\Kconfig.sysbuild.modules Kconfig\boards\Kconfig.nrf5340dk Kconfig\boards\Kconfig.sysbuild Kconfig\soc\Kconfig.soc Kconfig\soc\Kconfig.sysbuild Stethy_V18.00\CMakeCache.tmp Stethy_V18.00\zephyr\.config Stethy_V18.00\zephyr\dts.cmake _sysbuild\autoconf.h _sysbuild\empty.conf _sysbuild\sysbuild\images\Stethy_V18.00-prefix\tmp\Stethy_V18.00-cfgcmd.txt.in zephyr\.config zephyr\snippets_generated.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
