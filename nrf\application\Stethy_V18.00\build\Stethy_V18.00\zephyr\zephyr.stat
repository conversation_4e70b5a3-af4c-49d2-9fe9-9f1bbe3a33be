ELF Header:
  Magic:   7f 45 4c 46 01 01 01 00 00 00 00 00 00 00 00 00 
  Class:                             ELF32
  Data:                              2's complement, little endian
  Version:                           1 (current)
  OS/ABI:                            UNIX - System V
  ABI Version:                       0
  Type:                              EXEC (Executable file)
  Machine:                           ARM
  Version:                           0x1
  Entry point address:               0x2115
  Start of program headers:          52 (bytes into file)
  Start of section headers:          1336880 (bytes into file)
  Flags:                             0x5000200, Version5 EABI, soft-float ABI
  Size of this header:               52 (bytes)
  Size of program headers:           32 (bytes)
  Number of program headers:         6
  Size of section headers:           40 (bytes)
  Number of section headers:         40
  Section header string table index: 39

Section Headers:
  [Nr] Name              Type            Addr     Off    Size   ES Flg Lk Inf Al
  [ 0]                   NULL            00000000 000000 000000 00      0   0  0
  [ 1] rom_start         PROGBITS        00000000 000100 000154 00 WAX  0   0  4
  [ 2] text              PROGBITS        00000154 000254 0088dc 00  AX  0   0  4
  [ 3] .ARM.exidx        ARM_EXIDX       00008a30 008b30 000008 00  AL  2   0  4
  [ 4] initlevel         PROGBITS        00008a38 008b38 000088 00   A  0   0  4
  [ 5] device_area       PROGBITS        00008ac0 008bc0 000078 00   A  0   0  4
  [ 6] sw_isr_table      PROGBITS        00008b38 008c38 000228 00  WA  0   0  4
  [ 7] gpio_driver_[...] PROGBITS        00008d60 008e60 000024 00   A  0   0  4
  [ 8] i2s_driver_a[...] PROGBITS        00008d84 008e84 000014 00   A  0   0  4
  [ 9] clock_contro[...] PROGBITS        00008d98 008e98 00001c 00   A  0   0  4
  [10] led_strip_dr[...] PROGBITS        00008db4 008eb4 00000c 00   A  0   0  4
  [11] uart_driver_[...] PROGBITS        00008dc0 008ec0 00000c 00   A  0   0  4
  [12] log_const_area    PROGBITS        00008dcc 008ecc 000058 00   A  0   0  4
  [13] log_backend_area  PROGBITS        00008e24 008f24 000010 00   A  0   0  4
  [14] tbss              NOBITS          00008e34 008f34 000004 00 WAT  0   0  4
  [15] rodata            PROGBITS        00008e40 008f40 00105c 00   A  0   0 16
  [16] .ramfunc          PROGBITS        20000000 00a298 000000 00   W  0   0  1
  [17] datas             PROGBITS        20000000 009f9c 000234 00  WA  0   0  4
  [18] device_states     PROGBITS        20000234 00a1d0 00000c 00  WA  0   0  1
  [19] log_mpsc_pbu[...] PROGBITS        20000240 00a1dc 000038 00  WA  0   0  4
  [20] log_msg_ptr_area  PROGBITS        20000278 00a214 000004 00  WA  0   0  4
  [21] k_mem_slab_area   PROGBITS        2000027c 00a218 00001c 00  WA  0   0  4
  [22] k_mutex_area      PROGBITS        20000298 00a234 000050 00  WA  0   0  4
  [23] k_sem_area        PROGBITS        200002e8 00a284 000010 00  WA  0   0  4
  [24] .comment          PROGBITS        00000000 00a298 000040 01  MS  0   0  1
  [25] .debug_aranges    PROGBITS        00000000 00a2d8 001aa0 00      0   0  8
  [26] .debug_info       PROGBITS        00000000 00bd78 09c6fc 00      0   0  1
  [27] .debug_abbrev     PROGBITS        00000000 0a8474 01167d 00      0   0  1
  [28] .debug_line       PROGBITS        00000000 0b9af1 02dc61 00      0   0  1
  [29] .debug_frame      PROGBITS        00000000 0e7754 0041f4 00      0   0  4
  [30] .debug_str        PROGBITS        00000000 0eb948 00fdc0 01  MS  0   0  1
  [31] .debug_loc        PROGBITS        00000000 0fb708 02f4b4 00      0   0  1
  [32] .debug_ranges     PROGBITS        00000000 12abc0 008348 00      0   0  8
  [33] .ARM.attributes   ARM_ATTRIBUTES  00000000 132f08 000038 00      0   0  1
  [34] .last_section     PROGBITS        0000a194 00a294 000004 00  WA  0   0  4
  [35] bss               NOBITS          200002f8 00a298 000fa2 00  WA  0   0  8
  [36] noinit            NOBITS          200012a0 00a298 0015c8 00  WA  0   0  8
  [37] .symtab           SYMTAB          00000000 132f40 00a470 10     38 1423  4
  [38] .strtab           STRTAB          00000000 13d3b0 009089 00      0   0  1
  [39] .shstrtab         STRTAB          00000000 146439 0001f7 00      0   0  1
Key to Flags:
  W (write), A (alloc), X (execute), M (merge), S (strings), I (info),
  L (link order), O (extra OS processing required), G (group), T (TLS),
  C (compressed), x (unknown), o (OS specific), E (exclude),
  D (mbind), y (purecode), p (processor specific)

Program Headers:
  Type           Offset   VirtAddr   PhysAddr   FileSiz MemSiz  Flg Align
  EXIDX          0x008b30 0x00008a30 0x00008a30 0x00008 0x00008 R   0x4
  LOAD           0x000100 0x00000000 0x00000000 0x09e9c 0x09e9c RWE 0x10
  LOAD           0x009f9c 0x20000000 0x00009e9c 0x002f8 0x002f8 RW  0x4
  LOAD           0x00a294 0x0000a194 0x0000a194 0x00004 0x00004 RW  0x4
  LOAD           0x000000 0x200002f8 0x200002f8 0x00000 0x02570 RW  0x8
  TLS            0x008f34 0x00008e34 0x00008e34 0x00000 0x00004 R   0x4

 Section to Segment mapping:
  Segment Sections...
   00     .ARM.exidx 
   01     rom_start text .ARM.exidx initlevel device_area sw_isr_table gpio_driver_api_area i2s_driver_api_area clock_control_driver_api_area led_strip_driver_api_area uart_driver_api_area log_const_area log_backend_area rodata 
   02     datas device_states log_mpsc_pbuf_area log_msg_ptr_area k_mem_slab_area k_mutex_area k_sem_area 
   03     .last_section 
   04     bss noinit 
   05     tbss 
