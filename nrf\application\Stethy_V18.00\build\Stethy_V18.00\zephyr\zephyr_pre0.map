Archive member included to satisfy reference by file (symbol)

app/libapp.a(main.c.obj)      (--whole-archive)
app/libapp.a(button.c.obj)    (--whole-archive)
zephyr/libzephyr.a(heap.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(cbprintf_packaged.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(printk.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(sem.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(thread_entry.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(cbprintf_complete.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(assert.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(mpsc_pbuf.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(dec.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(hex.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(rb.c.obj)  (--whole-archive)
zephyr/libzephyr.a(timeutil.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(bitarray.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(onoff.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(notify.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(last_section_id.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(configs.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(log_core.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(log_mgmt.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(log_cache.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(log_msg.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(log_output.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(log_backend_uart.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(mem_attr.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(tracing_none.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(banner.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(flash_map_partition_manager.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
                              (--whole-archive)
zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                              (--whole-archive)
zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                              (--whole-archive)
zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
                              (--whole-archive)
zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(assert.c.obj)
                              (--whole-archive)
zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(cbprintf.c.obj)
                              (--whole-archive)
zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(chk_fail.c.obj)
                              (--whole-archive)
zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(errno_wrap.c.obj)
                              (--whole-archive)
zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(exit.c.obj)
                              (--whole-archive)
zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
                              (--whole-archive)
zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
                              (--whole-archive)
zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
                              (--whole-archive)
zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
                              (--whole-archive)
zephyr/soc/soc/nrf5340/libsoc__nordic.a(reboot.c.obj)
                              (--whole-archive)
zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
                              (--whole-archive)
zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
                              (--whole-archive)
zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
                              (--whole-archive)
zephyr/drivers/i2s/libdrivers__i2s.a(i2s_common.c.obj)
                              (--whole-archive)
zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
                              (--whole-archive)
zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
                              (--whole-archive)
zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
                              (--whole-archive)
zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
                              (--whole-archive)
zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
                              (--whole-archive)
zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
                              (--whole-archive)
zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                              (--whole-archive)
modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
                              (--whole-archive)
modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
                              (--whole-archive)
modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
                              (--whole-archive)
modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
                              (--whole-archive)
modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
                              (--whole-archive)
modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                              (--whole-archive)
modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_ppi.c.obj)
                              (--whole-archive)
modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                              (--whole-archive)
modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
                              (--whole-archive)
modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                              (--whole-archive)
modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
                              (--whole-archive)
zephyr/kernel/libkernel.a(busy_wait.c.obj)
                              modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj) (z_impl_k_busy_wait)
zephyr/kernel/libkernel.a(device.c.obj)
                              app/libapp.a(main.c.obj) (z_impl_device_is_ready)
zephyr/kernel/libkernel.a(fatal.c.obj)
                              zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj) (z_fatal_error)
zephyr/kernel/libkernel.a(init.c.obj)
                              zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj) (z_sys_post_kernel)
zephyr/kernel/libkernel.a(init_static.c.obj)
                              zephyr/kernel/libkernel.a(init.c.obj) (z_init_static)
zephyr/kernel/libkernel.a(mem_slab.c.obj)
                              zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj) (k_mem_slab_init)
zephyr/kernel/libkernel.a(idle.c.obj)
                              zephyr/kernel/libkernel.a(init.c.obj) (idle)
zephyr/kernel/libkernel.a(msg_q.c.obj)
                              zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj) (k_msgq_init)
zephyr/kernel/libkernel.a(mutex.c.obj)
                              zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj) (z_impl_k_mutex_init)
zephyr/kernel/libkernel.a(sem.c.obj)
                              zephyr/libzephyr.a(sem.c.obj) (z_impl_k_sem_init)
zephyr/kernel/libkernel.a(thread.c.obj)
                              zephyr/libzephyr.a(mpsc_pbuf.c.obj) (k_is_in_isr)
zephyr/kernel/libkernel.a(sched.c.obj)
                              zephyr/kernel/libkernel.a(init.c.obj) (z_ready_thread)
zephyr/kernel/libkernel.a(timeslicing.c.obj)
                              zephyr/kernel/libkernel.a(sched.c.obj) (z_reset_time_slice)
zephyr/kernel/libkernel.a(xip.c.obj)
                              zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj) (z_data_copy)
zephyr/kernel/libkernel.a(timeout.c.obj)
                              zephyr/kernel/libkernel.a(init.c.obj) (z_add_timeout)
zephyr/kernel/libkernel.a(timer.c.obj)
                              zephyr/libzephyr.a(log_core.c.obj) (k_timer_init)
zephyr/kernel/libkernel.a(mempool.c.obj)
                              zephyr/kernel/libkernel.a(msg_q.c.obj) (k_free)
zephyr/kernel/libkernel.a(kheap.c.obj)
                              zephyr/kernel/libkernel.a(mempool.c.obj) (k_heap_aligned_alloc)
zephyr/arch/common/libisr_tables.a(isr_tables.c.obj)
                              zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj) (_sw_isr_table)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
                              modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj) (nrf_cc3xx_platform_init_no_rng)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
                              zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj) (platform_abort_apis)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
                              zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj) (nrf_cc3xx_platform_set_mutexes)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj) (CC_LibInitNoRng)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (CC_HalInit)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (CC_PalInit)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj) (CC_PalDmaInit)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj) (CC_PalWaitInterruptRND)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj) (CC_PalMemCopyPlat)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj) (CC_PalMutexCreate)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj) (CC_PalPowerSaveModeInit)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (nrf_cc3xx_platform_ctr_drbg_init)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (nrf_cc3xx_platform_hmac_drbg_init)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj) (cc_mbedtls_platform_zeroize)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (LLF_RND_RunTrngStartupTest)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj) (cc_mbedtls_ctr_drbg_init)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj) (cc_mbedtls_entropy_init)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj) (cc_mbedtls_hmac_drbg_init)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj) (RNG_PLAT_SetUserRngParameters)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj) (CC_PalTrngParamGet)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj) (mbedtls_mutex_unlock)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj) (LLF_RND_WaitRngInterrupt)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj) (mbedtls_hardware_poll)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj) (cc_mbedtls_aes_init)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj) (cc_mbedtls_sha256_init)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj) (mbedtls_sha_process_internal)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj) (cc_mbedtls_sha256)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj) (SetDataBuffersInfo)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj) (InitHashDrv)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj) (ProcessAesDrv)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj) (kmu_validate_slot_and_size)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj) (write_invalid_chacha20_key)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj) (UtilCmacBuildDataForDerivation)
C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj) (CC_PalDataBufferAttrGet)
c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strcmp.S.o)
                              zephyr/libzephyr.a(log_mgmt.c.obj) (strcmp)
c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memcpy.c.o)
                              zephyr/libzephyr.a(heap.c.obj) (memcpy)
c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memmove.c.o)
                              C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj) (memmove)
c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memset.c.o)
                              zephyr/libzephyr.a(heap.c.obj) (memset)
c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strlen.S.o)
                              zephyr/libzephyr.a(cbprintf_packaged.c.obj) (strlen)
c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_errno_errno.c.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (errno)
c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_memcmp.c.o)
                              zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj) (memcmp)
c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strnlen.c.o)
                              zephyr/libzephyr.a(cbprintf_complete.c.obj) (strnlen)
c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_puts.c.o)
                              zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(exit.c.obj) (puts)
c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_snprintf.c.o)
                              zephyr/libzephyr.a(log_output.c.obj) (snprintf)
c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
                              (__l_vfprintf)
c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
                              (__l_vfscanf)
c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strchr.c.o)
                              c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o) (strchr)
c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_fgetc.c.o)
                              c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o) (fgetc)
c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_filestrput.c.o)
                              c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_snprintf.c.o) (__file_str_put)
c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_ungetc.c.o)
                              c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o) (ungetc)
c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
                              zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj) (cmse_check_address_range)
c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldf3.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_dmul)
c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubdf3.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_dsub)
c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivdf3.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_ddiv)
c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_truncdfsf2.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_d2f)
c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpsf2.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_fcmpeq)
c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_ldivmod.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_ldivmod)
c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
                              zephyr/libzephyr.a(cbprintf_complete.c.obj) (__aeabi_uldivmod)
c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_popcountsi2.o)
                              zephyr/libzephyr.a(bitarray.c.obj) (__popcountsi2)
c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixdfdi.o)
                              zephyr/libzephyr.a(timeutil.c.obj) (__aeabi_d2lz)
c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixunsdfdi.o)
                              c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixdfdi.o) (__aeabi_d2ulz)
c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
                              c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_ldivmod.o) (__udivmoddi4)
c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_dvmd_tls.o)
                              c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_ldivmod.o) (__aeabi_ldiv0)
c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpdf2.o)
                              c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixdfdi.o) (__aeabi_dcmplt)
c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunsdfsi.o)
                              c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixunsdfdi.o) (__aeabi_d2uiz)

Discarded input sections

 .text          0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_pre0.dir/misc/empty_file.c.obj
 .data          0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_pre0.dir/misc/empty_file.c.obj
 .bss           0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_pre0.dir/misc/empty_file.c.obj
 .debug_line    0x0000000000000000        0x0 zephyr/CMakeFiles/zephyr_pre0.dir/misc/empty_file.c.obj
 .debug_str     0x0000000000000000      0x20a zephyr/CMakeFiles/zephyr_pre0.dir/misc/empty_file.c.obj
 .comment       0x0000000000000000       0x21 zephyr/CMakeFiles/zephyr_pre0.dir/misc/empty_file.c.obj
 .text          0x0000000000000000        0x0 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .data          0x0000000000000000        0x0 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .bss           0x0000000000000000        0x0 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .text          0x0000000000000000        0x0 app/libapp.a(main.c.obj)
 .data          0x0000000000000000        0x0 app/libapp.a(main.c.obj)
 .bss           0x0000000000000000        0x0 app/libapp.a(main.c.obj)
 .text          0x0000000000000000        0x0 app/libapp.a(button.c.obj)
 .data          0x0000000000000000        0x0 app/libapp.a(button.c.obj)
 .bss           0x0000000000000000        0x0 app/libapp.a(button.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(heap.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(heap.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(heap.c.obj)
 .text.mem_to_chunkid
                0x0000000000000000       0x14 zephyr/libzephyr.a(heap.c.obj)
 .text.free_list_remove_bidx
                0x0000000000000000       0x5c zephyr/libzephyr.a(heap.c.obj)
 .text.free_list_remove
                0x0000000000000000       0x2e zephyr/libzephyr.a(heap.c.obj)
 .text.alloc_chunk
                0x0000000000000000       0x74 zephyr/libzephyr.a(heap.c.obj)
 .text.split_chunks
                0x0000000000000000       0x4e zephyr/libzephyr.a(heap.c.obj)
 .text.merge_chunks
                0x0000000000000000       0x3c zephyr/libzephyr.a(heap.c.obj)
 .text.free_chunk
                0x0000000000000000       0x84 zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_free
                0x0000000000000000       0x24 zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_usable_size
                0x0000000000000000       0x22 zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_alloc
                0x0000000000000000       0x6e zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_aligned_alloc
                0x0000000000000000       0xe8 zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_aligned_realloc
                0x0000000000000000      0x144 zephyr/libzephyr.a(heap.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .text.cbprintf_package
                0x0000000000000000       0x1e zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(printk.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(printk.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(printk.c.obj)
 .text.__printk_get_hook
                0x0000000000000000        0xc zephyr/libzephyr.a(printk.c.obj)
 .text.vprintk  0x0000000000000000        0x4 zephyr/libzephyr.a(printk.c.obj)
 .text.z_impl_k_str_out
                0x0000000000000000       0x1c zephyr/libzephyr.a(printk.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(sem.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(sem.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(sem.c.obj)
 .text.sys_sem_init
                0x0000000000000000        0xa zephyr/libzephyr.a(sem.c.obj)
 .text.sys_sem_give
                0x0000000000000000        0xa zephyr/libzephyr.a(sem.c.obj)
 .text.sys_sem_take
                0x0000000000000000       0x1e zephyr/libzephyr.a(sem.c.obj)
 .text.sys_sem_count_get
                0x0000000000000000        0x4 zephyr/libzephyr.a(sem.c.obj)
 .debug_info    0x0000000000000000      0x4dd zephyr/libzephyr.a(sem.c.obj)
 .debug_abbrev  0x0000000000000000      0x22a zephyr/libzephyr.a(sem.c.obj)
 .debug_loc     0x0000000000000000      0x21a zephyr/libzephyr.a(sem.c.obj)
 .debug_aranges
                0x0000000000000000       0x38 zephyr/libzephyr.a(sem.c.obj)
 .debug_ranges  0x0000000000000000       0x70 zephyr/libzephyr.a(sem.c.obj)
 .debug_line    0x0000000000000000      0x34f zephyr/libzephyr.a(sem.c.obj)
 .debug_str     0x0000000000000000      0x408 zephyr/libzephyr.a(sem.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(sem.c.obj)
 .debug_frame   0x0000000000000000       0x68 zephyr/libzephyr.a(sem.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(thread_entry.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(thread_entry.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(thread_entry.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .text.encode_uint
                0x0000000000000000       0x90 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .text.outs     0x0000000000000000       0x2e zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .rodata.z_cbvprintf_impl.str1.1
                0x0000000000000000        0x6 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .text.z_cbvprintf_impl
                0x0000000000000000      0x758 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_info    0x0000000000000000     0x10a3 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_abbrev  0x0000000000000000      0x3f7 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_loc     0x0000000000000000     0x12db zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_aranges
                0x0000000000000000       0x30 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_ranges  0x0000000000000000      0x1c8 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_line    0x0000000000000000      0xcfc zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_str     0x0000000000000000      0x6d2 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .debug_frame   0x0000000000000000       0x88 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(assert.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(assert.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(assert.c.obj)
 .text.assert_post_action
                0x0000000000000000       0x12 zephyr/libzephyr.a(assert.c.obj)
 .text.assert_print
                0x0000000000000000       0x1a zephyr/libzephyr.a(assert.c.obj)
 .debug_info    0x0000000000000000      0x247 zephyr/libzephyr.a(assert.c.obj)
 .debug_abbrev  0x0000000000000000      0x1ca zephyr/libzephyr.a(assert.c.obj)
 .debug_loc     0x0000000000000000       0x51 zephyr/libzephyr.a(assert.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/libzephyr.a(assert.c.obj)
 .debug_ranges  0x0000000000000000       0x18 zephyr/libzephyr.a(assert.c.obj)
 .debug_line    0x0000000000000000      0x2ef zephyr/libzephyr.a(assert.c.obj)
 .debug_str     0x0000000000000000      0x3c2 zephyr/libzephyr.a(assert.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(assert.c.obj)
 .debug_frame   0x0000000000000000       0x50 zephyr/libzephyr.a(assert.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .text.mpsc_pbuf_put_word
                0x0000000000000000       0xbe zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .text.mpsc_pbuf_put_word_ext
                0x0000000000000000       0xf0 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .text.mpsc_pbuf_put_data
                0x0000000000000000       0xea zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .text.mpsc_pbuf_get_utilization
                0x0000000000000000       0x16 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .text.mpsc_pbuf_get_max_utilization
                0x0000000000000000       0x16 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(dec.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(dec.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(dec.c.obj)
 .text.u8_to_dec
                0x0000000000000000       0x52 zephyr/libzephyr.a(dec.c.obj)
 .debug_info    0x0000000000000000      0x12b zephyr/libzephyr.a(dec.c.obj)
 .debug_abbrev  0x0000000000000000       0x9c zephyr/libzephyr.a(dec.c.obj)
 .debug_loc     0x0000000000000000      0x128 zephyr/libzephyr.a(dec.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/libzephyr.a(dec.c.obj)
 .debug_ranges  0x0000000000000000       0x10 zephyr/libzephyr.a(dec.c.obj)
 .debug_line    0x0000000000000000      0x1bb zephyr/libzephyr.a(dec.c.obj)
 .debug_str     0x0000000000000000      0x2df zephyr/libzephyr.a(dec.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(dec.c.obj)
 .debug_frame   0x0000000000000000       0x30 zephyr/libzephyr.a(dec.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(hex.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(hex.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(hex.c.obj)
 .text.char2hex
                0x0000000000000000       0x30 zephyr/libzephyr.a(hex.c.obj)
 .text.hex2char
                0x0000000000000000       0x1c zephyr/libzephyr.a(hex.c.obj)
 .text.bin2hex  0x0000000000000000       0x54 zephyr/libzephyr.a(hex.c.obj)
 .text.hex2bin  0x0000000000000000       0x70 zephyr/libzephyr.a(hex.c.obj)
 .debug_info    0x0000000000000000      0x29d zephyr/libzephyr.a(hex.c.obj)
 .debug_abbrev  0x0000000000000000      0x117 zephyr/libzephyr.a(hex.c.obj)
 .debug_loc     0x0000000000000000      0x2f4 zephyr/libzephyr.a(hex.c.obj)
 .debug_aranges
                0x0000000000000000       0x38 zephyr/libzephyr.a(hex.c.obj)
 .debug_ranges  0x0000000000000000       0x40 zephyr/libzephyr.a(hex.c.obj)
 .debug_line    0x0000000000000000      0x316 zephyr/libzephyr.a(hex.c.obj)
 .debug_str     0x0000000000000000      0x2e6 zephyr/libzephyr.a(hex.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(hex.c.obj)
 .debug_frame   0x0000000000000000       0x74 zephyr/libzephyr.a(hex.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(rb.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(rb.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(rb.c.obj)
 .text.find_and_stack
                0x0000000000000000       0x3a zephyr/libzephyr.a(rb.c.obj)
 .text.stack_left_limb
                0x0000000000000000       0x40 zephyr/libzephyr.a(rb.c.obj)
 .text.set_child
                0x0000000000000000       0x12 zephyr/libzephyr.a(rb.c.obj)
 .text.rotate   0x0000000000000000       0xa8 zephyr/libzephyr.a(rb.c.obj)
 .text.z_rb_get_minmax
                0x0000000000000000       0x1a zephyr/libzephyr.a(rb.c.obj)
 .text.rb_insert
                0x0000000000000000      0x134 zephyr/libzephyr.a(rb.c.obj)
 .text.rb_remove
                0x0000000000000000      0x344 zephyr/libzephyr.a(rb.c.obj)
 .text.z_rb_walk
                0x0000000000000000       0x24 zephyr/libzephyr.a(rb.c.obj)
 .text.z_rb_child
                0x0000000000000000        0xe zephyr/libzephyr.a(rb.c.obj)
 .text.z_rb_is_black
                0x0000000000000000        0x8 zephyr/libzephyr.a(rb.c.obj)
 .text.rb_contains
                0x0000000000000000       0x2c zephyr/libzephyr.a(rb.c.obj)
 .text.z_rb_foreach_next
                0x0000000000000000       0x56 zephyr/libzephyr.a(rb.c.obj)
 .debug_info    0x0000000000000000     0x24dc zephyr/libzephyr.a(rb.c.obj)
 .debug_abbrev  0x0000000000000000      0x491 zephyr/libzephyr.a(rb.c.obj)
 .debug_loc     0x0000000000000000     0x2796 zephyr/libzephyr.a(rb.c.obj)
 .debug_aranges
                0x0000000000000000       0x78 zephyr/libzephyr.a(rb.c.obj)
 .debug_ranges  0x0000000000000000      0x5d8 zephyr/libzephyr.a(rb.c.obj)
 .debug_line    0x0000000000000000     0x1375 zephyr/libzephyr.a(rb.c.obj)
 .debug_str     0x0000000000000000      0x4d6 zephyr/libzephyr.a(rb.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(rb.c.obj)
 .debug_frame   0x0000000000000000      0x17c zephyr/libzephyr.a(rb.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(timeutil.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(timeutil.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_timegm64
                0x0000000000000000       0xe8 zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_timegm
                0x0000000000000000       0x20 zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_state_update
                0x0000000000000000       0x5c zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_state_set_skew
                0x0000000000000000       0x3a zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_estimate_skew
                0x0000000000000000       0x96 zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_ref_from_local
                0x0000000000000000       0xae zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_local_from_ref
                0x0000000000000000       0xa8 zephyr/libzephyr.a(timeutil.c.obj)
 .text.timeutil_sync_skew_to_ppb
                0x0000000000000000       0x3c zephyr/libzephyr.a(timeutil.c.obj)
 .debug_info    0x0000000000000000      0x8d5 zephyr/libzephyr.a(timeutil.c.obj)
 .debug_abbrev  0x0000000000000000      0x1f1 zephyr/libzephyr.a(timeutil.c.obj)
 .debug_loc     0x0000000000000000      0x738 zephyr/libzephyr.a(timeutil.c.obj)
 .debug_aranges
                0x0000000000000000       0x58 zephyr/libzephyr.a(timeutil.c.obj)
 .debug_ranges  0x0000000000000000       0xc0 zephyr/libzephyr.a(timeutil.c.obj)
 .debug_line    0x0000000000000000      0x610 zephyr/libzephyr.a(timeutil.c.obj)
 .debug_str     0x0000000000000000      0x5af zephyr/libzephyr.a(timeutil.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(timeutil.c.obj)
 .debug_frame   0x0000000000000000      0x11c zephyr/libzephyr.a(timeutil.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(bitarray.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(bitarray.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(bitarray.c.obj)
 .text.setup_bundle_data.constprop.0
                0x0000000000000000       0x3a zephyr/libzephyr.a(bitarray.c.obj)
 .text.set_region
                0x0000000000000000       0x90 zephyr/libzephyr.a(bitarray.c.obj)
 .text.set_clear_region
                0x0000000000000000       0x46 zephyr/libzephyr.a(bitarray.c.obj)
 .text.match_region
                0x0000000000000000       0x8c zephyr/libzephyr.a(bitarray.c.obj)
 .text.is_region_set_clear
                0x0000000000000000       0x4e zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_popcount_region
                0x0000000000000000       0x94 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_xor
                0x0000000000000000       0xc2 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_set_bit
                0x0000000000000000       0x3e zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_clear_bit
                0x0000000000000000       0x40 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_test_bit
                0x0000000000000000       0x3e zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_test_and_set_bit
                0x0000000000000000       0x4e zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_test_and_clear_bit
                0x0000000000000000       0x50 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_alloc
                0x0000000000000000       0xa4 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_find_nth_set
                0x0000000000000000       0xba zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_free
                0x0000000000000000       0x6c zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_is_region_set
                0x0000000000000000        0x6 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_is_region_cleared
                0x0000000000000000        0x6 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_test_and_set_region
                0x0000000000000000       0x70 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_set_region
                0x0000000000000000        0x6 zephyr/libzephyr.a(bitarray.c.obj)
 .text.sys_bitarray_clear_region
                0x0000000000000000        0x6 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_info    0x0000000000000000     0x2562 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_abbrev  0x0000000000000000      0x476 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_loc     0x0000000000000000     0x1e0f zephyr/libzephyr.a(bitarray.c.obj)
 .debug_aranges
                0x0000000000000000       0xb8 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_ranges  0x0000000000000000      0x288 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_line    0x0000000000000000     0x177c zephyr/libzephyr.a(bitarray.c.obj)
 .debug_str     0x0000000000000000      0x6b6 zephyr/libzephyr.a(bitarray.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(bitarray.c.obj)
 .debug_frame   0x0000000000000000      0x290 zephyr/libzephyr.a(bitarray.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(onoff.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(onoff.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(onoff.c.obj)
 .text.sys_slist_find_and_remove
                0x0000000000000000       0x36 zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_reset
                0x0000000000000000       0x62 zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_cancel
                0x0000000000000000       0x36 zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_monitor_register
                0x0000000000000000       0x3a zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_monitor_unregister
                0x0000000000000000       0x34 zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_sync_lock
                0x0000000000000000       0x14 zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_sync_finalize
                0x0000000000000000       0x40 zephyr/libzephyr.a(onoff.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(notify.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(notify.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(notify.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(last_section_id.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(last_section_id.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(last_section_id.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(configs.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(configs.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(configs.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(log_core.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(log_core.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(log_core.c.obj)
 .text.log_format_table_size
                0x0000000000000000        0x4 zephyr/libzephyr.a(log_core.c.obj)
 .text.z_log_timestamp
                0x0000000000000000        0xc zephyr/libzephyr.a(log_core.c.obj)
 .text.log_format_set_all_active_backends
                0x0000000000000000       0x4c zephyr/libzephyr.a(log_core.c.obj)
 .text.log_init
                0x0000000000000000        0x8 zephyr/libzephyr.a(log_core.c.obj)
 .text.log_thread_trigger
                0x0000000000000000       0x1c zephyr/libzephyr.a(log_core.c.obj)
 .text.log_thread_set
                0x0000000000000000        0x2 zephyr/libzephyr.a(log_core.c.obj)
 .rodata.unordered_notify.str1.1
                0x0000000000000000       0x28 zephyr/libzephyr.a(log_core.c.obj)
 .text.unordered_notify
                0x0000000000000000       0x28 zephyr/libzephyr.a(log_core.c.obj)
 .text.z_impl_log_buffered_cnt
                0x0000000000000000        0xc zephyr/libzephyr.a(log_core.c.obj)
 .text.z_log_dropped_pending
                0x0000000000000000       0x14 zephyr/libzephyr.a(log_core.c.obj)
 .text.z_log_msg_claim_oldest
                0x0000000000000000       0x7c zephyr/libzephyr.a(log_core.c.obj)
 .text.z_log_msg_claim
                0x0000000000000000        0x4 zephyr/libzephyr.a(log_core.c.obj)
 .text.z_log_msg_enqueue
                0x0000000000000000       0x6c zephyr/libzephyr.a(log_core.c.obj)
 .text.log_set_tag
                0x0000000000000000        0x6 zephyr/libzephyr.a(log_core.c.obj)
 .text.log_mem_get_usage
                0x0000000000000000       0x14 zephyr/libzephyr.a(log_core.c.obj)
 .text.log_mem_get_max_usage
                0x0000000000000000        0xc zephyr/libzephyr.a(log_core.c.obj)
 .bss.unordered_cnt
                0x0000000000000000        0x4 zephyr/libzephyr.a(log_core.c.obj)
 .bss.prev_timestamp
                0x0000000000000000        0x4 zephyr/libzephyr.a(log_core.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(log_mgmt.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(log_mgmt.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.domain_id_cmp
                0x0000000000000000        0x8 zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.source_id_cmp
                0x0000000000000000        0xe zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.z_log_link_get_dynamic_filter
                0x0000000000000000       0x40 zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.z_log_ext_domain_count
                0x0000000000000000       0x20 zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.log_src_cnt_get
                0x0000000000000000       0x14 zephyr/libzephyr.a(log_mgmt.c.obj)
 .rodata.log_domain_name_get.str1.1
                0x0000000000000000        0x1 zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.log_domain_name_get
                0x0000000000000000        0x8 zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.log_compiled_level_get
                0x0000000000000000       0x20 zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.z_log_link_set_runtime_level
                0x0000000000000000       0x40 zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.z_log_runtime_filters_init
                0x0000000000000000       0x3c zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.log_source_id_get
                0x0000000000000000       0x38 zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.filter_set
                0x0000000000000000       0x20 zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.z_impl_log_filter_set
                0x0000000000000000       0x20 zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.z_impl_log_frontend_filter_set
                0x0000000000000000       0x20 zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.log_backend_get_by_name
                0x0000000000000000       0x2c zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.log_backend_disable
                0x0000000000000000        0x8 zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.log_filter_get
                0x0000000000000000       0x20 zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.log_frontend_filter_get
                0x0000000000000000        0x4 zephyr/libzephyr.a(log_mgmt.c.obj)
 .text.z_log_links_initiate
                0x0000000000000000       0x4c zephyr/libzephyr.a(log_mgmt.c.obj)
 .rodata.sname_cache_config.0
                0x0000000000000000       0x10 zephyr/libzephyr.a(log_mgmt.c.obj)
 .rodata.dname_cache_config.1
                0x0000000000000000       0x10 zephyr/libzephyr.a(log_mgmt.c.obj)
 .bss.sname_cache
                0x0000000000000000       0x20 zephyr/libzephyr.a(log_mgmt.c.obj)
 .bss.dname_cache
                0x0000000000000000       0x20 zephyr/libzephyr.a(log_mgmt.c.obj)
 .bss.sname_cache_buffer
                0x0000000000000000        0x9 zephyr/libzephyr.a(log_mgmt.c.obj)
 .bss.dname_cache_buffer
                0x0000000000000000        0x9 zephyr/libzephyr.a(log_mgmt.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(log_cache.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(log_cache.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(log_cache.c.obj)
 .text.log_cache_init
                0x0000000000000000       0x54 zephyr/libzephyr.a(log_cache.c.obj)
 .text.log_cache_get
                0x0000000000000000       0xa8 zephyr/libzephyr.a(log_cache.c.obj)
 .text.log_cache_put
                0x0000000000000000       0x14 zephyr/libzephyr.a(log_cache.c.obj)
 .text.log_cache_release
                0x0000000000000000       0x14 zephyr/libzephyr.a(log_cache.c.obj)
 .debug_info    0x0000000000000000     0x10a5 zephyr/libzephyr.a(log_cache.c.obj)
 .debug_abbrev  0x0000000000000000      0x2d5 zephyr/libzephyr.a(log_cache.c.obj)
 .debug_loc     0x0000000000000000      0xa61 zephyr/libzephyr.a(log_cache.c.obj)
 .debug_aranges
                0x0000000000000000       0x38 zephyr/libzephyr.a(log_cache.c.obj)
 .debug_ranges  0x0000000000000000      0x280 zephyr/libzephyr.a(log_cache.c.obj)
 .debug_line    0x0000000000000000      0x800 zephyr/libzephyr.a(log_cache.c.obj)
 .debug_str     0x0000000000000000      0x533 zephyr/libzephyr.a(log_cache.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(log_cache.c.obj)
 .debug_frame   0x0000000000000000       0x70 zephyr/libzephyr.a(log_cache.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(log_msg.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(log_msg.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(log_msg.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(log_output.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(log_output.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(log_output.c.obj)
 .text.log_output_timestamp_to_us
                0x0000000000000000       0x28 zephyr/libzephyr.a(log_output.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(log_backend_uart.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(log_backend_uart.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(log_backend_uart.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(mem_attr.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(mem_attr.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(mem_attr.c.obj)
 .text.mem_attr_check_buf
                0x0000000000000000        0xe zephyr/libzephyr.a(mem_attr.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(tracing_none.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(tracing_none.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(tracing_none.c.obj)
 .text.sys_trace_isr_enter
                0x0000000000000000        0x2 zephyr/libzephyr.a(tracing_none.c.obj)
 .text.sys_trace_isr_exit
                0x0000000000000000        0x2 zephyr/libzephyr.a(tracing_none.c.obj)
 .text.sys_trace_isr_exit_to_scheduler
                0x0000000000000000        0x2 zephyr/libzephyr.a(tracing_none.c.obj)
 .text.sys_trace_idle
                0x0000000000000000        0x2 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_info    0x0000000000000000       0x56 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_abbrev  0x0000000000000000       0x48 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_aranges
                0x0000000000000000       0x38 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_ranges  0x0000000000000000       0x28 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_line    0x0000000000000000       0x98 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_str     0x0000000000000000      0x26c zephyr/libzephyr.a(tracing_none.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(tracing_none.c.obj)
 .debug_frame   0x0000000000000000       0x50 zephyr/libzephyr.a(tracing_none.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(banner.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(banner.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(banner.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(flash_map_partition_manager.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(flash_map_partition_manager.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(flash_map_partition_manager.c.obj)
 .bss.flash_map
                0x0000000000000000        0x4 zephyr/libzephyr.a(flash_map_partition_manager.c.obj)
 .rodata.flash_map_entries
                0x0000000000000000        0x4 zephyr/libzephyr.a(flash_map_partition_manager.c.obj)
 .debug_info    0x0000000000000000      0x204 zephyr/libzephyr.a(flash_map_partition_manager.c.obj)
 .debug_abbrev  0x0000000000000000       0xd0 zephyr/libzephyr.a(flash_map_partition_manager.c.obj)
 .debug_aranges
                0x0000000000000000       0x18 zephyr/libzephyr.a(flash_map_partition_manager.c.obj)
 .debug_line    0x0000000000000000      0x221 zephyr/libzephyr.a(flash_map_partition_manager.c.obj)
 .debug_str     0x0000000000000000      0x392 zephyr/libzephyr.a(flash_map_partition_manager.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/libzephyr.a(flash_map_partition_manager.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .text          0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data          0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.mbedtls_threading_psa_rngdata_mutex
                0x0000000000000000        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.mbedtls_threading_psa_globaldata_mutex
                0x0000000000000000        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.mbedtls_threading_key_slot_mutex
                0x0000000000000000        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.heap_mutex
                0x0000000000000000        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 ._k_mutex.static.psa_rng_mutex_int_
                0x0000000000000000       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 ._k_mutex.static.psa_globaldata_mutex_int_
                0x0000000000000000       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 ._k_mutex.static.key_slot_mutex_int_
                0x0000000000000000       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 ._k_mutex.static.heap_mutex_int_
                0x0000000000000000       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .text.z_get_sw_isr_table_idx
                0x0000000000000000        0x2 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_info    0x0000000000000000       0xba zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_abbrev  0x0000000000000000       0x6b zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_loc     0x0000000000000000       0x15 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_ranges  0x0000000000000000       0x10 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_line    0x0000000000000000       0x70 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_str     0x0000000000000000      0x2d0 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .debug_frame   0x0000000000000000       0x20 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .text.arch_syscall_oops
                0x0000000000000000       0x1c zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .text.z_arm_save_fp_context
                0x0000000000000000        0x2 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .text.z_arm_restore_fp_context
                0x0000000000000000        0x2 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_info    0x0000000000000000      0x146 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_abbrev  0x0000000000000000       0xcd zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_ranges  0x0000000000000000       0x18 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_line    0x0000000000000000      0x17d zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_str     0x0000000000000000      0x331 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .debug_frame   0x0000000000000000       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .text.sys_arch_reboot
                0x0000000000000000       0x24 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .text.arch_irq_lock_outlined
                0x0000000000000000       0x10 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .text.arm_cmse_addr_read_ok
                0x0000000000000000       0x12 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .text.arm_cmse_addr_readwrite_ok
                0x0000000000000000       0x12 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .text.arm_cmse_addr_range_read_ok
                0x0000000000000000       0x1a zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .text.arm_cmse_addr_range_readwrite_ok
                0x0000000000000000       0x1a zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .text.z_arm_configure_dynamic_mpu_regions
                0x0000000000000000        0xc zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .bss.dynamic_regions.0
                0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text.arm_core_mpu_configure_dynamic_mpu_regions
                0x0000000000000000       0x4c zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .text          0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(assert.c.obj)
 .data          0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(assert.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(assert.c.obj)
 .text.__assert_no_args
                0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(assert.c.obj)
 .debug_info    0x0000000000000000       0x93 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(assert.c.obj)
 .debug_abbrev  0x0000000000000000       0x47 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(assert.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(assert.c.obj)
 .debug_ranges  0x0000000000000000       0x10 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(assert.c.obj)
 .debug_line    0x0000000000000000       0x68 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(assert.c.obj)
 .debug_str     0x0000000000000000      0x2c8 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(assert.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(assert.c.obj)
 .debug_frame   0x0000000000000000       0x20 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(assert.c.obj)
 .text          0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(cbprintf.c.obj)
 .data          0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(cbprintf.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(cbprintf.c.obj)
 .text          0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(chk_fail.c.obj)
 .data          0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(chk_fail.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(chk_fail.c.obj)
 .rodata.__chk_fail.str1.1
                0x0000000000000000       0x1e zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(chk_fail.c.obj)
 .text.__chk_fail
                0x0000000000000000       0x1c zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(chk_fail.c.obj)
 .debug_info    0x0000000000000000      0x1bb zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(chk_fail.c.obj)
 .debug_abbrev  0x0000000000000000      0x14d zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(chk_fail.c.obj)
 .debug_loc     0x0000000000000000       0x2c zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(chk_fail.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(chk_fail.c.obj)
 .debug_ranges  0x0000000000000000       0x10 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(chk_fail.c.obj)
 .debug_line    0x0000000000000000      0x25e zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(chk_fail.c.obj)
 .debug_str     0x0000000000000000      0x392 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(chk_fail.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(chk_fail.c.obj)
 .debug_frame   0x0000000000000000       0x28 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(chk_fail.c.obj)
 .text          0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(errno_wrap.c.obj)
 .data          0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(errno_wrap.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(errno_wrap.c.obj)
 .debug_info    0x0000000000000000       0x79 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(errno_wrap.c.obj)
 .debug_abbrev  0x0000000000000000       0x26 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(errno_wrap.c.obj)
 .debug_aranges
                0x0000000000000000       0x18 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(errno_wrap.c.obj)
 .debug_line    0x0000000000000000       0x54 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(errno_wrap.c.obj)
 .debug_str     0x0000000000000000      0x2bb zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(errno_wrap.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(errno_wrap.c.obj)
 .text          0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(exit.c.obj)
 .data          0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(exit.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(exit.c.obj)
 .rodata._exit.str1.1
                0x0000000000000000        0x5 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(exit.c.obj)
 .text._exit    0x0000000000000000       0x10 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(exit.c.obj)
 .debug_info    0x0000000000000000       0xca zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(exit.c.obj)
 .debug_abbrev  0x0000000000000000       0x82 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(exit.c.obj)
 .debug_loc     0x0000000000000000       0x25 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(exit.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(exit.c.obj)
 .debug_ranges  0x0000000000000000       0x10 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(exit.c.obj)
 .debug_line    0x0000000000000000       0x8c zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(exit.c.obj)
 .debug_str     0x0000000000000000      0x2d6 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(exit.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(exit.c.obj)
 .debug_frame   0x0000000000000000       0x28 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(exit.c.obj)
 .text          0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .data          0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .text.__retarget_lock_init_recursive
                0x0000000000000000       0x14 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .text.__retarget_lock_init
                0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .text.__retarget_lock_close_recursive
                0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .text.__retarget_lock_close
                0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .text.__retarget_lock_acquire_recursive
                0x0000000000000000        0xc zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .text.__retarget_lock_acquire
                0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .text.__retarget_lock_try_acquire_recursive
                0x0000000000000000       0x12 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .text.__retarget_lock_try_acquire
                0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .text.__retarget_lock_release_recursive
                0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .text.__retarget_lock_release
                0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 ._k_mutex.static.__lock___libc_recursive_mutex_
                0x0000000000000000       0x14 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .debug_info    0x0000000000000000      0x9fe zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .debug_abbrev  0x0000000000000000      0x321 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .debug_loc     0x0000000000000000      0x1b9 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .debug_aranges
                0x0000000000000000       0x68 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .debug_ranges  0x0000000000000000       0x88 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .debug_line    0x0000000000000000      0x50b zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .debug_str     0x0000000000000000      0x923 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .debug_frame   0x0000000000000000       0xc4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .text          0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .data          0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .text.__stdin_hook_install
                0x0000000000000000       0x14 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .rodata.stdout
                0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .rodata.stdin  0x0000000000000000        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .bss.__stdin   0x0000000000000000       0x10 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .text          0x0000000000000000        0x0 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .data          0x0000000000000000        0x0 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .rodata.abort.str1.1
                0x0000000000000000        0x9 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .text.abort    0x0000000000000000       0x1c zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_info    0x0000000000000000      0x1bb zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_abbrev  0x0000000000000000      0x14d zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_loc     0x0000000000000000       0x2c zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_ranges  0x0000000000000000       0x10 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_line    0x0000000000000000      0x2c0 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_str     0x0000000000000000      0x3a1 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .debug_frame   0x0000000000000000       0x28 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .text          0x0000000000000000        0x0 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .data          0x0000000000000000        0x0 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.malloc_lock
                0x0000000000000000       0x14 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.malloc_unlock
                0x0000000000000000        0xc zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.malloc   0x0000000000000000       0x34 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.aligned_alloc
                0x0000000000000000       0x34 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.realloc  0x0000000000000000       0x38 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.free     0x0000000000000000       0x1c zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.calloc   0x0000000000000000       0x34 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.reallocarray
                0x0000000000000000       0x24 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .data.z_malloc_heap_mutex
                0x0000000000000000       0x14 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .debug_info    0x0000000000000000       0x79 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .debug_abbrev  0x0000000000000000       0x26 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .debug_aranges
                0x0000000000000000       0x18 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .debug_line    0x0000000000000000       0x5a zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .debug_str     0x0000000000000000      0x2b8 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .debug_info    0x0000000000000000       0x64 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .debug_abbrev  0x0000000000000000       0x26 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .debug_aranges
                0x0000000000000000       0x18 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .debug_line    0x0000000000000000       0x5b zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .debug_str     0x0000000000000000      0x2a2 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .debug_info    0x0000000000000000       0x79 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .debug_abbrev  0x0000000000000000       0x26 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .debug_aranges
                0x0000000000000000       0x18 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .debug_line    0x0000000000000000       0x5d zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .debug_str     0x0000000000000000      0x2bb zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .text.nrf53_cpunet_enable
                0x0000000000000000       0x34 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .text          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(reboot.c.obj)
 .data          0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(reboot.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(reboot.c.obj)
 .text.sys_arch_reboot
                0x0000000000000000       0x24 zephyr/soc/soc/nrf5340/libsoc__nordic.a(reboot.c.obj)
 .debug_info    0x0000000000000000      0x48c zephyr/soc/soc/nrf5340/libsoc__nordic.a(reboot.c.obj)
 .debug_abbrev  0x0000000000000000      0x128 zephyr/soc/soc/nrf5340/libsoc__nordic.a(reboot.c.obj)
 .debug_aranges
                0x0000000000000000       0x20 zephyr/soc/soc/nrf5340/libsoc__nordic.a(reboot.c.obj)
 .debug_ranges  0x0000000000000000       0x10 zephyr/soc/soc/nrf5340/libsoc__nordic.a(reboot.c.obj)
 .debug_line    0x0000000000000000      0x1c9 zephyr/soc/soc/nrf5340/libsoc__nordic.a(reboot.c.obj)
 .debug_str     0x0000000000000000      0x42e zephyr/soc/soc/nrf5340/libsoc__nordic.a(reboot.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(reboot.c.obj)
 .debug_frame   0x0000000000000000       0x20 zephyr/soc/soc/nrf5340/libsoc__nordic.a(reboot.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.z_nrf_clock_bt_ctlr_hf_request
                0x0000000000000000       0x24 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.z_nrf_clock_bt_ctlr_hf_release
                0x0000000000000000       0x34 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .text.uart_register_input
                0x0000000000000000        0x2 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_common.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_common.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_common.c.obj)
 .text.z_impl_i2s_buf_read
                0x0000000000000000       0x40 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_common.c.obj)
 .text.z_impl_i2s_buf_write
                0x0000000000000000       0x68 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_common.c.obj)
 .debug_info    0x0000000000000000      0x919 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_common.c.obj)
 .debug_abbrev  0x0000000000000000      0x30d zephyr/drivers/i2s/libdrivers__i2s.a(i2s_common.c.obj)
 .debug_loc     0x0000000000000000      0x37e zephyr/drivers/i2s/libdrivers__i2s.a(i2s_common.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_common.c.obj)
 .debug_ranges  0x0000000000000000       0x60 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_common.c.obj)
 .debug_line    0x0000000000000000      0x401 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_common.c.obj)
 .debug_str     0x0000000000000000      0x5c5 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_common.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_common.c.obj)
 .debug_frame   0x0000000000000000       0x58 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_common.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .text.sys_clock_set_timeout
                0x0000000000000000        0x2 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .text.sys_clock_idle_exit
                0x0000000000000000        0x2 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_info    0x0000000000000000       0xda zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_abbrev  0x0000000000000000       0x7e zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_ranges  0x0000000000000000       0x18 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_line    0x0000000000000000      0x13f zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_str     0x0000000000000000      0x2f9 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .debug_frame   0x0000000000000000       0x30 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .text          0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .data          0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_compare_evt_address_get
                0x0000000000000000       0x10 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_capture_task_address_get
                0x0000000000000000       0x14 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_compare_int_lock
                0x0000000000000000        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_compare_int_unlock
                0x0000000000000000        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_compare_read
                0x0000000000000000       0x10 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_abort
                0x0000000000000000       0x58 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_get_ticks
                0x0000000000000000       0x78 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_set
                0x0000000000000000       0x18 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_exact_set
                0x0000000000000000       0x18 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_chan_alloc
                0x0000000000000000       0x3c zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_chan_free
                0x0000000000000000       0x1c zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_trigger_overflow
                0x0000000000000000        0x6 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.sys_clock_disable
                0x0000000000000000       0x30 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.alloc_mask
                0x0000000000000000        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text          0x0000000000000000        0x0 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .data          0x0000000000000000        0x0 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .bss           0x0000000000000000        0x0 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .text.SystemCoreClockUpdate
                0x0000000000000000       0x20 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .text.SystemStoreFICRNS
                0x0000000000000000       0x34 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .text.SystemLockFICRNS
                0x0000000000000000       0x20 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .rodata.nrfx_error_string_get.str1.1
                0x0000000000000000      0x178 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .text.nrfx_error_string_get
                0x0000000000000000       0xd8 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .text.nrfx_flag32_is_allocated
                0x0000000000000000        0xa modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .text.ram_ctrl_block_section_power_enable_set
                0x0000000000000000       0x24 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .text.ram_ctrl_block_section_retention_enable_set
                0x0000000000000000       0x24 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .text.ram_ctrl_block_section_iterate
                0x0000000000000000       0x34 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .text.nrfx_ram_ctrl_power_enable_set
                0x0000000000000000        0xc modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .text.nrfx_ram_ctrl_retention_enable_set
                0x0000000000000000        0xc modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .debug_info    0x0000000000000000      0x8fa modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .debug_abbrev  0x0000000000000000      0x2b6 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .debug_loc     0x0000000000000000      0x579 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .debug_aranges
                0x0000000000000000       0x40 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .debug_ranges  0x0000000000000000       0x50 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .debug_line    0x0000000000000000      0x405 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .debug_str     0x0000000000000000      0x856 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .comment       0x0000000000000000       0x21 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .debug_frame   0x0000000000000000       0x70 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channel_check
                0x0000000000000000       0x14 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channels_disable_all
                0x0000000000000000       0x10 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channels_disable
                0x0000000000000000        0xc modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_task_trigger
                0x0000000000000000        0xe modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channel_endpoints_clear
                0x0000000000000000        0xc modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_event_endpoint_clear
                0x0000000000000000        0x8 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_task_endpoint_clear
                0x0000000000000000        0x8 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_fork_endpoint_setup
                0x0000000000000000        0xa modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_fork_endpoint_clear
                0x0000000000000000        0x8 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channels_group_set
                0x0000000000000000       0x10 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channels_include_in_group
                0x0000000000000000       0x16 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channels_remove_from_group
                0x0000000000000000       0x18 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_clear
                0x0000000000000000       0x14 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_enable
                0x0000000000000000       0x10 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_disable
                0x0000000000000000       0x10 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_task_address_get
                0x0000000000000000        0xa modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_disable_task_get
                0x0000000000000000        0xa modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_enable_task_get
                0x0000000000000000        0x8 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_channel_free
                0x0000000000000000        0xc modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_alloc
                0x0000000000000000        0xc modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_group_free
                0x0000000000000000        0xc modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text.nrfx_gppi_edge_connection_setup
                0x0000000000000000        0x8 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_ppi.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_ppi.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_ppi.c.obj)
 .debug_info    0x0000000000000000       0x79 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_ppi.c.obj)
 .debug_abbrev  0x0000000000000000       0x26 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_ppi.c.obj)
 .debug_aranges
                0x0000000000000000       0x18 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_ppi.c.obj)
 .debug_line    0x0000000000000000       0x5e modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_ppi.c.obj)
 .debug_str     0x0000000000000000      0x2bc modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_ppi.c.obj)
 .comment       0x0000000000000000       0x21 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_ppi.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrf53_errata_4
                0x0000000000000000       0x24 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrfx_clock_disable
                0x0000000000000000       0x20 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrfx_clock_uninit
                0x0000000000000000       0x28 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrfx_clock_init_check
                0x0000000000000000        0xc modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrfx_clock_divider_set
                0x0000000000000000       0xdc modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .rodata.CSWTCH.684
                0x0000000000000000        0x4 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_free
                0x0000000000000000       0x60 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_channel_free
                0x0000000000000000       0x1c modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_channel_enable
                0x0000000000000000       0x34 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_channel_disable
                0x0000000000000000       0x34 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_group_alloc
                0x0000000000000000       0x14 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_group_free
                0x0000000000000000       0x20 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_channel_include_in_group
                0x0000000000000000       0x70 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_channel_remove_from_group
                0x0000000000000000       0x70 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_group_clear
                0x0000000000000000       0x40 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_group_enable
                0x0000000000000000       0x34 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text.nrfx_dppi_group_disable
                0x0000000000000000       0x34 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_in_event_get.isra.0
                0x0000000000000000       0x22 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_in_is_set
                0x0000000000000000       0x1a modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_channels_number_get
                0x0000000000000000        0xc modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_uninit
                0x0000000000000000       0x6c modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_set
                0x0000000000000000       0x18 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_clear
                0x0000000000000000       0x18 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_toggle
                0x0000000000000000       0x22 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_enable
                0x0000000000000000       0x1e modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_disable
                0x0000000000000000       0x1e modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_get
                0x0000000000000000       0x10 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_address_get
                0x0000000000000000       0x12 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_set_task_get
                0x0000000000000000       0x14 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_set_task_address_get
                0x0000000000000000       0x16 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_clr_task_get
                0x0000000000000000       0x14 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_clr_task_address_get
                0x0000000000000000       0x18 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_force
                0x0000000000000000       0x2a modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_out_task_trigger
                0x0000000000000000       0x14 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_set_task_trigger
                0x0000000000000000       0x18 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_clr_task_trigger
                0x0000000000000000       0x18 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_in_event_get
                0x0000000000000000        0x6 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_in_event_address_get
                0x0000000000000000       0x10 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .rodata.port_lens.0
                0x0000000000000000        0x2 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .data          0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .bss           0x0000000000000000        0x0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .text.nrfx_i2s_init_check
                0x0000000000000000       0x18 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(device.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(device.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(device.c.obj)
 .text.z_device_get_all_static
                0x0000000000000000       0x18 zephyr/kernel/libkernel.a(device.c.obj)
 .text.z_impl_device_get_binding
                0x0000000000000000       0x48 zephyr/kernel/libkernel.a(device.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(fatal.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(fatal.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(fatal.c.obj)
 .text.k_fatal_halt
                0x0000000000000000        0x6 zephyr/kernel/libkernel.a(fatal.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init.c.obj)
 .text.z_impl_device_init
                0x0000000000000000       0x28 zephyr/kernel/libkernel.a(init.c.obj)
 .text.z_early_rand_get
                0x0000000000000000       0x70 zephyr/kernel/libkernel.a(init.c.obj)
 .data.state.1  0x0000000000000000        0x8 zephyr/kernel/libkernel.a(init.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init_static.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init_static.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(init_static.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .text.k_mem_slab_runtime_stats_get
                0x0000000000000000       0x3c zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(idle.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(idle.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(idle.c.obj)
 .text.arch_spin_relax
                0x0000000000000000        0x4 zephyr/kernel/libkernel.a(idle.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(msg_q.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(msg_q.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(msg_q.c.obj)
 .text.z_impl_k_msgq_alloc_init
                0x0000000000000000       0x38 zephyr/kernel/libkernel.a(msg_q.c.obj)
 .text.k_msgq_cleanup
                0x0000000000000000       0x32 zephyr/kernel/libkernel.a(msg_q.c.obj)
 .text.z_impl_k_msgq_get_attrs
                0x0000000000000000        0xe zephyr/kernel/libkernel.a(msg_q.c.obj)
 .text.z_impl_k_msgq_peek
                0x0000000000000000       0x32 zephyr/kernel/libkernel.a(msg_q.c.obj)
 .text.z_impl_k_msgq_peek_at
                0x0000000000000000       0x46 zephyr/kernel/libkernel.a(msg_q.c.obj)
 .text.z_impl_k_msgq_purge
                0x0000000000000000       0x68 zephyr/kernel/libkernel.a(msg_q.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mutex.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mutex.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mutex.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sem.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sem.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sem.c.obj)
 .text.z_impl_k_sem_reset
                0x0000000000000000       0x60 zephyr/kernel/libkernel.a(sem.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(thread.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(thread.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.z_impl_k_is_preempt_thread
                0x0000000000000000       0x20 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.z_impl_k_thread_priority_get
                0x0000000000000000        0x6 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.z_impl_k_thread_name_copy
                0x0000000000000000        0x6 zephyr/kernel/libkernel.a(thread.c.obj)
 .rodata.k_thread_state_str.str1.1
                0x0000000000000000        0x3 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.k_thread_state_str
                0x0000000000000000       0x84 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.z_init_thread_base
                0x0000000000000000       0x14 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.k_thread_user_mode_enter
                0x0000000000000000       0x1c zephyr/kernel/libkernel.a(thread.c.obj)
 .text.k_thread_runtime_stats_get
                0x0000000000000000       0x14 zephyr/kernel/libkernel.a(thread.c.obj)
 .text.k_thread_runtime_stats_all_get
                0x0000000000000000        0xc zephyr/kernel/libkernel.a(thread.c.obj)
 .text.k_thread_runtime_stats_cpu_get
                0x0000000000000000        0xc zephyr/kernel/libkernel.a(thread.c.obj)
 .rodata.str1.1
                0x0000000000000000       0x41 zephyr/kernel/libkernel.a(thread.c.obj)
 .rodata.state_string.0
                0x0000000000000000       0x40 zephyr/kernel/libkernel.a(thread.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sched.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sched.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_requeue_current
                0x0000000000000000       0x4c zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_move_thread_to_end_of_prio_q
                0x0000000000000000       0x1e zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_thread_suspend
                0x0000000000000000       0xc4 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_pend_thread
                0x0000000000000000       0x44 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_unpend_thread
                0x0000000000000000       0x10 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_thread_resume
                0x0000000000000000       0x3c zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_swap_next_thread
                0x0000000000000000        0xc zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_unpend_all
                0x0000000000000000       0x26 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.init_ready_q
                0x0000000000000000        0x8 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_thread_priority_set
                0x0000000000000000       0x2c zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_reschedule
                0x0000000000000000       0x54 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.k_can_yield
                0x0000000000000000       0x30 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_thread_join
                0x0000000000000000       0x80 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_sched_wake
                0x0000000000000000       0x44 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_sched_wait
                0x0000000000000000       0x24 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_sched_waitq_walk
                0x0000000000000000       0x44 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_unready_thread
                0x0000000000000000       0x1e zephyr/kernel/libkernel.a(sched.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .text.k_sched_time_slice_set
                0x0000000000000000       0x54 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(xip.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(xip.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(xip.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timeout.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timeout.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.timeout_rem
                0x0000000000000000       0x30 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.z_timeout_remaining
                0x0000000000000000       0x38 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.z_timeout_expires
                0x0000000000000000       0x38 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.z_get_next_timeout_expiry
                0x0000000000000000       0x1e zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.sys_timepoint_calc
                0x0000000000000000       0x48 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.sys_timepoint_timeout
                0x0000000000000000       0x42 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timer.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timer.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timer.c.obj)
 .text.z_impl_k_timer_status_get
                0x0000000000000000       0x20 zephyr/kernel/libkernel.a(timer.c.obj)
 .text.z_impl_k_timer_status_sync
                0x0000000000000000       0x54 zephyr/kernel/libkernel.a(timer.c.obj)
 .bss.lock      0x0000000000000000        0x0 zephyr/kernel/libkernel.a(timer.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mempool.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mempool.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(mempool.c.obj)
 .text.k_free   0x0000000000000000        0xe zephyr/kernel/libkernel.a(mempool.c.obj)
 .text.z_thread_aligned_alloc
                0x0000000000000000       0x40 zephyr/kernel/libkernel.a(mempool.c.obj)
 .debug_info    0x0000000000000000      0x879 zephyr/kernel/libkernel.a(mempool.c.obj)
 .debug_abbrev  0x0000000000000000      0x2f0 zephyr/kernel/libkernel.a(mempool.c.obj)
 .debug_loc     0x0000000000000000      0x2bc zephyr/kernel/libkernel.a(mempool.c.obj)
 .debug_aranges
                0x0000000000000000       0x28 zephyr/kernel/libkernel.a(mempool.c.obj)
 .debug_ranges  0x0000000000000000       0x30 zephyr/kernel/libkernel.a(mempool.c.obj)
 .debug_line    0x0000000000000000      0x380 zephyr/kernel/libkernel.a(mempool.c.obj)
 .debug_str     0x0000000000000000      0x611 zephyr/kernel/libkernel.a(mempool.c.obj)
 .comment       0x0000000000000000       0x21 zephyr/kernel/libkernel.a(mempool.c.obj)
 .debug_frame   0x0000000000000000       0x40 zephyr/kernel/libkernel.a(mempool.c.obj)
 .text          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(kheap.c.obj)
 .data          0x0000000000000000        0x0 zephyr/kernel/libkernel.a(kheap.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/kernel/libkernel.a(kheap.c.obj)
 .text.k_heap_aligned_alloc
                0x0000000000000000       0x82 zephyr/kernel/libkernel.a(kheap.c.obj)
 .text.k_heap_alloc
                0x0000000000000000       0x14 zephyr/kernel/libkernel.a(kheap.c.obj)
 .text.k_heap_calloc
                0x0000000000000000       0x26 zephyr/kernel/libkernel.a(kheap.c.obj)
 .text.k_heap_realloc
                0x0000000000000000       0x84 zephyr/kernel/libkernel.a(kheap.c.obj)
 .text.k_heap_free
                0x0000000000000000       0x38 zephyr/kernel/libkernel.a(kheap.c.obj)
 .text          0x0000000000000000        0x0 zephyr/arch/common/libisr_tables.a(isr_tables.c.obj)
 .data          0x0000000000000000        0x0 zephyr/arch/common/libisr_tables.a(isr_tables.c.obj)
 .bss           0x0000000000000000        0x0 zephyr/arch/common/libisr_tables.a(isr_tables.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_init
                0x0000000000000000       0x44 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_init_hmac_drbg
                0x0000000000000000       0x44 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_deinit
                0x0000000000000000       0x1c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_is_initialized
                0x0000000000000000       0x14 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_rng_is_initialized
                0x0000000000000000       0x14 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_get_nonce_seed
                0x0000000000000000       0x28 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text.nrf_cc3xx_platform_get_boot_seed
                0x0000000000000000       0x28 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .bss.nrf_cc3xx_platform_rng_initialized
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .rodata.RndStartupTest.constprop.0.str1.4
                0x0000000000000000       0x6f C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.RndStartupTest.constprop.0
                0x0000000000000000       0xac C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.CC_LibInit
                0x0000000000000000       0x74 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.CC_LibInit_HMAC_DRBG
                0x0000000000000000       0x74 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.CC_RandomSeedsFilled
                0x0000000000000000       0x18 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.CC_LibFini
                0x0000000000000000       0x18 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text.CC_LibInitRngModule
                0x0000000000000000       0xbc C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .bss.rndWorkbuff.0
                0x0000000000000000      0x220 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .bss.random_seed_filled
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .data.tfm_random_seed_buff
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .data.eits_random_nonce_buff
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .data.invalid_chacha_256_bit_key
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .data.invalid_aes_256_bit_key
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .bss.random_seed_buffer
                0x0000000000000000       0x68 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalTerminate
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalClearInterruptBit
                0x0000000000000000       0x1c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalClearInterruptBitRNG
                0x0000000000000000        0xc C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalMaskInterrupt
                0x0000000000000000        0xc C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalWaitInterrupt
                0x0000000000000000        0xc C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text.CC_HalWaitInterruptRND
                0x0000000000000000        0xc C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .data.CCApbFilteringRegMutex
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .text.CC_PalWaitInterruptRND
                0x0000000000000000       0x38 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .text.CC_PalWaitInterrupt
                0x0000000000000000       0x28 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalSecMemCmp
                0x0000000000000000       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalMemCmpPlat
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalMemCopyPlat
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalMemMovePlat
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalMemSetPlat
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text.CC_PalMemSetZeroPlat
                0x0000000000000000        0x8 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .text.CC_PalMutexLock
                0x0000000000000000       0x10 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .text.CC_PalMutexUnlock
                0x0000000000000000       0x14 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .text.CC_PalPowerSaveModeStatus
                0x0000000000000000        0xc C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .text.CC_PalPowerSaveModeSelect
                0x0000000000000000       0x84 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_init
                0x0000000000000000       0x60 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_free
                0x0000000000000000       0x2c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_set_pr
                0x0000000000000000       0x28 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_set_reseed_interval
                0x0000000000000000       0x30 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_reseed
                0x0000000000000000       0x38 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_get_with_add
                0x0000000000000000       0x58 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text.nrf_cc3xx_platform_ctr_drbg_get
                0x0000000000000000       0x44 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_init
                0x0000000000000000       0x60 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_free
                0x0000000000000000       0x2c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_set_pr
                0x0000000000000000       0x28 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_set_reseed_interval
                0x0000000000000000       0x30 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_reseed
                0x0000000000000000       0x38 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_get_with_add
                0x0000000000000000       0x58 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text.nrf_cc3xx_platform_hmac_drbg_get
                0x0000000000000000       0x44 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .bss.global_ctx
                0x0000000000000000      0x250 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .text.mbedtls_zeroize_internal
                0x0000000000000000       0x14 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .text.cc_mbedtls_platform_zeroize
                0x0000000000000000       0x14 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.startTrngHW
                0x0000000000000000      0x130 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_StartTrngHW
                0x0000000000000000      0x10c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_RepetitionCounterTest
                0x0000000000000000       0x64 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_AdaptiveProportionTest
                0x0000000000000000       0x80 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.getTrngSource
                0x0000000000000000      0x2a8 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_GetTrngSource
                0x0000000000000000       0x18 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text.LLF_RND_RunTrngStartupTest
                0x0000000000000000       0x1c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.block_cipher_df
                0x0000000000000000      0x1d0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.ctr_drbg_update_internal
                0x0000000000000000      0x174 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.mbedtls_ctr_drbg_reseed_internal
                0x0000000000000000       0xc4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_init
                0x0000000000000000       0x2c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_free
                0x0000000000000000       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_set_prediction_resistance
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_set_entropy_len
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_set_nonce_len
                0x0000000000000000       0x20 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_set_reseed_interval
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_update
                0x0000000000000000       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_reseed
                0x0000000000000000       0x44 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_seed
                0x0000000000000000       0xa0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_random_with_add
                0x0000000000000000      0x1e4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text.cc_mbedtls_ctr_drbg_random
                0x0000000000000000       0x14 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .bss.buf.0     0x0000000000000000      0x1a0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .bss.seed.1    0x0000000000000000      0x180 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.entropy_update
                0x0000000000000000       0x7c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.entropy_gather_internal.part.0
                0x0000000000000000       0x88 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_init
                0x0000000000000000       0x84 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_free
                0x0000000000000000       0x38 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_add_source
                0x0000000000000000       0x64 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_update_manual
                0x0000000000000000       0x4c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_gather
                0x0000000000000000       0x48 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text.cc_mbedtls_entropy_func
                0x0000000000000000      0x110 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_hmac_sha256_starts.constprop.0
                0x0000000000000000      0x108 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_init
                0x0000000000000000       0x28 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_update
                0x0000000000000000      0x170 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_hmac_drbg_reseed_core
                0x0000000000000000       0xac C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_seed_buf
                0x0000000000000000       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_reseed
                0x0000000000000000        0x8 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_seed
                0x0000000000000000       0x54 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_set_prediction_resistance
                0x0000000000000000        0x8 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_set_entropy_len
                0x0000000000000000        0x8 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_set_reseed_interval
                0x0000000000000000        0x8 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_random_with_add
                0x0000000000000000      0x144 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_random
                0x0000000000000000       0x4c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text.cc_mbedtls_hmac_drbg_free
                0x0000000000000000       0x40 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .bss.hmac_ctx  0x0000000000000000       0x80 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .text.RNG_PLAT_SetUserRngParameters
                0x0000000000000000       0x74 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .text.CC_PalTrngParamGet
                0x0000000000000000       0xac C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mutex_init
                0x0000000000000000        0xc C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mutex_free
                0x0000000000000000        0xc C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mutex_lock
                0x0000000000000000        0xc C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mutex_unlock
                0x0000000000000000        0xc C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .rodata.mbedtls_threading_set_alt.str1.4
                0x0000000000000000       0x44 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mbedtls_threading_set_alt
                0x0000000000000000       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text.mbedtls_threading_free_alt
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .data.mbedtls_mutex_unlock
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .data.mbedtls_mutex_lock
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .data.mbedtls_mutex_free
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .data.mbedtls_mutex_init
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.Mult32x32
                0x0000000000000000       0x40 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.Mult48x16
                0x0000000000000000       0x1c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_EntropyEstimateFull
                0x0000000000000000      0x52c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_WaitRngInterrupt
                0x0000000000000000       0x20 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_GetRoscSampleCnt
                0x0000000000000000       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_GetFastestRosc
                0x0000000000000000       0x20 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_GetCountRoscs
                0x0000000000000000       0x18 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_TurnOffTrng
                0x0000000000000000       0x24 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text.LLF_RND_RndCprngt
                0x0000000000000000       0x58 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .rodata.mbedtls_hardware_poll.str1.4
                0x0000000000000000       0x6e C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .text.mbedtls_hardware_poll
                0x0000000000000000      0x104 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .bss.rndState.0
                0x0000000000000000        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .bss.trngParams.1
                0x0000000000000000       0x28 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .bss.rndWorkBuffer.2
                0x0000000000000000      0x220 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .rodata.cc_mbedtls_aes_init.str1.4
                0x0000000000000000       0x13 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_init
                0x0000000000000000       0x20 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_free
                0x0000000000000000        0xc C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_setkey_enc
                0x0000000000000000       0x44 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_setkey_dec
                0x0000000000000000       0x44 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_ecb
                0x0000000000000000       0x50 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_cbc
                0x0000000000000000       0x78 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_cfb128
                0x0000000000000000        0x8 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_cfb8
                0x0000000000000000        0x8 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_ctr
                0x0000000000000000       0x68 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_aes_crypt_ofb
                0x0000000000000000       0x68 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_internal_aes_encrypt
                0x0000000000000000       0x44 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text.cc_mbedtls_internal_aes_decrypt
                0x0000000000000000       0x48 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .rodata.cc_mbedtls_sha256_init.str1.4
                0x0000000000000000        0xe C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_init
                0x0000000000000000       0x28 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_free
                0x0000000000000000        0xc C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .rodata.cc_mbedtls_sha256_clone.str1.4
                0x0000000000000000       0x15 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_clone
                0x0000000000000000       0x2c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_starts
                0x0000000000000000       0x28 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_internal_sha256_process
                0x0000000000000000       0x10 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_update
                0x0000000000000000       0x54 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text.cc_mbedtls_sha256_finish
                0x0000000000000000       0x48 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .text.mbedtls_sha_process_internal
                0x0000000000000000       0xe0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .text.mbedtls_sha_starts_internal
                0x0000000000000000       0x28 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .text.mbedtls_sha_finish_internal
                0x0000000000000000       0x5c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .text.mbedtls_sha_update_internal
                0x0000000000000000      0x1ec C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .text.cc_mbedtls_sha256
                0x0000000000000000       0x50 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .bss.ctx.0     0x0000000000000000       0xf4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .text.SetDataBuffersInfo
                0x0000000000000000       0x64 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .text.InitHashDrv
                0x0000000000000000       0x50 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .rodata.ProcessHashDrv.str1.4
                0x0000000000000000       0x6f C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .text.ProcessHashDrv
                0x0000000000000000      0x210 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .text.FinishHashDrv
                0x0000000000000000       0x74 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .rodata.HASH_LARVAL_SHA256
                0x0000000000000000       0x20 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .rodata.HASH_LARVAL_SHA224
                0x0000000000000000       0x20 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .rodata.HASH_LARVAL_SHA1
                0x0000000000000000       0x14 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text.InitAes  0x0000000000000000       0xc8 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text.LoadAesKey
                0x0000000000000000      0x114 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text.write_invalid_key
                0x0000000000000000       0x50 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .rodata.ProcessAesDrv.str1.4
                0x0000000000000000       0x6f C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text.ProcessAesDrv
                0x0000000000000000      0x338 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text.FinishAesDrv
                0x0000000000000000      0x200 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_write_key_slot
                0x0000000000000000       0xd8 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_verify_kdf_input
                0x0000000000000000       0x24 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_convert_keybits_to_keysize
                0x0000000000000000       0x2c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_validate_slot_and_size
                0x0000000000000000       0x2c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_validate_slot_and_size_no_kdr
                0x0000000000000000       0x2c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_validate_kdr_slot_and_size
                0x0000000000000000       0x14 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_load_key_chacha20
                0x0000000000000000      0x108 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_load_key_aes
                0x0000000000000000      0x200 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_derive_cmac
                0x0000000000000000      0x120 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text.kmu_shadow_key_derive
                0x0000000000000000       0x98 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .text.write_invalid_chacha20_key
                0x0000000000000000       0x40 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .text.cc_platform_prepare_chacha_key
                0x0000000000000000       0x4c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .text.cc_platform_load_chacha_key
                0x0000000000000000       0x68 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .text.cc_platform_cleanup_chacha_key
                0x0000000000000000       0x3c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .text.UtilCmacBuildDataForDerivation
                0x0000000000000000       0xc0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .text.UtilCmacDeriveKey
                0x0000000000000000       0xf0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .text          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .data          0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .bss           0x0000000000000000        0x0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .text.CC_PalDataBufferAttrGet
                0x0000000000000000        0x8 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .comment       0x0000000000000000       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .text          0x0000000000000000       0x14 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strcmp.S.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strcmp.S.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strcmp.S.o)
 .ARM.extab     0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strcmp.S.o)
 .ARM.exidx     0x0000000000000000        0x8 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strcmp.S.o)
 .debug_frame   0x0000000000000000       0x20 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strcmp.S.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memcpy.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memcpy.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memcpy.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memmove.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memmove.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memmove.c.o)
 .text.memmove  0x0000000000000000       0x34 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memmove.c.o)
 .debug_frame   0x0000000000000000       0x28 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memmove.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memset.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memset.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memset.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strlen.S.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strlen.S.o)
 .ARM.extab     0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strlen.S.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_errno_errno.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_errno_errno.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_errno_errno.c.o)
 .tbss.errno    0x0000000000000000        0x4 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_errno_errno.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_memcmp.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_memcmp.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_memcmp.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strnlen.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strnlen.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strnlen.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_puts.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_puts.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_puts.c.o)
 .text.puts     0x0000000000000000       0x40 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_puts.c.o)
 .debug_frame   0x0000000000000000       0x30 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_puts.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_snprintf.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_snprintf.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_snprintf.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .text.scanf_getc
                0x0000000000000000       0x14 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .text.scanf_ungetc
                0x0000000000000000       0x10 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .text.skip_spaces
                0x0000000000000000       0x2a c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .text.putval   0x0000000000000000       0x2c c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .rodata.__l_vfscanf.str1.1
                0x0000000000000000        0xc c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .text.__l_vfscanf
                0x0000000000000000      0x360 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .debug_frame   0x0000000000000000       0x94 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strchr.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strchr.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strchr.c.o)
 .text.strchr   0x0000000000000000       0x1a c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strchr.c.o)
 .debug_frame   0x0000000000000000       0x20 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strchr.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_fgetc.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_fgetc.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_fgetc.c.o)
 .text.fgetc    0x0000000000000000       0x46 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_fgetc.c.o)
 .debug_frame   0x0000000000000000       0x28 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_fgetc.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_filestrput.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_filestrput.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_filestrput.c.o)
 .text          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_ungetc.c.o)
 .data          0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_ungetc.c.o)
 .bss           0x0000000000000000        0x0 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_ungetc.c.o)
 .text.ungetc   0x0000000000000000       0x32 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_ungetc.c.o)
 .debug_frame   0x0000000000000000       0x20 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_ungetc.c.o)
 .text          0x0000000000000000       0xc8 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
 .debug_frame   0x0000000000000000       0x28 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
 .text          0x0000000000000000      0x254 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldf3.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldf3.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldf3.o)
 .debug_frame   0x0000000000000000       0x30 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldf3.o)
 .text          0x0000000000000000      0x378 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubdf3.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubdf3.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubdf3.o)
 .debug_frame   0x0000000000000000       0xac c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubdf3.o)
 .text          0x0000000000000000      0x424 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivdf3.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivdf3.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivdf3.o)
 .debug_frame   0x0000000000000000       0x50 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivdf3.o)
 .text          0x0000000000000000       0xa0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_truncdfsf2.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_truncdfsf2.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_truncdfsf2.o)
 .debug_frame   0x0000000000000000       0x24 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_truncdfsf2.o)
 .text          0x0000000000000000       0xec c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpsf2.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpsf2.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpsf2.o)
 .debug_frame   0x0000000000000000       0xc8 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpsf2.o)
 .text          0x0000000000000000       0xa0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_ldivmod.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_ldivmod.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_ldivmod.o)
 .debug_frame   0x0000000000000000       0x44 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_ldivmod.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
 .text          0x0000000000000000       0x26 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_popcountsi2.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_popcountsi2.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_popcountsi2.o)
 .debug_frame   0x0000000000000000       0x20 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_popcountsi2.o)
 .text          0x0000000000000000       0x2e c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixdfdi.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixdfdi.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixdfdi.o)
 .debug_frame   0x0000000000000000       0x38 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixdfdi.o)
 .text          0x0000000000000000       0x3c c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixunsdfdi.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixunsdfdi.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixunsdfdi.o)
 .debug_frame   0x0000000000000000       0x2c c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixunsdfdi.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
 .ARM.extab     0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_dvmd_tls.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_dvmd_tls.o)
 .text          0x0000000000000000      0x110 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpdf2.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpdf2.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpdf2.o)
 .debug_frame   0x0000000000000000       0xc4 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpdf2.o)
 .text          0x0000000000000000       0x40 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunsdfsi.o)
 .data          0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunsdfsi.o)
 .bss           0x0000000000000000        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunsdfsi.o)
 .debug_frame   0x0000000000000000       0x24 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunsdfsi.o)

Memory Configuration

Name             Origin             Length             Attributes
FLASH            0x0000000000000000 0x0000000000100000 xr
RAM              0x0000000020000000 0x0000000000070000 xw
IDT_LIST         0x00000000ffff7fff 0x0000000000008000 xw
*default*        0x0000000000000000 0xffffffffffffffff

Linker script and memory map

                0x0000000000006df4                vfprintf = __l_vfprintf
                0x0000000000000000                vfscanf = __l_vfscanf
LOAD zephyr/CMakeFiles/zephyr_pre0.dir/misc/empty_file.c.obj
LOAD zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
LOAD app/libapp.a
LOAD zephyr/libzephyr.a
LOAD zephyr/arch/common/libarch__common.a
LOAD zephyr/arch/arch/arm/core/libarch__arm__core.a
LOAD zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a
LOAD zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a
LOAD zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a
LOAD zephyr/lib/libc/picolibc/liblib__libc__picolibc.a
LOAD zephyr/lib/libc/common/liblib__libc__common.a
LOAD zephyr/soc/soc/nrf5340/libsoc__nordic.a
LOAD zephyr/drivers/clock_control/libdrivers__clock_control.a
LOAD zephyr/drivers/console/libdrivers__console.a
LOAD zephyr/drivers/gpio/libdrivers__gpio.a
LOAD zephyr/drivers/i2s/libdrivers__i2s.a
LOAD zephyr/drivers/led_strip/libdrivers__led_strip.a
LOAD zephyr/drivers/pinctrl/libdrivers__pinctrl.a
LOAD zephyr/drivers/serial/libdrivers__serial.a
LOAD zephyr/drivers/timer/libdrivers__timer.a
LOAD modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a
LOAD modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a
LOAD zephyr/kernel/libkernel.a
LOAD zephyr/arch/common/libisr_tables.a
LOAD C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a
LOAD c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a
LOAD c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a
                0x0000000000000020                _region_min_align = 0x20

.rel.plt        0x0000000000000000        0x0
 *(SORT_BY_ALIGNMENT(.rel.plt))
                [!provide]                        PROVIDE (__rel_iplt_start = .)
 *(SORT_BY_ALIGNMENT(.rel.iplt))
 .rel.iplt      0x0000000000000000        0x0 app/libapp.a(main.c.obj)
                [!provide]                        PROVIDE (__rel_iplt_end = .)

.rela.plt       0x0000000000000000        0x0
 *(SORT_BY_ALIGNMENT(.rela.plt))
                [!provide]                        PROVIDE (__rela_iplt_start = .)
 *(SORT_BY_ALIGNMENT(.rela.iplt))
                [!provide]                        PROVIDE (__rela_iplt_end = .)

.rel.dyn
 *(SORT_BY_ALIGNMENT(.rel.*))

.rela.dyn
 *(SORT_BY_ALIGNMENT(.rela.*))

/DISCARD/
 *(SORT_BY_ALIGNMENT(.plt))

/DISCARD/
 *(SORT_BY_ALIGNMENT(.iplt))
                0x0000000000000000                __rom_region_start = 0x0

rom_start       0x0000000000000000      0x154
                0x0000000000000000                __rom_start_address = .
 FILL mask 0x00
                0x0000000000000000                . = (. + (0x0 - (. - __rom_start_address)))
                0x0000000000000000                . = ALIGN (0x4)
                0x0000000000000000                . = ALIGN (0x80)
                0x0000000000000000                . = ALIGN (0x200)
                0x0000000000000000                _vector_start = .
 *(SORT_BY_ALIGNMENT(.exc_vector_table))
 *(SORT_BY_ALIGNMENT(.exc_vector_table.*))
 .exc_vector_table._vector_table_section
                0x0000000000000000       0x40 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
                0x0000000000000000                _vector_table
 *(SORT_BY_ALIGNMENT(.vectors))
                0x0000000000000154                _vector_end = .
                0x0000000000000040                . = ALIGN (0x4)
 *(SORT_BY_ALIGNMENT(.gnu.linkonce.irq_vector_table*))
 .gnu.linkonce.irq_vector_table
                0x0000000000000040      0x114 zephyr/arch/common/libisr_tables.a(isr_tables.c.obj)
                0x0000000000000040                _irq_vector_table
                0x0000000000000154                _vector_end = .

text            0x0000000000000154     0x88dc
                0x0000000000000154                __text_region_start = .
 *(SORT_BY_ALIGNMENT(.text))
 .text          0x0000000000000154       0x30 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
                0x0000000000000154                __aeabi_uldivmod
 .text          0x0000000000000184        0x4 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_dvmd_tls.o)
                0x0000000000000184                __aeabi_idiv0
                0x0000000000000184                __aeabi_ldiv0
 .text          0x0000000000000188       0x10 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strlen.S.o)
                0x0000000000000188                strlen
 .text          0x0000000000000198      0x29c c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
                0x0000000000000198                __udivmoddi4
 *(SORT_BY_ALIGNMENT(.text.*))
 .text.main     0x0000000000000434       0x48 app/libapp.a(main.c.obj)
                0x0000000000000434                main
 .text.led_toggle
                0x000000000000047c       0x10 app/libapp.a(main.c.obj)
                0x000000000000047c                led_toggle
 .text.buttons_init
                0x000000000000048c       0x64 app/libapp.a(button.c.obj)
                0x000000000000048c                buttons_init
 .text.cbvprintf_package
                0x00000000000004f0      0x3a4 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
                0x00000000000004f0                cbvprintf_package
 .text.cbprintf_package_convert
                0x0000000000000894      0x3cc zephyr/libzephyr.a(cbprintf_packaged.c.obj)
                0x0000000000000894                cbprintf_package_convert
 .text.__printk_hook_install
                0x0000000000000c60        0xc zephyr/libzephyr.a(printk.c.obj)
                0x0000000000000c60                __printk_hook_install
 .text.z_thread_entry
                0x0000000000000c6c       0x34 zephyr/libzephyr.a(thread_entry.c.obj)
                0x0000000000000c6c                z_thread_entry
 .text.process_event
                0x0000000000000ca0      0x21c zephyr/libzephyr.a(onoff.c.obj)
 .text.activate_foreach_backend
                0x0000000000000ebc       0x64 zephyr/libzephyr.a(log_core.c.obj)
 .text.enable_logger
                0x0000000000000f20       0x5c zephyr/libzephyr.a(log_core.c.obj)
 .text.log_process_thread_timer_expiry_fn
                0x0000000000000f7c        0xc zephyr/libzephyr.a(log_core.c.obj)
 .text.z_log_init
                0x0000000000000f88       0x90 zephyr/libzephyr.a(log_core.c.obj)
 .text.log_format_func_t_get
                0x0000000000001018        0xc zephyr/libzephyr.a(log_core.c.obj)
                0x0000000000001018                log_format_func_t_get
 .text.log_set_timestamp_func
                0x0000000000001024       0x20 zephyr/libzephyr.a(log_core.c.obj)
                0x0000000000001024                log_set_timestamp_func
 .text.z_log_notify_backend_enabled
                0x0000000000001044       0x1c zephyr/libzephyr.a(log_core.c.obj)
                0x0000000000001044                z_log_notify_backend_enabled
 .text.z_log_dropped
                0x0000000000001060       0x3c zephyr/libzephyr.a(log_core.c.obj)
                0x0000000000001060                z_log_dropped
 .text.z_log_dropped_read_and_clear
                0x000000000000109c       0x18 zephyr/libzephyr.a(log_core.c.obj)
                0x000000000000109c                z_log_dropped_read_and_clear
 .text.dropped_notify
                0x00000000000010b4       0x30 zephyr/libzephyr.a(log_core.c.obj)
                0x00000000000010b4                dropped_notify
 .text.z_log_msg_init
                0x00000000000010e4       0x20 zephyr/libzephyr.a(log_core.c.obj)
                0x00000000000010e4                z_log_msg_init
 .text.log_core_init
                0x0000000000001104       0x34 zephyr/libzephyr.a(log_core.c.obj)
                0x0000000000001104                log_core_init
 .text.z_log_msg_alloc
                0x0000000000001138       0x10 zephyr/libzephyr.a(log_core.c.obj)
                0x0000000000001138                z_log_msg_alloc
 .text.z_log_msg_local_claim
                0x0000000000001148        0xc zephyr/libzephyr.a(log_core.c.obj)
                0x0000000000001148                z_log_msg_local_claim
 .text.z_log_msg_free
                0x0000000000001154       0x10 zephyr/libzephyr.a(log_core.c.obj)
                0x0000000000001154                z_log_msg_free
 .text.z_log_msg_pending
                0x0000000000001164        0xc zephyr/libzephyr.a(log_core.c.obj)
                0x0000000000001164                z_log_msg_pending
 .text.z_impl_log_process
                0x0000000000001170       0xb4 zephyr/libzephyr.a(log_core.c.obj)
                0x0000000000001170                z_impl_log_process
 .text.z_impl_log_panic
                0x0000000000001224       0x44 zephyr/libzephyr.a(log_core.c.obj)
                0x0000000000001224                z_impl_log_panic
 .text.log_process_thread_func
                0x0000000000001268       0xa4 zephyr/libzephyr.a(log_core.c.obj)
 .text.z_log_msg_post_finalize
                0x000000000000130c       0x78 zephyr/libzephyr.a(log_core.c.obj)
 .text.z_log_msg_commit
                0x0000000000001384       0x24 zephyr/libzephyr.a(log_core.c.obj)
                0x0000000000001384                z_log_msg_commit
 .text.log_source_name_get
                0x00000000000013a8       0x1c zephyr/libzephyr.a(log_mgmt.c.obj)
                0x00000000000013a8                log_source_name_get
 .text.log_backend_enable
                0x00000000000013c4       0x28 zephyr/libzephyr.a(log_mgmt.c.obj)
                0x00000000000013c4                log_backend_enable
 .text.z_impl_z_log_msg_static_create
                0x00000000000013ec      0x108 zephyr/libzephyr.a(log_msg.c.obj)
                0x00000000000013ec                z_impl_z_log_msg_static_create
 .text.z_log_msg_runtime_vcreate
                0x00000000000014f4       0xa0 zephyr/libzephyr.a(log_msg.c.obj)
                0x00000000000014f4                z_log_msg_runtime_vcreate
 .text.log_msg_get_source_id
                0x0000000000001594       0x18 zephyr/libzephyr.a(log_msg.c.obj)
                0x0000000000001594                log_msg_get_source_id
 .text.print_formatted
                0x00000000000015ac       0x24 zephyr/libzephyr.a(log_output.c.obj)
 .text.newline_print
                0x00000000000015d0       0x1c zephyr/libzephyr.a(log_output.c.obj)
 .text.log_output_process
                0x00000000000015ec      0x2d4 zephyr/libzephyr.a(log_output.c.obj)
                0x00000000000015ec                log_output_process
 .text.log_output_dropped_process
                0x00000000000018c0       0x58 zephyr/libzephyr.a(log_output.c.obj)
                0x00000000000018c0                log_output_dropped_process
 .text.log_output_timestamp_freq_set
                0x0000000000001918       0x2c zephyr/libzephyr.a(log_output.c.obj)
                0x0000000000001918                log_output_timestamp_freq_set
 .text.char_out
                0x0000000000001944       0x24 zephyr/libzephyr.a(log_backend_uart.c.obj)
 .text.mem_attr_get_regions
                0x0000000000001968        0xc zephyr/libzephyr.a(mem_attr.c.obj)
                0x0000000000001968                mem_attr_get_regions
 .text.boot_banner
                0x0000000000001974       0x1c zephyr/libzephyr.a(banner.c.obj)
                0x0000000000001974                boot_banner
 .text.nrf_cc3xx_platform_abort_init
                0x0000000000001990        0xc zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
                0x0000000000001990                nrf_cc3xx_platform_abort_init
 .text.mutex_free_platform
                0x000000000000199c       0x54 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .text.mutex_lock_platform
                0x00000000000019f0       0x74 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .text.mutex_unlock_platform
                0x0000000000001a64       0x68 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .text.mutex_init_platform
                0x0000000000001acc       0x94 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .text.nrf_cc3xx_platform_mutex_init
                0x0000000000001b60       0x2c zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x0000000000001b60                nrf_cc3xx_platform_mutex_init
 .text.z_arm_fatal_error
                0x0000000000001b8c       0xb8 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
                0x0000000000001b8c                z_arm_fatal_error
 .text.z_SysNmiOnReset
                0x0000000000001c44        0x8 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
                0x0000000000001c44                z_SysNmiOnReset
 .text.arch_tls_stack_setup
                0x0000000000001c4c       0x40 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
                0x0000000000001c4c                arch_tls_stack_setup
 .text._HandlerModeExit
                0x0000000000001c8c       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
                0x0000000000001c8c                z_arm_int_exit
                0x0000000000001c8c                z_arm_exc_exit
 .text.mem_manage_fault.constprop.0
                0x0000000000001cb4       0xd0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .text.usage_fault.constprop.0
                0x0000000000001d84       0xbc zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .text.bus_fault.constprop.0
                0x0000000000001e40       0xd0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .text.z_arm_fault
                0x0000000000001f10      0x1d0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
                0x0000000000001f10                z_arm_fault
 .text.z_arm_fault_init
                0x00000000000020e0       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
                0x00000000000020e0                z_arm_fault_init
 .text.__fault  0x0000000000002100       0x14 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
                0x0000000000002100                z_arm_usage_fault
                0x0000000000002100                z_arm_mpu_fault
                0x0000000000002100                z_arm_exc_spurious
                0x0000000000002100                z_arm_debug_monitor
                0x0000000000002100                z_arm_hard_fault
                0x0000000000002100                z_arm_bus_fault
 .text._reset_section
                0x0000000000002114       0x60 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
                0x0000000000002114                __start
                0x0000000000002114                z_arm_reset
 .text.z_arm_clear_arm_mpu_config
                0x0000000000002174       0x24 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
                0x0000000000002174                z_arm_clear_arm_mpu_config
 .text.z_arm_init_arch_hw_at_boot
                0x0000000000002198       0x4c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
                0x0000000000002198                z_arm_init_arch_hw_at_boot
 .text.z_impl_k_thread_abort
                0x00000000000021e4       0x2c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
                0x00000000000021e4                z_impl_k_thread_abort
 .text.z_arm_pendsv
                0x0000000000002210       0x68 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
                0x0000000000002210                z_arm_pendsv
 .text.z_arm_svc
                0x0000000000002278       0x24 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
                0x0000000000002278                z_arm_svc
 .text.arch_irq_enable
                0x000000000000229c       0x1c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                0x000000000000229c                arch_irq_enable
 .text.arch_irq_disable
                0x00000000000022b8       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                0x00000000000022b8                arch_irq_disable
 .text.arch_irq_is_enabled
                0x00000000000022e0       0x1c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                0x00000000000022e0                arch_irq_is_enabled
 .text.z_arm_irq_priority_set
                0x00000000000022fc       0x2c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                0x00000000000022fc                z_arm_irq_priority_set
 .text.z_prep_c
                0x0000000000002328       0x38 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
                0x0000000000002328                z_prep_c
 .text.arch_new_thread
                0x0000000000002360       0x38 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                0x0000000000002360                arch_new_thread
 .text.arch_switch_to_main_thread
                0x0000000000002398       0x40 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                0x0000000000002398                arch_switch_to_main_thread
 .text.z_arm_cpu_idle_init
                0x00000000000023d8        0xc zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
                0x00000000000023d8                z_arm_cpu_idle_init
 .text.z_arm_interrupt_init
                0x00000000000023e4       0x18 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
                0x00000000000023e4                z_arm_interrupt_init
 .text._isr_wrapper
                0x00000000000023fc       0x24 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
                0x00000000000023fc                _isr_wrapper
 .text.z_arm_configure_static_mpu_regions
                0x0000000000002420       0x38 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
                0x0000000000002420                z_arm_configure_static_mpu_regions
 .text.region_init
                0x0000000000002458       0x34 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text.region_allocate_and_init
                0x000000000000248c       0x38 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text.mpu_configure_regions_and_partition.constprop.0
                0x00000000000024c4      0x164 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text.arm_core_mpu_enable
                0x0000000000002628       0x18 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                0x0000000000002628                arm_core_mpu_enable
 .text.arm_core_mpu_disable
                0x0000000000002640       0x14 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                0x0000000000002640                arm_core_mpu_disable
 .text.arm_core_mpu_configure_static_mpu_regions
                0x0000000000002654       0x14 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                0x0000000000002654                arm_core_mpu_configure_static_mpu_regions
 .text.arm_core_mpu_mark_areas_for_dynamic_regions
                0x0000000000002668       0xb8 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                0x0000000000002668                arm_core_mpu_mark_areas_for_dynamic_regions
 .text.z_arm_mpu_init
                0x0000000000002720      0x11c zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                0x0000000000002720                z_arm_mpu_init
 .text.cbvprintf
                0x000000000000283c       0x38 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(cbprintf.c.obj)
                0x000000000000283c                cbvprintf
 .text.z_impl_zephyr_fputc
                0x0000000000002874       0x10 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
                0x0000000000002874                z_impl_zephyr_fputc
 .text.__stdout_hook_install
                0x0000000000002884       0x18 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
                0x0000000000002884                __stdout_hook_install
 .text.malloc_prepare
                0x000000000000289c       0x24 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .text.nrf_gpio_pin_control_select
                0x00000000000028c0       0x3c zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .text.nordicsemi_nrf53_init
                0x00000000000028fc       0x5c zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .text.rtc_pretick_init
                0x0000000000002958       0x60 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .text.z_arm_on_enter_cpu_idle
                0x00000000000029b8       0x64 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
                0x00000000000029b8                z_arm_on_enter_cpu_idle
 .text.arch_busy_wait
                0x0000000000002a1c       0x24 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
                0x0000000000002a1c                arch_busy_wait
 .text.nrf53_cpunet_mgmt_init
                0x0000000000002a40       0x10 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .text.onoff_start
                0x0000000000002a50       0x6c zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .text.onoff_stop
                0x0000000000002abc       0x30 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.onoff_start
                0x0000000000002aec       0x44 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.generic_hfclk_stop
                0x0000000000002b30       0x34 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.clk_init
                0x0000000000002b64       0x64 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.api_blocking_start
                0x0000000000002bc8       0x34 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.clkstarted_handle.constprop.0
                0x0000000000002bfc       0x4c zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.generic_hfclk_start
                0x0000000000002c48       0x78 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.clock_event_handler
                0x0000000000002cc0       0x34 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.z_nrf_clock_control_get_onoff
                0x0000000000002cf4       0x10 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
                0x0000000000002cf4                z_nrf_clock_control_get_onoff
 .text.z_nrf_clock_control_lf_on
                0x0000000000002d04       0xf8 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
                0x0000000000002d04                z_nrf_clock_control_lf_on
 .text.uart_console_init
                0x0000000000002dfc       0x28 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .text.console_out
                0x0000000000002e24       0x28 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .text.gpio_nrfx_pin_interrupt_configure
                0x0000000000002e4c      0x100 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_init
                0x0000000000002f4c       0x48 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.nrfx_gpio_handler
                0x0000000000002f94       0x4c zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_pin_configure
                0x0000000000002fe0      0x188 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.get_next_rx_buffer
                0x0000000000003168       0x2c zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .text.start_transfer
                0x0000000000003194       0xc8 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .text.i2s_nrfx_init0
                0x000000000000325c       0x68 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .text.i2s_nrfx_write
                0x00000000000032c4       0xdc zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .text.data_handler0
                0x00000000000033a0      0x1a4 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .text.i2s_nrfx_configure
                0x0000000000003544      0x330 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .text.i2s_nrfx_trigger
                0x0000000000003874      0x1e8 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .text.i2s_nrfx_read
                0x0000000000003a5c       0x84 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .text.ws2812_i2s_init
                0x0000000000003ae0       0xd8 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .text.ws2812_strip_update_rgb
                0x0000000000003bb8      0x160 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .text.nrf_gpio_pin_port_decode
                0x0000000000003d18       0x24 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .text.pinctrl_configure_pins
                0x0000000000003d3c      0x1d8 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
                0x0000000000003d3c                pinctrl_configure_pins
 .text.uarte_nrfx_poll_out
                0x0000000000003f14       0xb4 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .text.uarte_0_init
                0x0000000000003fc8      0x120 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .text.compare_int_lock
                0x00000000000040e8       0x40 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.sys_clock_timeout_handler
                0x0000000000004128       0x48 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.compare_int_unlock
                0x0000000000004170       0x4c zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.z_nrf_rtc_timer_read
                0x00000000000041bc       0x44 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                0x00000000000041bc                z_nrf_rtc_timer_read
 .text.compare_set
                0x0000000000004200      0x124 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.sys_clock_driver_init
                0x0000000000004324       0x90 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.rtc_nrf_isr
                0x00000000000043b4       0xd0 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                0x00000000000043b4                rtc_nrf_isr
 .text.sys_clock_set_timeout
                0x0000000000004484       0x5c zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                0x0000000000004484                sys_clock_set_timeout
 .text.sys_clock_elapsed
                0x00000000000044e0       0x14 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                0x00000000000044e0                sys_clock_elapsed
 .text.nrf53_errata_42
                0x00000000000044f4       0x24 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .text.SystemInit
                0x0000000000004518      0x1c4 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
                0x0000000000004518                SystemInit
 .text.nrfx_flag32_alloc
                0x00000000000046dc       0x40 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
                0x00000000000046dc                nrfx_flag32_alloc
 .text.nrfx_flag32_free
                0x000000000000471c       0x38 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
                0x000000000000471c                nrfx_flag32_free
 .text.nrfx_gppi_channels_enable
                0x0000000000004754        0xc modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                0x0000000000004754                nrfx_gppi_channels_enable
 .text.nrfx_gppi_channel_alloc
                0x0000000000004760        0xc modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                0x0000000000004760                nrfx_gppi_channel_alloc
 .text.clock_stop
                0x000000000000476c       0xf4 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .text.nrfx_clock_init
                0x0000000000004860       0x20 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                0x0000000000004860                nrfx_clock_init
 .text.nrfx_clock_enable
                0x0000000000004880       0x30 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                0x0000000000004880                nrfx_clock_enable
 .text.nrfx_clock_start
                0x00000000000048b0       0xd0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                0x00000000000048b0                nrfx_clock_start
 .text.nrfx_power_clock_irq_handler
                0x0000000000004980       0x70 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                0x0000000000004980                nrfx_power_clock_irq_handler
 .text.nrfx_dppi_channel_alloc
                0x00000000000049f0       0x10 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
                0x00000000000049f0                nrfx_dppi_channel_alloc
 .text.nrf_gpio_pin_port_decode
                0x0000000000004a00       0x24 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.get_pin_idx
                0x0000000000004a24       0x14 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.call_handler.constprop.0
                0x0000000000004a38       0x40 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_te_get.constprop.0
                0x0000000000004a78       0x18 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_is_output.constprop.0
                0x0000000000004a90       0x18 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_in_use_by_te.constprop.0
                0x0000000000004aa8       0x18 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.latch_pending_read_and_check
                0x0000000000004ac0       0x3c modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.release_handler.isra.0
                0x0000000000004afc       0x58 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.__nrfy_internal_gpiote_events_process.constprop.0
                0x0000000000004b54       0x5c modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_uninit
                0x0000000000004bb0       0x84 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_input_configure
                0x0000000000004c34      0x178 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000004c34                nrfx_gpiote_input_configure
 .text.nrfx_gpiote_output_configure
                0x0000000000004dac      0x104 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000004dac                nrfx_gpiote_output_configure
 .text.nrfx_gpiote_global_callback_set
                0x0000000000004eb0        0xc modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000004eb0                nrfx_gpiote_global_callback_set
 .text.nrfx_gpiote_channel_get
                0x0000000000004ebc       0x34 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000004ebc                nrfx_gpiote_channel_get
 .text.nrfx_gpiote_init
                0x0000000000004ef0       0x64 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000004ef0                nrfx_gpiote_init
 .text.nrfx_gpiote_init_check
                0x0000000000004f54       0x14 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000004f54                nrfx_gpiote_init_check
 .text.nrfx_gpiote_channel_free
                0x0000000000004f68        0xc modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000004f68                nrfx_gpiote_channel_free
 .text.nrfx_gpiote_channel_alloc
                0x0000000000004f74        0xc modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000004f74                nrfx_gpiote_channel_alloc
 .text.nrfx_gpiote_trigger_enable
                0x0000000000004f80       0xac modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x0000000000004f80                nrfx_gpiote_trigger_enable
 .text.nrfx_gpiote_0_irq_handler
                0x000000000000502c      0x1b8 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x000000000000502c                nrfx_gpiote_0_irq_handler
 .text.nrf_gpio_cfg.constprop.0
                0x00000000000051e4       0x44 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .text.nrfx_i2s_init
                0x0000000000005228      0x1a4 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
                0x0000000000005228                nrfx_i2s_init
 .text.nrfx_i2s_start
                0x00000000000053cc       0xe8 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
                0x00000000000053cc                nrfx_i2s_start
 .text.nrfx_i2s_next_buffers_set
                0x00000000000054b4       0x84 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
                0x00000000000054b4                nrfx_i2s_next_buffers_set
 .text.nrfx_i2s_stop
                0x0000000000005538       0x24 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
                0x0000000000005538                nrfx_i2s_stop
 .text.nrfx_i2s_uninit
                0x000000000000555c       0x7c modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
                0x000000000000555c                nrfx_i2s_uninit
 .text.nrfx_i2s_0_irq_handler
                0x00000000000055d8      0x120 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
                0x00000000000055d8                nrfx_i2s_0_irq_handler
 .text.k_sys_fatal_error_handler
                0x00000000000056f8       0x20 zephyr/kernel/libkernel.a(fatal.c.obj)
                0x00000000000056f8                k_sys_fatal_error_handler
 .text.z_fatal_error
                0x0000000000005718       0xe8 zephyr/kernel/libkernel.a(fatal.c.obj)
                0x0000000000005718                z_fatal_error
 .text.z_sys_init_run_level
                0x0000000000005800       0x2c zephyr/kernel/libkernel.a(init.c.obj)
 .text.bg_thread_main
                0x000000000000582c       0xc4 zephyr/kernel/libkernel.a(init.c.obj)
 .text.z_bss_zero
                0x00000000000058f0       0x18 zephyr/kernel/libkernel.a(init.c.obj)
                0x00000000000058f0                z_bss_zero
 .text.z_init_cpu
                0x0000000000005908       0x68 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000000005908                z_init_cpu
 .text.z_cstart
                0x0000000000005970       0xe0 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000000005970                z_cstart
 .text.init_mem_slab_obj_core_list
                0x0000000000005a50       0x24 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .text.k_mem_slab_alloc
                0x0000000000005a74       0x5c zephyr/kernel/libkernel.a(mem_slab.c.obj)
                0x0000000000005a74                k_mem_slab_alloc
 .text.z_impl_k_msgq_put
                0x0000000000005ad0       0xac zephyr/kernel/libkernel.a(msg_q.c.obj)
                0x0000000000005ad0                z_impl_k_msgq_put
 .text.z_impl_k_msgq_get
                0x0000000000005b7c       0xc4 zephyr/kernel/libkernel.a(msg_q.c.obj)
                0x0000000000005b7c                z_impl_k_msgq_get
 .text.z_impl_k_mutex_lock
                0x0000000000005c40       0xf0 zephyr/kernel/libkernel.a(mutex.c.obj)
                0x0000000000005c40                z_impl_k_mutex_lock
 .text.z_impl_k_mutex_unlock
                0x0000000000005d30       0xa0 zephyr/kernel/libkernel.a(mutex.c.obj)
                0x0000000000005d30                z_impl_k_mutex_unlock
 .text.z_impl_k_sem_give
                0x0000000000005dd0       0x68 zephyr/kernel/libkernel.a(sem.c.obj)
                0x0000000000005dd0                z_impl_k_sem_give
 .text.z_impl_k_sem_take
                0x0000000000005e38       0x4c zephyr/kernel/libkernel.a(sem.c.obj)
                0x0000000000005e38                z_impl_k_sem_take
 .text.z_setup_new_thread
                0x0000000000005e84       0x7c zephyr/kernel/libkernel.a(thread.c.obj)
                0x0000000000005e84                z_setup_new_thread
 .text.z_impl_k_thread_create
                0x0000000000005f00       0x58 zephyr/kernel/libkernel.a(thread.c.obj)
                0x0000000000005f00                z_impl_k_thread_create
 .text.z_swap_irqlock
                0x0000000000005f58       0x30 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.ready_thread
                0x0000000000005f88       0x90 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.unready_thread
                0x0000000000006018       0x54 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.move_thread_to_end_of_prio_q
                0x000000000000606c       0x90 zephyr/kernel/libkernel.a(sched.c.obj)
                0x000000000000606c                move_thread_to_end_of_prio_q
 .text.z_pend_curr
                0x00000000000060fc       0x58 zephyr/kernel/libkernel.a(sched.c.obj)
                0x00000000000060fc                z_pend_curr
 .text.z_thread_prio_set
                0x0000000000006154       0x9c zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006154                z_thread_prio_set
 .text.z_reschedule
                0x00000000000061f0       0x24 zephyr/kernel/libkernel.a(sched.c.obj)
                0x00000000000061f0                z_reschedule
 .text.z_reschedule_irqlock
                0x0000000000006214       0x28 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006214                z_reschedule_irqlock
 .text.k_sched_lock
                0x000000000000623c       0x28 zephyr/kernel/libkernel.a(sched.c.obj)
                0x000000000000623c                k_sched_lock
 .text.k_sched_unlock
                0x0000000000006264       0x68 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006264                k_sched_unlock
 .text.z_sched_init
                0x00000000000062cc       0x10 zephyr/kernel/libkernel.a(sched.c.obj)
                0x00000000000062cc                z_sched_init
 .text.z_impl_k_yield
                0x00000000000062dc       0x88 zephyr/kernel/libkernel.a(sched.c.obj)
                0x00000000000062dc                z_impl_k_yield
 .text.z_tick_sleep
                0x0000000000006364       0x80 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_impl_k_usleep
                0x00000000000063e4       0x3c zephyr/kernel/libkernel.a(sched.c.obj)
                0x00000000000063e4                z_impl_k_usleep
 .text.z_impl_k_wakeup
                0x0000000000006420       0x4c zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006420                z_impl_k_wakeup
 .text.z_impl_k_sched_current_thread_query
                0x000000000000646c        0xc zephyr/kernel/libkernel.a(sched.c.obj)
                0x000000000000646c                z_impl_k_sched_current_thread_query
 .text.z_thread_abort
                0x0000000000006478      0x110 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000006478                z_thread_abort
 .text.slice_timeout
                0x0000000000006588       0x20 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .text.thread_is_sliceable
                0x00000000000065a8       0x3c zephyr/kernel/libkernel.a(timeslicing.c.obj)
                0x00000000000065a8                thread_is_sliceable
 .text.z_reset_time_slice
                0x00000000000065e4       0x50 zephyr/kernel/libkernel.a(timeslicing.c.obj)
                0x00000000000065e4                z_reset_time_slice
 .text.z_time_slice
                0x0000000000006634       0x60 zephyr/kernel/libkernel.a(timeslicing.c.obj)
                0x0000000000006634                z_time_slice
 .text.z_data_copy
                0x0000000000006694       0x38 zephyr/kernel/libkernel.a(xip.c.obj)
                0x0000000000006694                z_data_copy
 .text.first    0x00000000000066cc       0x10 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.elapsed  0x00000000000066dc       0x14 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.remove_timeout
                0x00000000000066f0       0x38 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.z_add_timeout
                0x0000000000006728       0xec zephyr/kernel/libkernel.a(timeout.c.obj)
                0x0000000000006728                z_add_timeout
 .text.sys_clock_announce
                0x0000000000006814       0xbc zephyr/kernel/libkernel.a(timeout.c.obj)
                0x0000000000006814                sys_clock_announce
 .text.sys_clock_tick_get
                0x00000000000068d0       0x30 zephyr/kernel/libkernel.a(timeout.c.obj)
                0x00000000000068d0                sys_clock_tick_get
 .text.z_timer_expiration_handler
                0x0000000000006900       0xe0 zephyr/kernel/libkernel.a(timer.c.obj)
                0x0000000000006900                z_timer_expiration_handler
 .text.z_impl_k_timer_start
                0x00000000000069e0       0x74 zephyr/kernel/libkernel.a(timer.c.obj)
                0x00000000000069e0                z_impl_k_timer_start
 .text.statics_init
                0x0000000000006a54       0x24 zephyr/kernel/libkernel.a(kheap.c.obj)
 .text.nrf_cc3xx_platform_init_no_rng
                0x0000000000006a78       0x38 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
                0x0000000000006a78                nrf_cc3xx_platform_init_no_rng
 .text.nrf_cc3xx_platform_abort
                0x0000000000006ab0       0x24 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .text.CC_PalAbort
                0x0000000000006ad4       0x44 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
                0x0000000000006ad4                CC_PalAbort
 .text.nrf_cc3xx_platform_set_abort
                0x0000000000006b18       0x10 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
                0x0000000000006b18                nrf_cc3xx_platform_set_abort
 .text.mutex_free
                0x0000000000006b28       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .text.mutex_lock
                0x0000000000006b5c       0x48 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .text.mutex_unlock
                0x0000000000006ba4       0x38 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .text.mutex_init
                0x0000000000006bdc       0x20 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .text.nrf_cc3xx_platform_set_mutexes
                0x0000000000006bfc       0x78 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
                0x0000000000006bfc                nrf_cc3xx_platform_set_mutexes
 .text.CC_LibInitNoRng
                0x0000000000006c74       0x28 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
                0x0000000000006c74                CC_LibInitNoRng
 .text.CC_HalInit
                0x0000000000006c9c        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
                0x0000000000006c9c                CC_HalInit
 .text.CC_PalInit
                0x0000000000006ca0       0x5c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x0000000000006ca0                CC_PalInit
 .text.CC_PalTerminate
                0x0000000000006cfc       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x0000000000006cfc                CC_PalTerminate
 .text.CC_PalDmaInit
                0x0000000000006d30        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
                0x0000000000006d30                CC_PalDmaInit
 .text.CC_PalDmaTerminate
                0x0000000000006d34        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
                0x0000000000006d34                CC_PalDmaTerminate
 .text.CC_PalMutexCreate
                0x0000000000006d38       0x14 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
                0x0000000000006d38                CC_PalMutexCreate
 .text.CC_PalMutexDestroy
                0x0000000000006d4c       0x14 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
                0x0000000000006d4c                CC_PalMutexDestroy
 .text.CC_PalPowerSaveModeInit
                0x0000000000006d60       0x3c C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
                0x0000000000006d60                CC_PalPowerSaveModeInit
 .text.snprintf
                0x0000000000006d9c       0x58 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_snprintf.c.o)
                0x0000000000006d9c                snprintf
 .text.__l_vfprintf
                0x0000000000006df4      0x4b4 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
                0x0000000000006df4                __l_vfprintf
 .text._OffsetAbsSyms
                0x00000000000072a8        0x2 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
                0x00000000000072a8                _OffsetAbsSyms
 .text.button_pressed
                0x00000000000072aa        0x4 app/libapp.a(button.c.obj)
                0x00000000000072aa                button_pressed
 .text.gpio_pin_interrupt_configure_dt.constprop.0
                0x00000000000072ae       0x34 app/libapp.a(button.c.obj)
 .text.chunk_field
                0x00000000000072e2       0x16 zephyr/libzephyr.a(heap.c.obj)
 .text.chunk_set
                0x00000000000072f8       0x16 zephyr/libzephyr.a(heap.c.obj)
 .text.chunk_size
                0x000000000000730e        0xc zephyr/libzephyr.a(heap.c.obj)
 .text.set_chunk_used
                0x000000000000731a       0x30 zephyr/libzephyr.a(heap.c.obj)
 .text.set_chunk_size
                0x000000000000734a        0x8 zephyr/libzephyr.a(heap.c.obj)
 .text.bucket_idx.isra.0
                0x0000000000007352       0x1c zephyr/libzephyr.a(heap.c.obj)
 .text.free_list_add
                0x000000000000736e       0x84 zephyr/libzephyr.a(heap.c.obj)
 .text.sys_heap_init
                0x00000000000073f2       0xb0 zephyr/libzephyr.a(heap.c.obj)
                0x00000000000073f2                sys_heap_init
 .text.cbpprintf_external
                0x00000000000074a2       0x62 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
                0x00000000000074a2                cbpprintf_external
 .text.is_ptr   0x0000000000007504       0x4a zephyr/libzephyr.a(cbprintf_packaged.c.obj)
                0x0000000000007504                is_ptr
 .text.arch_printk_char_out
                0x000000000000754e        0x4 zephyr/libzephyr.a(printk.c.obj)
                0x000000000000754e                arch_printk_char_out
 .text.printk   0x0000000000007552       0x1a zephyr/libzephyr.a(printk.c.obj)
                0x0000000000007552                printk
 .text.free_space
                0x000000000000756c       0x26 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .text.get_usage
                0x0000000000007592       0x22 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .text.idx_inc  0x00000000000075b4       0x1c zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .text.rd_idx_inc
                0x00000000000075d0       0x18 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .text.add_skip_item
                0x00000000000075e8       0x3e zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .text.drop_item_locked
                0x0000000000007626       0xee zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .text.post_drop_action
                0x0000000000007714       0x36 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .text.max_utilization_update
                0x000000000000774a       0x1a zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .text.mpsc_pbuf_init
                0x0000000000007764       0x42 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
                0x0000000000007764                mpsc_pbuf_init
 .text.mpsc_pbuf_alloc
                0x00000000000077a6       0xfe zephyr/libzephyr.a(mpsc_pbuf.c.obj)
                0x00000000000077a6                mpsc_pbuf_alloc
 .text.mpsc_pbuf_commit
                0x00000000000078a4       0x3e zephyr/libzephyr.a(mpsc_pbuf.c.obj)
                0x00000000000078a4                mpsc_pbuf_commit
 .text.mpsc_pbuf_claim
                0x00000000000078e2       0xa8 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
                0x00000000000078e2                mpsc_pbuf_claim
 .text.mpsc_pbuf_free
                0x000000000000798a       0x76 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
                0x000000000000798a                mpsc_pbuf_free
 .text.mpsc_pbuf_is_pending
                0x0000000000007a00       0x32 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
                0x0000000000007a00                mpsc_pbuf_is_pending
 .text.process_recheck
                0x0000000000007a32       0x38 zephyr/libzephyr.a(onoff.c.obj)
 .text.validate_args
                0x0000000000007a6a       0x20 zephyr/libzephyr.a(onoff.c.obj)
 .text.notify_one
                0x0000000000007a8a       0x2c zephyr/libzephyr.a(onoff.c.obj)
 .text.transition_complete
                0x0000000000007ab6       0x1a zephyr/libzephyr.a(onoff.c.obj)
 .text.onoff_manager_init
                0x0000000000007ad0       0x26 zephyr/libzephyr.a(onoff.c.obj)
                0x0000000000007ad0                onoff_manager_init
 .text.onoff_request
                0x0000000000007af6       0xae zephyr/libzephyr.a(onoff.c.obj)
                0x0000000000007af6                onoff_request
 .text.onoff_release
                0x0000000000007ba4       0x48 zephyr/libzephyr.a(onoff.c.obj)
                0x0000000000007ba4                onoff_release
 .text.sys_notify_validate
                0x0000000000007bec       0x22 zephyr/libzephyr.a(notify.c.obj)
                0x0000000000007bec                sys_notify_validate
 .text.sys_notify_finalize
                0x0000000000007c0e       0x1a zephyr/libzephyr.a(notify.c.obj)
                0x0000000000007c0e                sys_notify_finalize
 .text._ConfigAbsSyms
                0x0000000000007c28        0x2 zephyr/libzephyr.a(configs.c.obj)
                0x0000000000007c28                _ConfigAbsSyms
 .text.log_msg_generic_get_wlen
                0x0000000000007c2a       0x20 zephyr/libzephyr.a(log_core.c.obj)
 .text.dummy_timestamp
                0x0000000000007c4a        0x4 zephyr/libzephyr.a(log_core.c.obj)
 .text.default_get_timestamp
                0x0000000000007c4e        0x4 zephyr/libzephyr.a(log_core.c.obj)
 .text.atomic_inc
                0x0000000000007c52       0x12 zephyr/libzephyr.a(log_core.c.obj)
 .text.z_log_vprintk
                0x0000000000007c64       0x1c zephyr/libzephyr.a(log_core.c.obj)
                0x0000000000007c64                z_log_vprintk
 .text.z_log_notify_drop
                0x0000000000007c80        0x6 zephyr/libzephyr.a(log_core.c.obj)
 .text.z_log_get_tag
                0x0000000000007c86        0x4 zephyr/libzephyr.a(log_core.c.obj)
                0x0000000000007c86                z_log_get_tag
 .text.z_cbprintf_cpy
                0x0000000000007c8a       0x2c zephyr/libzephyr.a(log_msg.c.obj)
 .text.z_log_msg_finalize
                0x0000000000007cb6       0x32 zephyr/libzephyr.a(log_msg.c.obj)
                0x0000000000007cb6                z_log_msg_finalize
 .text.z_log_msg_simple_create
                0x0000000000007ce8       0x6e zephyr/libzephyr.a(log_msg.c.obj)
 .text.z_impl_z_log_msg_simple_create_0
                0x0000000000007d56       0x12 zephyr/libzephyr.a(log_msg.c.obj)
                0x0000000000007d56                z_impl_z_log_msg_simple_create_0
 .text.z_impl_z_log_msg_simple_create_1
                0x0000000000007d68       0x14 zephyr/libzephyr.a(log_msg.c.obj)
                0x0000000000007d68                z_impl_z_log_msg_simple_create_1
 .text.z_impl_z_log_msg_simple_create_2
                0x0000000000007d7c       0x18 zephyr/libzephyr.a(log_msg.c.obj)
                0x0000000000007d7c                z_impl_z_log_msg_simple_create_2
 .text.log_output_write
                0x0000000000007d94       0x1c zephyr/libzephyr.a(log_output.c.obj)
 .text.log_output_flush
                0x0000000000007db0       0x1a zephyr/libzephyr.a(log_output.c.obj)
 .text.out_func
                0x0000000000007dca       0x2e zephyr/libzephyr.a(log_output.c.obj)
 .text.cr_out_func
                0x0000000000007df8       0x1c zephyr/libzephyr.a(log_output.c.obj)
 .text.log_output_msg_process
                0x0000000000007e14       0x6a zephyr/libzephyr.a(log_output.c.obj)
                0x0000000000007e14                log_output_msg_process
 .text.format_set
                0x0000000000007e7e        0xc zephyr/libzephyr.a(log_backend_uart.c.obj)
 .text.log_backend_uart_init
                0x0000000000007e8a        0xc zephyr/libzephyr.a(log_backend_uart.c.obj)
 .text.panic    0x0000000000007e96       0x32 zephyr/libzephyr.a(log_backend_uart.c.obj)
 .text.dropped  0x0000000000007ec8        0xa zephyr/libzephyr.a(log_backend_uart.c.obj)
 .text.process  0x0000000000007ed2       0x1e zephyr/libzephyr.a(log_backend_uart.c.obj)
 .text.abort_function
                0x0000000000007ef0        0x2 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .text.z_do_kernel_oops
                0x0000000000007ef2        0x8 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
                0x0000000000007ef2                z_do_kernel_oops
 .text.z_arm_nmi
                0x0000000000007efa        0xe zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
                0x0000000000007efa                z_arm_nmi
 .text.z_log_msg_simple_create_0.constprop.0
                0x0000000000007f08        0x8 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .text.z_irq_spurious
                0x0000000000007f10        0x8 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                0x0000000000007f10                z_irq_spurious
 .text.configure_builtin_stack_guard
                0x0000000000007f18        0x8 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                0x0000000000007f18                configure_builtin_stack_guard
 .text.arch_irq_unlock_outlined
                0x0000000000007f20        0xe zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                0x0000000000007f20                arch_irq_unlock_outlined
 .text.arch_cpu_idle
                0x0000000000007f2e       0x32 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
                0x0000000000007f2e                arch_cpu_idle
 .text.arch_cpu_atomic_idle
                0x0000000000007f60       0x38 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
                0x0000000000007f60                arch_cpu_atomic_idle
 .text.arm_cmse_mpu_region_get
                0x0000000000007f98       0x12 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
                0x0000000000007f98                arm_cmse_mpu_region_get
 .text.mpu_configure_region
                0x0000000000007faa       0x32 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .text.cbputc   0x0000000000007fdc        0xc zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(cbprintf.c.obj)
 .text.picolibc_put
                0x0000000000007fe8        0xa zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .text.onoff_stop
                0x0000000000007ff2       0x10 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .text.get_status
                0x0000000000008002       0x12 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.stop     0x0000000000008014       0x50 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.api_stop
                0x0000000000008064        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.async_start
                0x000000000000806a       0x5a zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.api_start
                0x00000000000080c4        0xe zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.onoff_started_callback
                0x00000000000080d2       0x12 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.hfclkaudio_start
                0x00000000000080e4        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.hfclk192m_start
                0x00000000000080ea        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.lfclk_start
                0x00000000000080f0        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.hfclkaudio_stop
                0x00000000000080f6        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.hfclk192m_stop
                0x00000000000080fc        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.lfclk_stop
                0x0000000000008102        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.blocking_start_callback
                0x0000000000008108        0x6 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .text.gpio_nrfx_port_get_raw
                0x000000000000810e        0xc zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_port_set_masked_raw
                0x000000000000811a       0x14 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_port_set_bits_raw
                0x000000000000812e        0xa zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_port_clear_bits_raw
                0x0000000000008138        0xa zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_port_toggle_bits
                0x0000000000008142       0x14 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.gpio_nrfx_manage_callback
                0x0000000000008156       0x52 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .text.i2s_nrfx_config_get
                0x00000000000081a8       0x2c zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .text.get_next_tx_buffer
                0x00000000000081d4       0x26 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .text.z_log_msg_simple_create_1.constprop.0
                0x00000000000081fa        0xa zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .text.supply_next_buffers
                0x0000000000008204       0x4a zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .text.z_log_msg_simple_create_0.constprop.0
                0x000000000000824e        0x8 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .text.clock_started_callback
                0x0000000000008256       0x2a zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .text.purge_queue.isra.0
                0x0000000000008280       0x52 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .text.ws2812_strip_length
                0x00000000000082d2        0x6 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .text.pinctrl_lookup_state
                0x00000000000082d8       0x26 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
                0x00000000000082d8                pinctrl_lookup_state
 .text.uarte_nrfx_err_check
                0x00000000000082fe        0xe zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .text.uarte_nrfx_poll_in
                0x000000000000830c       0x26 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .text.is_tx_ready.isra.0
                0x0000000000008332       0x20 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .text.uarte_nrfx_isr_int
                0x0000000000008352       0x42 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .text.event_clear
                0x0000000000008394       0x18 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .text.sys_clock_cycle_get_32
                0x00000000000083ac        0x8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                0x00000000000083ac                sys_clock_cycle_get_32
 .text.hw_cc3xx_init_internal
                0x00000000000083b4        0x4 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .text.hw_cc3xx_init
                0x00000000000083b8       0x12 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .text.nrfx_isr
                0x00000000000083ca        0x2 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
                0x00000000000083ca                nrfx_isr
 .text.nrfx_busy_wait
                0x00000000000083cc        0x4 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
                0x00000000000083cc                nrfx_busy_wait
 .text.nrfx_gppi_event_endpoint_setup
                0x00000000000083d0        0xa modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                0x00000000000083d0                nrfx_gppi_event_endpoint_setup
 .text.nrfx_gppi_task_endpoint_setup
                0x00000000000083da        0xa modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                0x00000000000083da                nrfx_gppi_task_endpoint_setup
 .text.nrfx_gppi_channel_endpoints_setup
                0x00000000000083e4        0xe modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                0x00000000000083e4                nrfx_gppi_channel_endpoints_setup
 .text.nrfx_clock_stop
                0x00000000000083f2        0x4 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                0x00000000000083f2                nrfx_clock_stop
 .text.nrf_gpio_reconfigure
                0x00000000000083f6       0x92 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrf_gpio_cfg_sense_set
                0x0000000000008488       0x1e modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.pin_trigger_disable
                0x00000000000084a6       0x48 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .text.nrfx_gpiote_pin_uninit
                0x00000000000084ee        0x4 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x00000000000084ee                nrfx_gpiote_pin_uninit
 .text.nrfx_gpiote_trigger_disable
                0x00000000000084f2        0x4 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                0x00000000000084f2                nrfx_gpiote_trigger_disable
 .text.nrf_gpio_cfg_input.constprop.0
                0x00000000000084f6        0x8 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .text.nrf_gpio_cfg_default
                0x00000000000084fe        0x8 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .text.nrf_gpio_cfg_output
                0x0000000000008506        0x8 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .text.z_impl_k_busy_wait
                0x000000000000850e        0x8 zephyr/kernel/libkernel.a(busy_wait.c.obj)
                0x000000000000850e                z_impl_k_busy_wait
 .text.z_device_state_init
                0x0000000000008516        0x2 zephyr/kernel/libkernel.a(device.c.obj)
                0x0000000000008516                z_device_state_init
 .text.z_impl_device_is_ready
                0x0000000000008518       0x16 zephyr/kernel/libkernel.a(device.c.obj)
                0x0000000000008518                z_impl_device_is_ready
 .text.arch_system_halt
                0x000000000000852e       0x10 zephyr/kernel/libkernel.a(fatal.c.obj)
                0x000000000000852e                arch_system_halt
 .text.do_device_init
                0x000000000000853e       0x30 zephyr/kernel/libkernel.a(init.c.obj)
 .text.z_early_memset
                0x000000000000856e        0x4 zephyr/kernel/libkernel.a(init.c.obj)
                0x000000000000856e                z_early_memset
 .text.z_early_memcpy
                0x0000000000008572        0x4 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000000008572                z_early_memcpy
 .text.z_init_static
                0x0000000000008576        0x2 zephyr/kernel/libkernel.a(init_static.c.obj)
                0x0000000000008576                z_init_static
 .text.create_free_list
                0x0000000000008578       0x34 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .text.k_mem_slab_init
                0x00000000000085ac       0x1c zephyr/kernel/libkernel.a(mem_slab.c.obj)
                0x00000000000085ac                k_mem_slab_init
 .text.k_mem_slab_free
                0x00000000000085c8       0x6e zephyr/kernel/libkernel.a(mem_slab.c.obj)
                0x00000000000085c8                k_mem_slab_free
 .text.idle     0x0000000000008636       0x16 zephyr/kernel/libkernel.a(idle.c.obj)
                0x0000000000008636                idle
 .text.unpend_thread_no_timeout
                0x000000000000864c       0x1a zephyr/kernel/libkernel.a(msg_q.c.obj)
 .text.k_msgq_init
                0x0000000000008666       0x1e zephyr/kernel/libkernel.a(msg_q.c.obj)
                0x0000000000008666                k_msgq_init
 .text.adjust_owner_prio.isra.0
                0x0000000000008684       0x10 zephyr/kernel/libkernel.a(mutex.c.obj)
 .text.z_impl_k_mutex_init
                0x0000000000008694        0xe zephyr/kernel/libkernel.a(mutex.c.obj)
                0x0000000000008694                z_impl_k_mutex_init
 .text.z_impl_k_sem_init
                0x00000000000086a2       0x18 zephyr/kernel/libkernel.a(sem.c.obj)
                0x00000000000086a2                z_impl_k_sem_init
 .text.k_is_in_isr
                0x00000000000086ba        0xc zephyr/kernel/libkernel.a(thread.c.obj)
                0x00000000000086ba                k_is_in_isr
 .text.z_impl_k_thread_name_set
                0x00000000000086c6        0x6 zephyr/kernel/libkernel.a(thread.c.obj)
                0x00000000000086c6                z_impl_k_thread_name_set
 .text.k_thread_name_get
                0x00000000000086cc        0x4 zephyr/kernel/libkernel.a(thread.c.obj)
                0x00000000000086cc                k_thread_name_get
 .text.sys_dlist_remove
                0x00000000000086d0       0x10 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.unpend_thread_no_timeout
                0x00000000000086e0       0x14 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.add_to_waitq_locked
                0x00000000000086f4       0x50 zephyr/kernel/libkernel.a(sched.c.obj)
 .text.z_ready_thread
                0x0000000000008744       0x1e zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000008744                z_ready_thread
 .text.z_unpend_thread_no_timeout
                0x0000000000008762       0x22 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000008762                z_unpend_thread_no_timeout
 .text.z_sched_wake_thread
                0x0000000000008784       0x3a zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000000008784                z_sched_wake_thread
 .text.z_thread_timeout
                0x00000000000087be        0x8 zephyr/kernel/libkernel.a(sched.c.obj)
                0x00000000000087be                z_thread_timeout
 .text.z_unpend1_no_timeout
                0x00000000000087c6       0x2c zephyr/kernel/libkernel.a(sched.c.obj)
                0x00000000000087c6                z_unpend1_no_timeout
 .text.z_impl_k_sleep
                0x00000000000087f2       0x32 zephyr/kernel/libkernel.a(sched.c.obj)
                0x00000000000087f2                z_impl_k_sleep
 .text.next_timeout
                0x0000000000008824       0x32 zephyr/kernel/libkernel.a(timeout.c.obj)
 .text.z_abort_timeout
                0x0000000000008856       0x42 zephyr/kernel/libkernel.a(timeout.c.obj)
                0x0000000000008856                z_abort_timeout
 .text.sys_clock_tick_get_32
                0x0000000000008898        0x8 zephyr/kernel/libkernel.a(timeout.c.obj)
                0x0000000000008898                sys_clock_tick_get_32
 .text.z_impl_k_uptime_ticks
                0x00000000000088a0        0x4 zephyr/kernel/libkernel.a(timeout.c.obj)
                0x00000000000088a0                z_impl_k_uptime_ticks
 .text.k_timer_init
                0x00000000000088a4       0x18 zephyr/kernel/libkernel.a(timer.c.obj)
                0x00000000000088a4                k_timer_init
 .text.z_impl_k_timer_stop
                0x00000000000088bc       0x38 zephyr/kernel/libkernel.a(timer.c.obj)
                0x00000000000088bc                z_impl_k_timer_stop
 .text.k_heap_init
                0x00000000000088f4       0x10 zephyr/kernel/libkernel.a(kheap.c.obj)
                0x00000000000088f4                k_heap_init
 .text.memcpy   0x0000000000008904       0x1a c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memcpy.c.o)
                0x0000000000008904                __aeabi_memcpy
                0x0000000000008904                __aeabi_memcpy4
                0x0000000000008904                __aeabi_memcpy8
                0x0000000000008904                memcpy
 .text.memset   0x000000000000891e       0x10 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memset.c.o)
                0x000000000000891e                memset
 .text.memcmp   0x000000000000892e       0x20 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_memcmp.c.o)
                0x000000000000892e                memcmp
 .text.strnlen  0x000000000000894e       0x18 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strnlen.c.o)
                0x000000000000894e                strnlen
 .text.__ultoa_invert
                0x0000000000008966       0xac c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
 .text.__file_str_put
                0x0000000000008a12       0x10 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_filestrput.c.o)
                0x0000000000008a12                __file_str_put
 *(SORT_BY_ALIGNMENT(.TEXT.*))
 *fill*         0x0000000000008a22        0x2 
 .TEXT.__aeabi_read_tp
                0x0000000000008a24        0xc zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
                0x0000000000008a24                __aeabi_read_tp
 *(SORT_BY_ALIGNMENT(.gnu.linkonce.t.*))
 *(SORT_BY_ALIGNMENT(.glue_7t))
 .glue_7t       0x0000000000008a30        0x0 linker stubs
 *(SORT_BY_ALIGNMENT(.glue_7))
 .glue_7        0x0000000000008a30        0x0 linker stubs
 *(SORT_BY_ALIGNMENT(.vfp11_veneer))
 .vfp11_veneer  0x0000000000008a30        0x0 linker stubs
 *(SORT_BY_ALIGNMENT(.v4_bx))
 .v4_bx         0x0000000000008a30        0x0 linker stubs
                0x0000000000008a30                . = ALIGN (0x4)
                0x0000000000008a30                __text_region_end = .

.ARM.exidx      0x0000000000008a30        0x8
                0x0000000000008a30                __exidx_start = .
 *(SORT_BY_ALIGNMENT(.ARM.exidx*) SORT_BY_ALIGNMENT(gnu.linkonce.armexidx.*))
 .ARM.exidx     0x0000000000008a30        0x8 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strlen.S.o)
 .ARM.exidx     0x0000000000008a38        0x0 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
                                          0x8 (size before relaxing)
                0x0000000000008a38                __exidx_end = .
                0x0000000000008a38                __rodata_region_start = .

initlevel       0x0000000000008a38       0x88
                0x0000000000008a38                __init_start = .
                0x0000000000008a38                __init_EARLY_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_EARLY?_*)))
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_EARLY??_*)))
                0x0000000000008a38                __init_PRE_KERNEL_1_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_PRE_KERNEL_1?_*)))
 .z_init_PRE_KERNEL_10_0_
                0x0000000000008a38        0x8 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .z_init_PRE_KERNEL_10_0_
                0x0000000000008a40        0x8 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_PRE_KERNEL_1??_*)))
 .z_init_PRE_KERNEL_130_00081_
                0x0000000000008a48        0x8 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .z_init_PRE_KERNEL_130_0_
                0x0000000000008a50        0x8 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .z_init_PRE_KERNEL_130_0_
                0x0000000000008a58        0x8 zephyr/kernel/libkernel.a(kheap.c.obj)
 .z_init_PRE_KERNEL_140_00012_
                0x0000000000008a60        0x8 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .z_init_PRE_KERNEL_140_00033_
                0x0000000000008a68        0x8 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .z_init_PRE_KERNEL_140_0_
                0x0000000000008a70        0x8 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .z_init_PRE_KERNEL_150_00117_
                0x0000000000008a78        0x8 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .z_init_PRE_KERNEL_160_0_
                0x0000000000008a80        0x8 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
                0x0000000000008a88                __init_PRE_KERNEL_2_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_PRE_KERNEL_2?_*)))
 .z_init_PRE_KERNEL_20_0_
                0x0000000000008a88        0x8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_PRE_KERNEL_2??_*)))
                0x0000000000008a90                __init_POST_KERNEL_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_POST_KERNEL?_*)))
 .z_init_POST_KERNEL0_0_
                0x0000000000008a90        0x8 zephyr/libzephyr.a(log_core.c.obj)
 .z_init_POST_KERNEL0_0_
                0x0000000000008a98        0x8 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_POST_KERNEL??_*)))
 .z_init_POST_KERNEL35_0_
                0x0000000000008aa0        0x8 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .z_init_POST_KERNEL40_0_
                0x0000000000008aa8        0x8 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .z_init_POST_KERNEL50_00140_
                0x0000000000008ab0        0x8 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .z_init_POST_KERNEL90_00141_
                0x0000000000008ab8        0x8 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
                0x0000000000008ac0                __init_APPLICATION_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_APPLICATION?_*)))
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_APPLICATION??_*)))
                0x0000000000008ac0                __init_SMP_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_SMP?_*)))
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_SMP??_*)))
                0x0000000000008ac0                __init_end = .
                0x0000000000008ac0                __deferred_init_list_start = .
 *(SORT_BY_ALIGNMENT(.z_deferred_init*))
                0x0000000000008ac0                __deferred_init_list_end = .

device_area     0x0000000000008ac0       0x78
                0x0000000000008ac0                _device_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._device.static.*_?_*)))
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._device.static.*_??_*)))
 ._device.static.1_30_
                0x0000000000008ac0       0x14 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
                0x0000000000008ac0                __device_dts_ord_81
 ._device.static.1_40_
                0x0000000000008ad4       0x28 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
                0x0000000000008ad4                __device_dts_ord_33
                0x0000000000008ae8                __device_dts_ord_12
 ._device.static.1_50_
                0x0000000000008afc       0x14 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
                0x0000000000008afc                __device_dts_ord_117
 ._device.static.3_50_
                0x0000000000008b10       0x14 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
                0x0000000000008b10                __device_dts_ord_140
 ._device.static.3_90_
                0x0000000000008b24       0x14 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
                0x0000000000008b24                __device_dts_ord_141
                0x0000000000008b38                _device_list_end = .

sw_isr_table    0x0000000000008b38      0x228
                0x0000000000008b38                . = ALIGN (0x4)
 *(SORT_BY_ALIGNMENT(.gnu.linkonce.sw_isr_table*))
 .gnu.linkonce.sw_isr_table
                0x0000000000008b38      0x228 zephyr/arch/common/libisr_tables.a(isr_tables.c.obj)
                0x0000000000008b38                _sw_isr_table

initlevel_error
                0x0000000000008a38        0x0
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.z_init_[_A-Z0-9]*)))
                0x0000000000000001                ASSERT ((SIZEOF (initlevel_error) == 0x0), Undefined initialization levels used.)

app_shmem_regions
                0x0000000000008d60        0x0
                0x0000000000008d60                __app_shmem_regions_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.app_regions.*)))
                0x0000000000008d60                __app_shmem_regions_end = .

k_p4wq_initparam_area
                0x0000000000008d60        0x0
                0x0000000000008d60                _k_p4wq_initparam_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_p4wq_initparam.static.*)))
                0x0000000000008d60                _k_p4wq_initparam_list_end = .

_static_thread_data_area
                0x0000000000008d60        0x0
                0x0000000000008d60                __static_thread_data_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.__static_thread_data.static.*)))
                0x0000000000008d60                __static_thread_data_list_end = .

device_deps     0x0000000000008d60        0x0
                0x0000000000008d60                __device_deps_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.__device_deps_pass2*)))
                0x0000000000008d60                __device_deps_end = .

gpio_driver_api_area
                0x0000000000008d60       0x24
                0x0000000000008d60                _gpio_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._gpio_driver_api.static.*)))
 ._gpio_driver_api.static.gpio_nrfx_drv_api_funcs_
                0x0000000000008d60       0x24 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
                0x0000000000008d84                _gpio_driver_api_list_end = .

i2s_driver_api_area
                0x0000000000008d84       0x14
                0x0000000000008d84                _i2s_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._i2s_driver_api.static.*)))
 ._i2s_driver_api.static.i2s_nrf_drv_api_
                0x0000000000008d84       0x14 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
                0x0000000000008d98                _i2s_driver_api_list_end = .

shared_irq_driver_api_area
                0x0000000000008d98        0x0
                0x0000000000008d98                _shared_irq_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._shared_irq_driver_api.static.*)))
                0x0000000000008d98                _shared_irq_driver_api_list_end = .

crypto_driver_api_area
                0x0000000000008d98        0x0
                0x0000000000008d98                _crypto_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._crypto_driver_api.static.*)))
                0x0000000000008d98                _crypto_driver_api_list_end = .

adc_driver_api_area
                0x0000000000008d98        0x0
                0x0000000000008d98                _adc_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._adc_driver_api.static.*)))
                0x0000000000008d98                _adc_driver_api_list_end = .

auxdisplay_driver_api_area
                0x0000000000008d98        0x0
                0x0000000000008d98                _auxdisplay_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._auxdisplay_driver_api.static.*)))
                0x0000000000008d98                _auxdisplay_driver_api_list_end = .

bbram_driver_api_area
                0x0000000000008d98        0x0
                0x0000000000008d98                _bbram_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._bbram_driver_api.static.*)))
                0x0000000000008d98                _bbram_driver_api_list_end = .

bt_hci_driver_api_area
                0x0000000000008d98        0x0
                0x0000000000008d98                _bt_hci_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._bt_hci_driver_api.static.*)))
                0x0000000000008d98                _bt_hci_driver_api_list_end = .

can_driver_api_area
                0x0000000000008d98        0x0
                0x0000000000008d98                _can_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._can_driver_api.static.*)))
                0x0000000000008d98                _can_driver_api_list_end = .

cellular_driver_api_area
                0x0000000000008d98        0x0
                0x0000000000008d98                _cellular_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._cellular_driver_api.static.*)))
                0x0000000000008d98                _cellular_driver_api_list_end = .

charger_driver_api_area
                0x0000000000008d98        0x0
                0x0000000000008d98                _charger_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._charger_driver_api.static.*)))
                0x0000000000008d98                _charger_driver_api_list_end = .

clock_control_driver_api_area
                0x0000000000008d98       0x1c
                0x0000000000008d98                _clock_control_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._clock_control_driver_api.static.*)))
 ._clock_control_driver_api.static.clock_control_api_
                0x0000000000008d98       0x1c zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
                0x0000000000008db4                _clock_control_driver_api_list_end = .

comparator_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _comparator_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._comparator_driver_api.static.*)))
                0x0000000000008db4                _comparator_driver_api_list_end = .

coredump_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _coredump_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._coredump_driver_api.static.*)))
                0x0000000000008db4                _coredump_driver_api_list_end = .

counter_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _counter_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._counter_driver_api.static.*)))
                0x0000000000008db4                _counter_driver_api_list_end = .

dac_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _dac_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._dac_driver_api.static.*)))
                0x0000000000008db4                _dac_driver_api_list_end = .

dai_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _dai_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._dai_driver_api.static.*)))
                0x0000000000008db4                _dai_driver_api_list_end = .

display_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _display_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._display_driver_api.static.*)))
                0x0000000000008db4                _display_driver_api_list_end = .

dma_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _dma_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._dma_driver_api.static.*)))
                0x0000000000008db4                _dma_driver_api_list_end = .

edac_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _edac_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._edac_driver_api.static.*)))
                0x0000000000008db4                _edac_driver_api_list_end = .

eeprom_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _eeprom_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._eeprom_driver_api.static.*)))
                0x0000000000008db4                _eeprom_driver_api_list_end = .

emul_bbram_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _emul_bbram_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._emul_bbram_driver_api.static.*)))
                0x0000000000008db4                _emul_bbram_driver_api_list_end = .

fuel_gauge_emul_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _fuel_gauge_emul_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._fuel_gauge_emul_driver_api.static.*)))
                0x0000000000008db4                _fuel_gauge_emul_driver_api_list_end = .

emul_sensor_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _emul_sensor_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._emul_sensor_driver_api.static.*)))
                0x0000000000008db4                _emul_sensor_driver_api_list_end = .

entropy_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _entropy_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._entropy_driver_api.static.*)))
                0x0000000000008db4                _entropy_driver_api_list_end = .

espi_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _espi_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._espi_driver_api.static.*)))
                0x0000000000008db4                _espi_driver_api_list_end = .

espi_saf_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _espi_saf_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._espi_saf_driver_api.static.*)))
                0x0000000000008db4                _espi_saf_driver_api_list_end = .

flash_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _flash_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._flash_driver_api.static.*)))
                0x0000000000008db4                _flash_driver_api_list_end = .

fpga_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _fpga_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._fpga_driver_api.static.*)))
                0x0000000000008db4                _fpga_driver_api_list_end = .

fuel_gauge_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _fuel_gauge_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._fuel_gauge_driver_api.static.*)))
                0x0000000000008db4                _fuel_gauge_driver_api_list_end = .

gnss_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _gnss_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._gnss_driver_api.static.*)))
                0x0000000000008db4                _gnss_driver_api_list_end = .

haptics_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _haptics_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._haptics_driver_api.static.*)))
                0x0000000000008db4                _haptics_driver_api_list_end = .

hwspinlock_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _hwspinlock_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._hwspinlock_driver_api.static.*)))
                0x0000000000008db4                _hwspinlock_driver_api_list_end = .

i2c_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _i2c_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._i2c_driver_api.static.*)))
                0x0000000000008db4                _i2c_driver_api_list_end = .

i2c_target_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _i2c_target_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._i2c_target_driver_api.static.*)))
                0x0000000000008db4                _i2c_target_driver_api_list_end = .

i3c_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _i3c_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._i3c_driver_api.static.*)))
                0x0000000000008db4                _i3c_driver_api_list_end = .

ipm_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _ipm_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._ipm_driver_api.static.*)))
                0x0000000000008db4                _ipm_driver_api_list_end = .

kscan_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _kscan_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._kscan_driver_api.static.*)))
                0x0000000000008db4                _kscan_driver_api_list_end = .

led_driver_api_area
                0x0000000000008db4        0x0
                0x0000000000008db4                _led_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._led_driver_api.static.*)))
                0x0000000000008db4                _led_driver_api_list_end = .

led_strip_driver_api_area
                0x0000000000008db4        0xc
                0x0000000000008db4                _led_strip_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._led_strip_driver_api.static.*)))
 ._led_strip_driver_api.static.ws2812_i2s_api_
                0x0000000000008db4        0xc zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
                0x0000000000008dc0                _led_strip_driver_api_list_end = .

lora_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _lora_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._lora_driver_api.static.*)))
                0x0000000000008dc0                _lora_driver_api_list_end = .

mbox_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _mbox_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._mbox_driver_api.static.*)))
                0x0000000000008dc0                _mbox_driver_api_list_end = .

mdio_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _mdio_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._mdio_driver_api.static.*)))
                0x0000000000008dc0                _mdio_driver_api_list_end = .

mipi_dbi_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _mipi_dbi_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._mipi_dbi_driver_api.static.*)))
                0x0000000000008dc0                _mipi_dbi_driver_api_list_end = .

mipi_dsi_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _mipi_dsi_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._mipi_dsi_driver_api.static.*)))
                0x0000000000008dc0                _mipi_dsi_driver_api_list_end = .

mspi_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _mspi_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._mspi_driver_api.static.*)))
                0x0000000000008dc0                _mspi_driver_api_list_end = .

peci_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _peci_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._peci_driver_api.static.*)))
                0x0000000000008dc0                _peci_driver_api_list_end = .

ps2_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _ps2_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._ps2_driver_api.static.*)))
                0x0000000000008dc0                _ps2_driver_api_list_end = .

ptp_clock_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _ptp_clock_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._ptp_clock_driver_api.static.*)))
                0x0000000000008dc0                _ptp_clock_driver_api_list_end = .

pwm_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _pwm_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._pwm_driver_api.static.*)))
                0x0000000000008dc0                _pwm_driver_api_list_end = .

regulator_parent_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _regulator_parent_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._regulator_parent_driver_api.static.*)))
                0x0000000000008dc0                _regulator_parent_driver_api_list_end = .

regulator_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _regulator_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._regulator_driver_api.static.*)))
                0x0000000000008dc0                _regulator_driver_api_list_end = .

reset_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _reset_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._reset_driver_api.static.*)))
                0x0000000000008dc0                _reset_driver_api_list_end = .

retained_mem_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _retained_mem_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._retained_mem_driver_api.static.*)))
                0x0000000000008dc0                _retained_mem_driver_api_list_end = .

rtc_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _rtc_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._rtc_driver_api.static.*)))
                0x0000000000008dc0                _rtc_driver_api_list_end = .

sdhc_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _sdhc_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._sdhc_driver_api.static.*)))
                0x0000000000008dc0                _sdhc_driver_api_list_end = .

sensor_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _sensor_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._sensor_driver_api.static.*)))
                0x0000000000008dc0                _sensor_driver_api_list_end = .

smbus_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _smbus_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._smbus_driver_api.static.*)))
                0x0000000000008dc0                _smbus_driver_api_list_end = .

spi_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _spi_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._spi_driver_api.static.*)))
                0x0000000000008dc0                _spi_driver_api_list_end = .

stepper_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _stepper_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._stepper_driver_api.static.*)))
                0x0000000000008dc0                _stepper_driver_api_list_end = .

syscon_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _syscon_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._syscon_driver_api.static.*)))
                0x0000000000008dc0                _syscon_driver_api_list_end = .

tee_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _tee_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._tee_driver_api.static.*)))
                0x0000000000008dc0                _tee_driver_api_list_end = .

video_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _video_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._video_driver_api.static.*)))
                0x0000000000008dc0                _video_driver_api_list_end = .

w1_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _w1_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._w1_driver_api.static.*)))
                0x0000000000008dc0                _w1_driver_api_list_end = .

wdt_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _wdt_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._wdt_driver_api.static.*)))
                0x0000000000008dc0                _wdt_driver_api_list_end = .

can_transceiver_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _can_transceiver_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._can_transceiver_driver_api.static.*)))
                0x0000000000008dc0                _can_transceiver_driver_api_list_end = .

nrf_clock_control_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _nrf_clock_control_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._nrf_clock_control_driver_api.static.*)))
                0x0000000000008dc0                _nrf_clock_control_driver_api_list_end = .

i3c_target_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _i3c_target_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._i3c_target_driver_api.static.*)))
                0x0000000000008dc0                _i3c_target_driver_api_list_end = .

its_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _its_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._its_driver_api.static.*)))
                0x0000000000008dc0                _its_driver_api_list_end = .

vtd_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _vtd_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._vtd_driver_api.static.*)))
                0x0000000000008dc0                _vtd_driver_api_list_end = .

tgpio_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _tgpio_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._tgpio_driver_api.static.*)))
                0x0000000000008dc0                _tgpio_driver_api_list_end = .

pcie_ctrl_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _pcie_ctrl_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._pcie_ctrl_driver_api.static.*)))
                0x0000000000008dc0                _pcie_ctrl_driver_api_list_end = .

pcie_ep_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _pcie_ep_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._pcie_ep_driver_api.static.*)))
                0x0000000000008dc0                _pcie_ep_driver_api_list_end = .

svc_driver_api_area
                0x0000000000008dc0        0x0
                0x0000000000008dc0                _svc_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._svc_driver_api.static.*)))
                0x0000000000008dc0                _svc_driver_api_list_end = .

uart_driver_api_area
                0x0000000000008dc0        0xc
                0x0000000000008dc0                _uart_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._uart_driver_api.static.*)))
 ._uart_driver_api.static.uart_nrfx_uarte_driver_api_
                0x0000000000008dc0        0xc zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
                0x0000000000008dcc                _uart_driver_api_list_end = .

bc12_emul_driver_api_area
                0x0000000000008dcc        0x0
                0x0000000000008dcc                _bc12_emul_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._bc12_emul_driver_api.static.*)))
                0x0000000000008dcc                _bc12_emul_driver_api_list_end = .

bc12_driver_api_area
                0x0000000000008dcc        0x0
                0x0000000000008dcc                _bc12_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._bc12_driver_api.static.*)))
                0x0000000000008dcc                _bc12_driver_api_list_end = .

usbc_ppc_driver_api_area
                0x0000000000008dcc        0x0
                0x0000000000008dcc                _usbc_ppc_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._usbc_ppc_driver_api.static.*)))
                0x0000000000008dcc                _usbc_ppc_driver_api_list_end = .

tcpc_driver_api_area
                0x0000000000008dcc        0x0
                0x0000000000008dcc                _tcpc_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._tcpc_driver_api.static.*)))
                0x0000000000008dcc                _tcpc_driver_api_list_end = .

usbc_vbus_driver_api_area
                0x0000000000008dcc        0x0
                0x0000000000008dcc                _usbc_vbus_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._usbc_vbus_driver_api.static.*)))
                0x0000000000008dcc                _usbc_vbus_driver_api_list_end = .

ivshmem_driver_api_area
                0x0000000000008dcc        0x0
                0x0000000000008dcc                _ivshmem_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._ivshmem_driver_api.static.*)))
                0x0000000000008dcc                _ivshmem_driver_api_list_end = .

ethphy_driver_api_area
                0x0000000000008dcc        0x0
                0x0000000000008dcc                _ethphy_driver_api_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._ethphy_driver_api.static.*)))
                0x0000000000008dcc                _ethphy_driver_api_list_end = .

ztest           0x0000000000008dcc        0x0
                0x0000000000008dcc                _ztest_expected_result_entry_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._ztest_expected_result_entry.static.*)))
                0x0000000000008dcc                _ztest_expected_result_entry_list_end = .
                0x0000000000008dcc                _ztest_suite_node_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._ztest_suite_node.static.*)))
                0x0000000000008dcc                _ztest_suite_node_list_end = .
                0x0000000000008dcc                _ztest_unit_test_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._ztest_unit_test.static.*)))
                0x0000000000008dcc                _ztest_unit_test_list_end = .
                0x0000000000008dcc                _ztest_test_rule_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._ztest_test_rule.static.*)))
                0x0000000000008dcc                _ztest_test_rule_list_end = .

init_array      0x0000000000008dcc        0x0
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.ctors*)))
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.init_array*)))
                0x0000000000000001                ASSERT ((SIZEOF (init_array) == 0x0), GNU-style constructors required but STATIC_INIT_GNU not enabled)

bt_l2cap_fixed_chan_area
                0x0000000000008dcc        0x0
                0x0000000000008dcc                _bt_l2cap_fixed_chan_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._bt_l2cap_fixed_chan.static.*)))
                0x0000000000008dcc                _bt_l2cap_fixed_chan_list_end = .

bt_gatt_service_static_area
                0x0000000000008dcc        0x0
                0x0000000000008dcc                _bt_gatt_service_static_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._bt_gatt_service_static.static.*)))
                0x0000000000008dcc                _bt_gatt_service_static_list_end = .

log_strings_area
                0x0000000000008dcc        0x0
                0x0000000000008dcc                _log_strings_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_strings.static.*)))
                0x0000000000008dcc                _log_strings_list_end = .

log_stmesp_ptr_area
                0x0000000000008dcc        0x0
                0x0000000000008dcc                _log_stmesp_ptr_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_stmesp_ptr.static.*)))
                0x0000000000008dcc                _log_stmesp_ptr_list_end = .

log_stmesp_str_area
                0x0000000000008dcc        0x0
                0x0000000000008dcc                _log_stmesp_str_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_stmesp_str.static.*)))
                0x0000000000008dcc                _log_stmesp_str_list_end = .

log_const_area  0x0000000000008dcc       0x58
                0x0000000000008dcc                _log_const_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_const.static.*)))
 ._log_const.static.log_const_cbprintf_package_
                0x0000000000008dcc        0x8 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
                0x0000000000008dcc                log_const_cbprintf_package
 ._log_const.static.log_const_clock_control_
                0x0000000000008dd4        0x8 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
                0x0000000000008dd4                log_const_clock_control
 ._log_const.static.log_const_i2s_nrfx_
                0x0000000000008ddc        0x8 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
                0x0000000000008ddc                log_const_i2s_nrfx
 ._log_const.static.log_const_log_
                0x0000000000008de4        0x8 zephyr/libzephyr.a(log_core.c.obj)
                0x0000000000008de4                log_const_log
 ._log_const.static.log_const_log_mgmt_
                0x0000000000008dec        0x8 zephyr/libzephyr.a(log_mgmt.c.obj)
                0x0000000000008dec                log_const_log_mgmt
 ._log_const.static.log_const_log_uart_
                0x0000000000008df4        0x8 zephyr/libzephyr.a(log_backend_uart.c.obj)
                0x0000000000008df4                log_const_log_uart
 ._log_const.static.log_const_mpu_
                0x0000000000008dfc        0x8 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
                0x0000000000008dfc                log_const_mpu
 ._log_const.static.log_const_os_
                0x0000000000008e04        0x8 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000000008e04                log_const_os
 ._log_const.static.log_const_soc_
                0x0000000000008e0c        0x8 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
                0x0000000000008e0c                log_const_soc
 ._log_const.static.log_const_uart_nrfx_uarte_
                0x0000000000008e14        0x8 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
                0x0000000000008e14                log_const_uart_nrfx_uarte
 ._log_const.static.log_const_ws2812_i2s_
                0x0000000000008e1c        0x8 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
                0x0000000000008e1c                log_const_ws2812_i2s
                0x0000000000008e24                _log_const_list_end = .

log_backend_area
                0x0000000000008e24       0x10
                0x0000000000008e24                _log_backend_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_backend.static.*)))
 ._log_backend.static.log_backend_uart_
                0x0000000000008e24       0x10 zephyr/libzephyr.a(log_backend_uart.c.obj)
                0x0000000000008e34                _log_backend_list_end = .

log_link_area   0x0000000000008e34        0x0
                0x0000000000008e34                _log_link_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_link.static.*)))
                0x0000000000008e34                _log_link_list_end = .

tracing_backend_area
                0x0000000000008e34        0x0
                0x0000000000008e34                _tracing_backend_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._tracing_backend.static.*)))
                0x0000000000008e34                _tracing_backend_list_end = .

zephyr_dbg_info
 *(SORT_BY_ALIGNMENT(.dbg_thread_info))

intc_table_area
                0x0000000000008e34        0x0
                0x0000000000008e34                _intc_table_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._intc_table.static.*)))
                0x0000000000008e34                _intc_table_list_end = .

symbol_to_keep  0x0000000000008e34        0x0
                0x0000000000008e34                __symbol_to_keep_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(.symbol_to_keep*)))
                0x0000000000008e34                __symbol_to_keep_end = .

shell_area      0x0000000000008e34        0x0
                0x0000000000008e34                _shell_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._shell.static.*)))
                0x0000000000008e34                _shell_list_end = .

shell_root_cmds_area
                0x0000000000008e34        0x0
                0x0000000000008e34                _shell_root_cmds_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._shell_root_cmds.static.*)))
                0x0000000000008e34                _shell_root_cmds_list_end = .

shell_subcmds_area
                0x0000000000008e34        0x0
                0x0000000000008e34                _shell_subcmds_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._shell_subcmds.static.*)))
                0x0000000000008e34                _shell_subcmds_list_end = .

shell_dynamic_subcmds_area
                0x0000000000008e34        0x0
                0x0000000000008e34                _shell_dynamic_subcmds_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._shell_dynamic_subcmds.static.*)))
                0x0000000000008e34                _shell_dynamic_subcmds_list_end = .

cfb_font_area   0x0000000000008e34        0x0
                0x0000000000008e34                _cfb_font_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._cfb_font.static.*)))
                0x0000000000008e34                _cfb_font_list_end = .

tdata           0x0000000000008e34        0x0
 *(SORT_BY_ALIGNMENT(.tdata) SORT_BY_ALIGNMENT(.tdata.*) SORT_BY_ALIGNMENT(.gnu.linkonce.td.*))

tbss            0x0000000000008e34        0x4
 *(SORT_BY_ALIGNMENT(.tbss) SORT_BY_ALIGNMENT(.tbss.*) SORT_BY_ALIGNMENT(.gnu.linkonce.tb.*) SORT_BY_ALIGNMENT(.tcommon))
 .tbss.z_tls_current
                0x0000000000008e34        0x4 zephyr/libzephyr.a(thread_entry.c.obj)
                0x0000000000008e34                z_tls_current
                0x0000000000008e34                PROVIDE (__tdata_start = LOADADDR (tdata))
                0x0000000000000001                PROVIDE (__tdata_align = ALIGNOF (tdata))
                0x0000000000000000                PROVIDE (__tdata_size = (((SIZEOF (tdata) + __tdata_align) - 0x1) & ~ ((__tdata_align - 0x1))))
                [!provide]                        PROVIDE (__tdata_end = (__tdata_start + __tdata_size))
                0x0000000000000004                PROVIDE (__tbss_align = ALIGNOF (tbss))
                [!provide]                        PROVIDE (__tbss_start = ADDR (tbss))
                0x0000000000000004                PROVIDE (__tbss_size = (((SIZEOF (tbss) + __tbss_align) - 0x1) & ~ ((__tbss_align - 0x1))))
                [!provide]                        PROVIDE (__tbss_end = (__tbss_start + __tbss_size))
                [!provide]                        PROVIDE (__tls_start = __tdata_start)
                [!provide]                        PROVIDE (__tls_end = __tbss_end)
                [!provide]                        PROVIDE (__tls_size = (__tbss_end - __tdata_start))

rodata          0x0000000000008e40     0x105c
 *(SORT_BY_ALIGNMENT(.rodata))
 *(SORT_BY_ALIGNMENT(.rodata.*))
 .rodata.delay_machine_code.1
                0x0000000000008e40        0x6 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 *fill*         0x0000000000008e46        0x2 
 .rodata.button3
                0x0000000000008e48        0x8 app/libapp.a(button.c.obj)
 .rodata.button2
                0x0000000000008e50        0x8 app/libapp.a(button.c.obj)
 .rodata.button1
                0x0000000000008e58        0x8 app/libapp.a(button.c.obj)
 .rodata.button0
                0x0000000000008e60        0x8 app/libapp.a(button.c.obj)
 .rodata.mpsc_config
                0x0000000000008e68       0x14 zephyr/libzephyr.a(log_core.c.obj)
 .rodata.format_table
                0x0000000000008e7c       0x10 zephyr/libzephyr.a(log_core.c.obj)
 .rodata.colors
                0x0000000000008e8c       0x14 zephyr/libzephyr.a(log_output.c.obj)
 .rodata.severity
                0x0000000000008ea0       0x14 zephyr/libzephyr.a(log_output.c.obj)
 .rodata.lbu_cb_ctx
                0x0000000000008eb4        0x8 zephyr/libzephyr.a(log_backend_uart.c.obj)
 .rodata.lbu_output
                0x0000000000008ebc       0x10 zephyr/libzephyr.a(log_backend_uart.c.obj)
 .rodata.log_backend_uart_api
                0x0000000000008ecc       0x1c zephyr/libzephyr.a(log_backend_uart.c.obj)
                0x0000000000008ecc                log_backend_uart_api
 .rodata.mem_attr_region
                0x0000000000008ee8        0x0 zephyr/libzephyr.a(mem_attr.c.obj)
 .rodata.apis   0x0000000000008ee8        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .rodata.mutexes
                0x0000000000008ef0       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .rodata.mutex_apis
                0x0000000000008f04       0x10 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .rodata.static_regions
                0x0000000000008f14        0xc zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .rodata.mpu_config
                0x0000000000008f20        0x8 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
                0x0000000000008f20                mpu_config
 .rodata.mpu_regions
                0x0000000000008f28       0x20 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .rodata.transitions.0
                0x0000000000008f48        0xc zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .rodata.transitions.0
                0x0000000000008f54        0xc zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .rodata.config
                0x0000000000008f60       0x30 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .rodata.gpio_nrfx_p1_cfg
                0x0000000000008f90       0x18 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .rodata.gpio_nrfx_p0_cfg
                0x0000000000008fa8       0x18 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .rodata.i2s_nrfx_cfg0
                0x0000000000008fc0       0x3c zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .rodata.__pinctrl_dev_config__device_dts_ord_140
                0x0000000000008ffc        0xc zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .rodata.__pinctrl_states__device_dts_ord_140
                0x0000000000009008        0x8 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .rodata.__pinctrl_state_pins_0__device_dts_ord_140
                0x0000000000009010       0x10 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .rodata.ws2812_i2s_0_cfg
                0x0000000000009020       0x28 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .rodata.uarte_0z_config
                0x0000000000009048       0x24 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .rodata.__pinctrl_dev_config__device_dts_ord_117
                0x000000000000906c        0xc zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .rodata.__pinctrl_states__device_dts_ord_117
                0x0000000000009078        0x8 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .rodata.__pinctrl_state_pins_0__device_dts_ord_117
                0x0000000000009080       0x10 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .rodata.CSWTCH.489
                0x0000000000009090       0x14 zephyr/kernel/libkernel.a(fatal.c.obj)
 .rodata.levels.0
                0x00000000000090a4       0x18 zephyr/kernel/libkernel.a(init.c.obj)
 .rodata.CSWTCH.10
                0x00000000000090bc       0x20 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .rodata.mutex_free.str1.4
                0x00000000000090dc       0x26 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 *fill*         0x0000000000009102        0x2 
 .rodata.mutex_init.str1.4
                0x0000000000009104       0x23 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 *fill*         0x0000000000009127        0x1 
 .rodata.CC_PalPowerSaveModeInit.str1.4
                0x0000000000009128       0x20 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .rodata.ratios.0
                0x0000000000009148       0x24 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .rodata.CSWTCH.689
                0x000000000000916c        0x8 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .rodata.CSWTCH.688
                0x0000000000009174        0x8 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .rodata.cbprintf_package_convert.str1.1
                0x000000000000917c       0xa3 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .rodata.str1.1
                0x000000000000921f       0x11 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .rodata.enable_logger.str1.1
                0x0000000000009230        0x8 zephyr/libzephyr.a(log_core.c.obj)
 .rodata.str1.1
                0x0000000000009238        0x4 zephyr/libzephyr.a(log_core.c.obj)
 .rodata.str1.1
                0x000000000000923c        0x9 zephyr/libzephyr.a(log_mgmt.c.obj)
 .rodata.z_impl_z_log_msg_static_create.str1.1
                0x0000000000009245       0x3f zephyr/libzephyr.a(log_msg.c.obj)
 .rodata.z_log_msg_runtime_vcreate.str1.1
                0x0000000000009284       0x38 zephyr/libzephyr.a(log_msg.c.obj)
 .rodata.newline_print.str1.1
                0x00000000000092bc        0x3 zephyr/libzephyr.a(log_output.c.obj)
                                          0x5 (size before relaxing)
 .rodata.log_output_process.str1.1
                0x00000000000092bf       0x4f zephyr/libzephyr.a(log_output.c.obj)
                                         0x51 (size before relaxing)
 .rodata.log_output_dropped_process.str1.1
                0x000000000000930e       0x4f zephyr/libzephyr.a(log_output.c.obj)
                                          0x3 (size before relaxing)
 .rodata.postfix.0
                0x000000000000930e       0x1c zephyr/libzephyr.a(log_output.c.obj)
 .rodata.prefix.1
                0x000000000000932a        0xc zephyr/libzephyr.a(log_output.c.obj)
 .rodata.str1.1
                0x0000000000009336       0x20 zephyr/libzephyr.a(log_output.c.obj)
 .rodata.str1.1
                0x0000000000009356       0x1a zephyr/libzephyr.a(log_backend_uart.c.obj)
 .rodata.boot_banner.str1.1
                0x0000000000009370       0x63 zephyr/libzephyr.a(banner.c.obj)
 .rodata.mutex_free_platform.str1.1
                0x00000000000093d3       0x26 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .rodata.mutex_init_platform.str1.1
                0x00000000000093f9       0x2d zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .rodata.z_arm_fatal_error.str1.1
                0x0000000000009426       0xae zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .rodata.mem_manage_fault.constprop.0.str1.1
                0x00000000000094d4       0xd8 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .rodata.usage_fault.constprop.0.str1.1
                0x00000000000095ac       0xf7 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .rodata.bus_fault.constprop.0.str1.1
                0x00000000000096a3       0x88 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .rodata.z_arm_fault.str1.1
                0x000000000000972b      0x111 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .rodata.str1.1
                0x000000000000983c        0x4 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .rodata.region_allocate_and_init.str1.1
                0x0000000000009840       0x26 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .rodata.mpu_configure_regions_and_partition.constprop.0.str1.1
                0x0000000000009866       0x46 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .rodata.str1.1
                0x00000000000098ac        0xf zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .rodata.str1.1
                0x00000000000098bb        0x4 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .rodata.CSWTCH.4
                0x00000000000098bf        0x4 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .rodata.str1.1
                0x00000000000098c3       0x3a zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .rodata.str1.1
                0x00000000000098fd       0x18 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .rodata.get_next_rx_buffer.str1.1
                0x0000000000009915       0x26 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .rodata.start_transfer.str1.1
                0x000000000000993b       0x3c zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .rodata.i2s_nrfx_write.str1.1
                0x0000000000009977       0x83 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .rodata.data_handler0.str1.1
                0x00000000000099fa       0x36 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .rodata.i2s_nrfx_configure.str1.1
                0x0000000000009a30      0x137 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .rodata.i2s_nrfx_trigger.str1.1
                0x0000000000009b67       0xac zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .rodata.str1.1
                0x0000000000009c13       0x13 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .rodata.ws2812_i2s_init.str1.1
                0x0000000000009c26       0x95 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .rodata.ws2812_strip_update_rgb.str1.1
                0x0000000000009cbb       0x6c zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .rodata.__func__.0
                0x0000000000009d27       0x10 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .rodata.str1.1
                0x0000000000009d37       0x14 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .rodata.ws2812_i2s_0_color_mapping
                0x0000000000009d4b        0x3 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .rodata.drive_modes
                0x0000000000009d4e        0x9 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .rodata.uarte_0_init.str1.1
                0x0000000000009d57       0x1f zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .rodata.str1.1
                0x0000000000009d76       0x1a zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .rodata.CSWTCH.17
                0x0000000000009d90        0x4 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .rodata.CSWTCH.5
                0x0000000000009d94        0x4 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .rodata.CSWTCH.3
                0x0000000000009d98        0x4 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .rodata.CSWTCH.690
                0x0000000000009d9c        0x4 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .rodata.port_offset.1
                0x0000000000009da0       0x10 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .rodata.ports  0x0000000000009db0        0x2 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .rodata.k_sys_fatal_error_handler.str1.1
                0x0000000000009db2        0xf zephyr/kernel/libkernel.a(fatal.c.obj)
 .rodata.z_fatal_error.str1.1
                0x0000000000009dc1       0x77 zephyr/kernel/libkernel.a(fatal.c.obj)
 .rodata.str1.1
                0x0000000000009e38       0x4a zephyr/kernel/libkernel.a(fatal.c.obj)
 .rodata.z_cstart.str1.1
                0x0000000000009e82        0x5 zephyr/kernel/libkernel.a(init.c.obj)
 .rodata.str1.1
                0x0000000000009e87        0x3 zephyr/kernel/libkernel.a(init.c.obj)
 .rodata.__l_vfprintf.str1.1
                0x0000000000009e8a        0xf c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
 *(SORT_BY_ALIGNMENT(.gnu.linkonce.r.*))
                0x0000000000009e9c                . = ALIGN (0x4)
 *fill*         0x0000000000009e99        0x3 

/DISCARD/
 *(SORT_BY_ALIGNMENT(.eh_frame))
                0x0000000000009e9c                __rodata_region_end = .
                0x0000000000009ea0                . = ALIGN (_region_min_align)
                0x0000000000009ea0                __rom_region_end = ((__rom_region_start + .) - ADDR (rom_start))

/DISCARD/
 *(SORT_BY_ALIGNMENT(.got.plt))
 *(SORT_BY_ALIGNMENT(.igot.plt))
 *(SORT_BY_ALIGNMENT(.got))
 *(SORT_BY_ALIGNMENT(.igot))
                0x0000000020000000                . = 0x20000000
                0x0000000020000000                . = ALIGN (_region_min_align)
                0x0000000020000000                _image_ram_start = .

.ramfunc        0x0000000020000000        0x0 load address 0x0000000000009e9c
                0x0000000020000000                __ramfunc_region_start = .
                0x0000000020000000                . = ALIGN (_region_min_align)
                0x0000000020000000                __ramfunc_start = .
 *(SORT_BY_ALIGNMENT(.ramfunc))
 *(SORT_BY_ALIGNMENT(.ramfunc.*))
                0x0000000020000000                . = ALIGN (_region_min_align)
                0x0000000020000000                __ramfunc_end = .
                0x0000000000000000                __ramfunc_size = (__ramfunc_end - __ramfunc_start)
                0x0000000000009e9c                __ramfunc_load_start = LOADADDR (.ramfunc)

datas           0x0000000020000000      0x234 load address 0x0000000000009e9c
                0x0000000020000000                __data_region_start = .
                0x0000000020000000                __data_start = .
 *(SORT_BY_ALIGNMENT(.data))
 *(SORT_BY_ALIGNMENT(.data.*))
 .data._char_out
                0x0000000020000000        0x4 zephyr/libzephyr.a(printk.c.obj)
 .data.timestamp_func
                0x0000000020000004        0x4 zephyr/libzephyr.a(log_core.c.obj)
 .data.backend_cb_log_backend_uart
                0x0000000020000008        0x8 zephyr/libzephyr.a(log_backend_uart.c.obj)
 .data.power_mutex
                0x0000000020000010        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.rng_mutex
                0x0000000020000018        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.asym_mutex
                0x0000000020000020        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.sym_mutex
                0x0000000020000028        0x8 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .data.__stdout
                0x0000000020000030       0x10 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .data.i2s_nrfx_data0
                0x0000000020000040       0xec zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .data.SystemCoreClock
                0x000000002000012c        0x4 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
                0x000000002000012c                SystemCoreClock
 .data.dppi     0x0000000020000130        0x8 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .data.m_cb     0x0000000020000138       0x10 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .data.m_cb     0x0000000020000148       0x84 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .data.timeout_list
                0x00000000200001cc        0x8 zephyr/kernel/libkernel.a(timeout.c.obj)
 .data.platform_abort_apis
                0x00000000200001d4        0x8 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
                0x00000000200001d4                platform_abort_apis
 .data.platform_mutexes
                0x00000000200001dc       0x14 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
                0x00000000200001dc                platform_mutexes
 .data.platform_mutex_apis
                0x00000000200001f0       0x10 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
                0x00000000200001f0                platform_mutex_apis
 .data.power_mutex
                0x0000000020000200        0x8 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .data.rng_mutex
                0x0000000020000208        0x8 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .data.asym_mutex
                0x0000000020000210        0x8 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .data.sym_mutex
                0x0000000020000218        0x8 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .data.pCCRndCryptoMutex
                0x0000000020000220        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x0000000020000220                pCCRndCryptoMutex
 .data.CCPowerMutex
                0x0000000020000224        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x0000000020000224                CCPowerMutex
 .data.CCRndCryptoMutex
                0x0000000020000228        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x0000000020000228                CCRndCryptoMutex
 .data.CCAsymCryptoMutex
                0x000000002000022c        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x000000002000022c                CCAsymCryptoMutex
 .data.CCSymCryptoMutex
                0x0000000020000230        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
                0x0000000020000230                CCSymCryptoMutex
 *(SORT_BY_ALIGNMENT(.kernel.*))
                0x0000000020000234                __data_end = .
                0x0000000000000234                __data_size = (__data_end - __data_start)
                0x0000000000009e9c                __data_load_start = LOADADDR (datas)
                0x0000000000009e9c                __data_region_load_start = LOADADDR (datas)

device_states   0x0000000020000234        0xc load address 0x000000000000a0d0
                0x0000000020000234                __device_states_start = .
 *(SORT_BY_ALIGNMENT(.z_devstate))
 .z_devstate    0x0000000020000234        0x2 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .z_devstate    0x0000000020000236        0x4 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .z_devstate    0x000000002000023a        0x2 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .z_devstate    0x000000002000023c        0x2 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .z_devstate    0x000000002000023e        0x2 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 *(SORT_BY_ALIGNMENT(.z_devstate.*))
                0x0000000020000240                __device_states_end = .

log_mpsc_pbuf_area
                0x0000000020000240       0x38 load address 0x000000000000a0dc
                0x0000000020000240                _log_mpsc_pbuf_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_mpsc_pbuf.static.*)))
 ._log_mpsc_pbuf.static.log_buffer_
                0x0000000020000240       0x38 zephyr/libzephyr.a(log_core.c.obj)
                0x0000000020000278                _log_mpsc_pbuf_list_end = .

log_msg_ptr_area
                0x0000000020000278        0x4 load address 0x000000000000a114
                0x0000000020000278                _log_msg_ptr_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_msg_ptr.static.*)))
 ._log_msg_ptr.static.log_msg_ptr_
                0x0000000020000278        0x4 zephyr/libzephyr.a(log_core.c.obj)
                0x000000002000027c                _log_msg_ptr_list_end = .

log_dynamic_area
                0x000000002000027c        0x0 load address 0x000000000000a118
                0x000000002000027c                _log_dynamic_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._log_dynamic.static.*)))
                0x000000002000027c                _log_dynamic_list_end = .

k_timer_area    0x000000002000027c        0x0 load address 0x000000000000a118
                0x000000002000027c                _k_timer_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_timer.static.*)))
                0x000000002000027c                _k_timer_list_end = .

k_mem_slab_area
                0x000000002000027c       0x1c load address 0x000000000000a118
                0x000000002000027c                _k_mem_slab_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_mem_slab.static.*)))
 ._k_mem_slab.static.ws2812_i2s_0_slab_
                0x000000002000027c       0x1c zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
                0x0000000020000298                _k_mem_slab_list_end = .

k_heap_area     0x0000000020000298        0x0 load address 0x000000000000a134
                0x0000000020000298                _k_heap_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_heap.static.*)))
                0x0000000020000298                _k_heap_list_end = .

k_mutex_area    0x0000000020000298       0x50 load address 0x000000000000a134
                0x0000000020000298                _k_mutex_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_mutex.static.*)))
 ._k_mutex.static.asym_mutex_int_
                0x0000000020000298       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x0000000020000298                asym_mutex_int
 ._k_mutex.static.power_mutex_int_
                0x00000000200002ac       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x00000000200002ac                power_mutex_int
 ._k_mutex.static.rng_mutex_int_
                0x00000000200002c0       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x00000000200002c0                rng_mutex_int
 ._k_mutex.static.sym_mutex_int_
                0x00000000200002d4       0x14 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x00000000200002d4                sym_mutex_int
                0x00000000200002e8                _k_mutex_list_end = .

k_stack_area    0x00000000200002e8        0x0 load address 0x000000000000a184
                0x00000000200002e8                _k_stack_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_stack.static.*)))
                0x00000000200002e8                _k_stack_list_end = .

k_msgq_area     0x00000000200002e8        0x0 load address 0x000000000000a184
                0x00000000200002e8                _k_msgq_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_msgq.static.*)))
                0x00000000200002e8                _k_msgq_list_end = .

k_mbox_area     0x00000000200002e8        0x0 load address 0x000000000000a184
                0x00000000200002e8                _k_mbox_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_mbox.static.*)))
                0x00000000200002e8                _k_mbox_list_end = .

k_pipe_area     0x00000000200002e8        0x0 load address 0x000000000000a184
                0x00000000200002e8                _k_pipe_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_pipe.static.*)))
                0x00000000200002e8                _k_pipe_list_end = .

k_sem_area      0x00000000200002e8       0x10 load address 0x000000000000a184
                0x00000000200002e8                _k_sem_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_sem.static.*)))
 ._k_sem.static.log_process_thread_sem_
                0x00000000200002e8       0x10 zephyr/libzephyr.a(log_core.c.obj)
                0x00000000200002e8                log_process_thread_sem
                0x00000000200002f8                _k_sem_list_end = .

k_event_area    0x00000000200002f8        0x0 load address 0x000000000000a194
                0x00000000200002f8                _k_event_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_event.static.*)))
                0x00000000200002f8                _k_event_list_end = .

k_queue_area    0x00000000200002f8        0x0 load address 0x000000000000a194
                0x00000000200002f8                _k_queue_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_queue.static.*)))
                0x00000000200002f8                _k_queue_list_end = .

k_fifo_area     0x00000000200002f8        0x0 load address 0x000000000000a194
                0x00000000200002f8                _k_fifo_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_fifo.static.*)))
                0x00000000200002f8                _k_fifo_list_end = .

k_lifo_area     0x00000000200002f8        0x0 load address 0x000000000000a194
                0x00000000200002f8                _k_lifo_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_lifo.static.*)))
                0x00000000200002f8                _k_lifo_list_end = .

k_condvar_area  0x00000000200002f8        0x0 load address 0x000000000000a194
                0x00000000200002f8                _k_condvar_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._k_condvar.static.*)))
                0x00000000200002f8                _k_condvar_list_end = .

sys_mem_blocks_ptr_area
                0x00000000200002f8        0x0 load address 0x000000000000a194
                0x00000000200002f8                _sys_mem_blocks_ptr_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._sys_mem_blocks_ptr.static.*)))
                0x00000000200002f8                _sys_mem_blocks_ptr_list_end = .

net_buf_pool_area
                0x00000000200002f8        0x0 load address 0x000000000000a194
                0x00000000200002f8                _net_buf_pool_list_start = .
 *(SORT_BY_NAME(SORT_BY_ALIGNMENT(._net_buf_pool.static.*)))
                0x00000000200002f8                _net_buf_pool_list_end = .
                0x00000000200002f8                __data_region_end = .
                0x0000000000004518                PROVIDE (soc_reset_hook = SystemInit)

.intList        0x00000000ffff8000       0x58
 *(SORT_BY_ALIGNMENT(.irq_info*))
 .irq_info      0x00000000ffff8000        0x8 zephyr/arch/common/libisr_tables.a(isr_tables.c.obj)
                0x00000000ffff8000                _iheader
 *(SORT_BY_ALIGNMENT(.intList*))
 .intList       0x00000000ffff8008       0x10 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .intList       0x00000000ffff8018       0x10 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .intList       0x00000000ffff8028       0x10 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .intList       0x00000000ffff8038       0x10 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .intList       0x00000000ffff8048       0x10 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)

.stab
 *(SORT_BY_ALIGNMENT(.stab))

.stabstr
 *(SORT_BY_ALIGNMENT(.stabstr))

.stab.excl
 *(SORT_BY_ALIGNMENT(.stab.excl))

.stab.exclstr
 *(SORT_BY_ALIGNMENT(.stab.exclstr))

.stab.index
 *(SORT_BY_ALIGNMENT(.stab.index))

.stab.indexstr
 *(SORT_BY_ALIGNMENT(.stab.indexstr))

.gnu.build.attributes
 *(SORT_BY_ALIGNMENT(.gnu.build.attributes) SORT_BY_ALIGNMENT(.gnu.build.attributes.*))

.comment        0x0000000000000000       0x40
 *(SORT_BY_ALIGNMENT(.comment))
 .comment       0x0000000000000000       0x20 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
                                         0x21 (size before relaxing)
 .comment       0x0000000000000020       0x21 app/libapp.a(main.c.obj)
 .comment       0x0000000000000020       0x21 app/libapp.a(button.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(heap.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(printk.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(thread_entry.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(onoff.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(notify.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(last_section_id.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(configs.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(log_core.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(log_mgmt.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(log_msg.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(log_output.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(log_backend_uart.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(mem_attr.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(banner.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(cbprintf.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .comment       0x0000000000000020       0x21 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .comment       0x0000000000000020       0x21 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(device.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(fatal.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(init.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(init_static.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(idle.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(msg_q.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(mutex.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(sem.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(thread.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(sched.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(xip.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(timeout.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(timer.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/kernel/libkernel.a(kheap.c.obj)
 .comment       0x0000000000000020       0x21 zephyr/arch/common/libisr_tables.a(isr_tables.c.obj)
 .comment       0x0000000000000020       0x20 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
                                         0x21 (size before relaxing)
 .comment       0x0000000000000040       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .comment       0x0000000000000040       0x21 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)

.debug
 *(SORT_BY_ALIGNMENT(.debug))

.line
 *(SORT_BY_ALIGNMENT(.line))

.debug_srcinfo
 *(SORT_BY_ALIGNMENT(.debug_srcinfo))

.debug_sfnames
 *(SORT_BY_ALIGNMENT(.debug_sfnames))

.debug_aranges  0x0000000000000000     0x1aa0
 *(SORT_BY_ALIGNMENT(.debug_aranges))
 .debug_aranges
                0x0000000000000000       0x20 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .debug_aranges
                0x0000000000000020       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .debug_aranges
                0x0000000000000040       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .debug_aranges
                0x0000000000000060       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .debug_aranges
                0x0000000000000088       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
 .debug_aranges
                0x00000000000000a8       0x20 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_aranges
                0x00000000000000c8       0x28 app/libapp.a(main.c.obj)
 .debug_aranges
                0x00000000000000f0       0x30 app/libapp.a(button.c.obj)
 .debug_aranges
                0x0000000000000120       0xb8 zephyr/libzephyr.a(heap.c.obj)
 .debug_aranges
                0x00000000000001d8       0x40 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_aranges
                0x0000000000000218       0x48 zephyr/libzephyr.a(printk.c.obj)
 .debug_aranges
                0x0000000000000260       0x20 zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_aranges
                0x0000000000000280       0xb0 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .debug_aranges
                0x0000000000000330       0x90 zephyr/libzephyr.a(onoff.c.obj)
 .debug_aranges
                0x00000000000003c0       0x28 zephyr/libzephyr.a(notify.c.obj)
 .debug_aranges
                0x00000000000003e8       0x18 zephyr/libzephyr.a(last_section_id.c.obj)
 .debug_aranges
                0x0000000000000400       0x20 zephyr/libzephyr.a(configs.c.obj)
 .debug_aranges
                0x0000000000000420      0x170 zephyr/libzephyr.a(log_core.c.obj)
 .debug_aranges
                0x0000000000000590       0xb8 zephyr/libzephyr.a(log_mgmt.c.obj)
 .debug_aranges
                0x0000000000000648       0x60 zephyr/libzephyr.a(log_msg.c.obj)
 .debug_aranges
                0x00000000000006a8       0x70 zephyr/libzephyr.a(log_output.c.obj)
 .debug_aranges
                0x0000000000000718       0x48 zephyr/libzephyr.a(log_backend_uart.c.obj)
 .debug_aranges
                0x0000000000000760       0x28 zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_aranges
                0x0000000000000788       0x20 zephyr/libzephyr.a(banner.c.obj)
 .debug_aranges
                0x00000000000007a8       0x28 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_aranges
                0x00000000000007d0       0x40 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_aranges
                0x0000000000000810       0x30 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_aranges
                0x0000000000000840       0x20 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_aranges
                0x0000000000000860       0x20 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .debug_aranges
                0x0000000000000880       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_aranges
                0x00000000000008a0       0x48 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_aranges
                0x00000000000008e8       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_aranges
                0x0000000000000918       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_aranges
                0x0000000000000938       0x40 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_aranges
                0x0000000000000978       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_aranges
                0x0000000000000998       0x40 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_aranges
                0x00000000000009d8       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_aranges
                0x0000000000000a08       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_aranges
                0x0000000000000a28       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_aranges
                0x0000000000000a48       0x40 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_aranges
                0x0000000000000a88       0x28 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_aranges
                0x0000000000000ab0       0x68 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_aranges
                0x0000000000000b18       0x18 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .debug_aranges
                0x0000000000000b30       0x28 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(cbprintf.c.obj)
 .debug_aranges
                0x0000000000000b58       0x38 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .debug_aranges
                0x0000000000000b90       0x60 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .debug_aranges
                0x0000000000000bf0       0x40 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_aranges
                0x0000000000000c30       0x38 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_aranges
                0x0000000000000c68       0xe0 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_aranges
                0x0000000000000d48       0x30 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .debug_aranges
                0x0000000000000d78       0x68 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_aranges
                0x0000000000000de0       0x90 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .debug_aranges
                0x0000000000000e70       0x30 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .debug_aranges
                0x0000000000000ea0       0x20 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_aranges
                0x0000000000000ec0       0x28 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_aranges
                0x0000000000000ee8       0x48 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .debug_aranges
                0x0000000000000f30       0xd8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_aranges
                0x0000000000001008       0x28 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_aranges
                0x0000000000001030       0x40 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_aranges
                0x0000000000001070       0x30 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_aranges
                0x00000000000010a0       0x30 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_aranges
                0x00000000000010d0       0xf0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_aranges
                0x00000000000011c0       0x70 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_aranges
                0x0000000000001230       0x78 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_aranges
                0x00000000000012a8      0x188 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_aranges
                0x0000000000001430       0x70 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .debug_aranges
                0x00000000000014a0       0x20 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_aranges
                0x00000000000014c0       0x38 zephyr/kernel/libkernel.a(device.c.obj)
 .debug_aranges
                0x00000000000014f8       0x38 zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_aranges
                0x0000000000001530       0x68 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_aranges
                0x0000000000001598       0x20 zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_aranges
                0x00000000000015b8       0x48 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_aranges
                0x0000000000001600       0x28 zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_aranges
                0x0000000000001628       0x68 zephyr/kernel/libkernel.a(msg_q.c.obj)
 .debug_aranges
                0x0000000000001690       0x38 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_aranges
                0x00000000000016c8       0x38 zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_aranges
                0x0000000000001700       0x88 zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_aranges
                0x0000000000001788      0x170 zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_aranges
                0x00000000000018f8       0x40 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .debug_aranges
                0x0000000000001938       0x20 zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_aranges
                0x0000000000001958       0x98 zephyr/kernel/libkernel.a(timeout.c.obj)
 .debug_aranges
                0x00000000000019f0       0x48 zephyr/kernel/libkernel.a(timer.c.obj)
 .debug_aranges
                0x0000000000001a38       0x50 zephyr/kernel/libkernel.a(kheap.c.obj)
 .debug_aranges
                0x0000000000001a88       0x18 zephyr/arch/common/libisr_tables.a(isr_tables.c.obj)

.debug_pubnames
 *(SORT_BY_ALIGNMENT(.debug_pubnames))

.debug_info     0x0000000000000000    0x9c742
 *(SORT_BY_ALIGNMENT(.debug_info) SORT_BY_ALIGNMENT(.gnu.linkonce.wi.*))
 .debug_info    0x0000000000000000       0xdc zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_info    0x00000000000000dc      0xaac app/libapp.a(main.c.obj)
 .debug_info    0x0000000000000b88      0x9c6 app/libapp.a(button.c.obj)
 .debug_info    0x000000000000154e     0x2922 zephyr/libzephyr.a(heap.c.obj)
 .debug_info    0x0000000000003e70     0x1fd9 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_info    0x0000000000005e49      0x498 zephyr/libzephyr.a(printk.c.obj)
 .debug_info    0x00000000000062e1      0x750 zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_info    0x0000000000006a31     0x246d zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .debug_info    0x0000000000008e9e     0x28e3 zephyr/libzephyr.a(onoff.c.obj)
 .debug_info    0x000000000000b781      0x31d zephyr/libzephyr.a(notify.c.obj)
 .debug_info    0x000000000000ba9e       0x95 zephyr/libzephyr.a(last_section_id.c.obj)
 .debug_info    0x000000000000bb33       0x38 zephyr/libzephyr.a(configs.c.obj)
 .debug_info    0x000000000000bb6b     0x4353 zephyr/libzephyr.a(log_core.c.obj)
 .debug_info    0x000000000000febe     0x232e zephyr/libzephyr.a(log_mgmt.c.obj)
 .debug_info    0x00000000000121ec     0x23c8 zephyr/libzephyr.a(log_msg.c.obj)
 .debug_info    0x00000000000145b4     0x200b zephyr/libzephyr.a(log_output.c.obj)
 .debug_info    0x00000000000165bf     0x17dd zephyr/libzephyr.a(log_backend_uart.c.obj)
 .debug_info    0x0000000000017d9c      0x2ba zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_info    0x0000000000018056       0xdc zephyr/libzephyr.a(banner.c.obj)
 .debug_info    0x0000000000018132      0x246 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_info    0x0000000000018378     0x1083 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_info    0x00000000000193fb     0x3407 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_info    0x000000000001c802       0xb6 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_info    0x000000000001c8b8       0x23 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .debug_info    0x000000000001c8db      0x72b zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .debug_info    0x000000000001d006      0x94a zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_info    0x000000000001d950     0xcd0f zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_info    0x000000000002a65f       0x23 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .debug_info    0x000000000002a682       0x23 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .debug_info    0x000000000002a6a5      0x958 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_info    0x000000000002affd      0x9f1 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_info    0x000000000002b9ee       0x22 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .debug_info    0x000000000002ba10      0xa85 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_info    0x000000000002c495      0x596 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_info    0x000000000002ca2b      0xb2c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_info    0x000000000002d557      0x647 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_info    0x000000000002db9e      0x73a zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_info    0x000000000002e2d8      0x1a3 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_info    0x000000000002e47b       0x23 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
 .debug_info    0x000000000002e49e      0x630 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_info    0x000000000002eace      0x859 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_info    0x000000000002f327     0x5239 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_info    0x0000000000034560      0x1f1 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .debug_info    0x0000000000034751      0x307 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(cbprintf.c.obj)
 .debug_info    0x0000000000034a58      0x3f3 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .debug_info    0x0000000000034e4b      0xe01 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .debug_info    0x0000000000035c4c     0x1b46 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_info    0x0000000000037792      0xd8d zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_info    0x000000000003851f     0x4386 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_info    0x000000000003c8a5      0x742 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .debug_info    0x000000000003cfe7     0x2920 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_info    0x000000000003f907    0x1134b zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .debug_info    0x0000000000050c52     0x47d2 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .debug_info    0x0000000000055424      0x1af zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_info    0x00000000000555d3     0x2549 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_info    0x0000000000057b1c     0x3825 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .debug_info    0x000000000005b341     0x35ec zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_info    0x000000000005e92d      0x375 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_info    0x000000000005eca2     0x1a52 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_info    0x00000000000606f4      0x25d modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_info    0x0000000000060951      0x354 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_info    0x0000000000060ca5     0x1630 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_info    0x00000000000622d5     0x4a3d modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_info    0x0000000000066d12     0x525a modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_info    0x000000000006bf6c     0x8ee8 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_info    0x0000000000074e54     0x7427 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .debug_info    0x000000000007c27b       0xe3 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_info    0x000000000007c35e      0x315 zephyr/kernel/libkernel.a(device.c.obj)
 .debug_info    0x000000000007c673     0x2d20 zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_info    0x000000000007f393     0x2109 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_info    0x000000000008149c       0x38 zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_info    0x00000000000814d4     0x15d2 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_info    0x0000000000082aa6      0x2e7 zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_info    0x0000000000082d8d     0x1c2e zephyr/kernel/libkernel.a(msg_q.c.obj)
 .debug_info    0x00000000000849bb     0x669f zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_info    0x000000000008b05a     0x13b9 zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_info    0x000000000008c413     0x24a0 zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_info    0x000000000008e8b3     0x8878 zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_info    0x000000000009712b      0xf80 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .debug_info    0x00000000000980ab      0x12a zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_info    0x00000000000981d5     0x1a50 zephyr/kernel/libkernel.a(timeout.c.obj)
 .debug_info    0x0000000000099c25     0x1735 zephyr/kernel/libkernel.a(timer.c.obj)
 .debug_info    0x000000000009b35a     0x1219 zephyr/kernel/libkernel.a(kheap.c.obj)
 .debug_info    0x000000000009c573      0x1cf zephyr/arch/common/libisr_tables.a(isr_tables.c.obj)

.debug_abbrev   0x0000000000000000    0x1167f
 *(SORT_BY_ALIGNMENT(.debug_abbrev))
 .debug_abbrev  0x0000000000000000       0x62 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_abbrev  0x0000000000000062      0x346 app/libapp.a(main.c.obj)
 .debug_abbrev  0x00000000000003a8      0x342 app/libapp.a(button.c.obj)
 .debug_abbrev  0x00000000000006ea      0x449 zephyr/libzephyr.a(heap.c.obj)
 .debug_abbrev  0x0000000000000b33      0x54e zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_abbrev  0x0000000000001081      0x289 zephyr/libzephyr.a(printk.c.obj)
 .debug_abbrev  0x000000000000130a      0x2bc zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_abbrev  0x00000000000015c6      0x5d9 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .debug_abbrev  0x0000000000001b9f      0x54f zephyr/libzephyr.a(onoff.c.obj)
 .debug_abbrev  0x00000000000020ee      0x1bf zephyr/libzephyr.a(notify.c.obj)
 .debug_abbrev  0x00000000000022ad       0x46 zephyr/libzephyr.a(last_section_id.c.obj)
 .debug_abbrev  0x00000000000022f3       0x2e zephyr/libzephyr.a(configs.c.obj)
 .debug_abbrev  0x0000000000002321      0x938 zephyr/libzephyr.a(log_core.c.obj)
 .debug_abbrev  0x0000000000002c59      0x65c zephyr/libzephyr.a(log_mgmt.c.obj)
 .debug_abbrev  0x00000000000032b5      0x5b0 zephyr/libzephyr.a(log_msg.c.obj)
 .debug_abbrev  0x0000000000003865      0x5a4 zephyr/libzephyr.a(log_output.c.obj)
 .debug_abbrev  0x0000000000003e09      0x454 zephyr/libzephyr.a(log_backend_uart.c.obj)
 .debug_abbrev  0x000000000000425d      0x142 zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_abbrev  0x000000000000439f       0x9c zephyr/libzephyr.a(banner.c.obj)
 .debug_abbrev  0x000000000000443b      0x13d zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_abbrev  0x0000000000004578      0x406 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_abbrev  0x000000000000497e      0x3c2 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_abbrev  0x0000000000004d40       0x70 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_abbrev  0x0000000000004db0       0x14 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .debug_abbrev  0x0000000000004dc4      0x28e zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .debug_abbrev  0x0000000000005052      0x19f zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_abbrev  0x00000000000051f1      0x508 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_abbrev  0x00000000000056f9       0x14 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .debug_abbrev  0x000000000000570d       0x14 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .debug_abbrev  0x0000000000005721      0x286 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_abbrev  0x00000000000059a7      0x247 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_abbrev  0x0000000000005bee       0x12 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .debug_abbrev  0x0000000000005c00      0x27d zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_abbrev  0x0000000000005e7d      0x18e zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_abbrev  0x000000000000600b      0x38a zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_abbrev  0x0000000000006395      0x1b7 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_abbrev  0x000000000000654c      0x159 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_abbrev  0x00000000000066a5      0x136 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_abbrev  0x00000000000067db       0x14 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
 .debug_abbrev  0x00000000000067ef      0x1b2 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_abbrev  0x00000000000069a1      0x277 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_abbrev  0x0000000000006c18      0x60d zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_abbrev  0x0000000000007225      0x112 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .debug_abbrev  0x0000000000007337      0x18d zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(cbprintf.c.obj)
 .debug_abbrev  0x00000000000074c4      0x1f6 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .debug_abbrev  0x00000000000076ba      0x463 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .debug_abbrev  0x0000000000007b1d      0x548 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_abbrev  0x0000000000008065      0x450 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_abbrev  0x00000000000084b5      0x7b7 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_abbrev  0x0000000000008c6c      0x368 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .debug_abbrev  0x0000000000008fd4      0x5b6 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_abbrev  0x000000000000958a      0x6be zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .debug_abbrev  0x0000000000009c48      0x4ed zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .debug_abbrev  0x000000000000a135       0xd0 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_abbrev  0x000000000000a205      0x2b4 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_abbrev  0x000000000000a4b9      0x617 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .debug_abbrev  0x000000000000aad0      0x6e4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_abbrev  0x000000000000b1b4      0x210 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_abbrev  0x000000000000b3c4      0x28f modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_abbrev  0x000000000000b653      0x155 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_abbrev  0x000000000000b7a8      0x10b modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_abbrev  0x000000000000b8b3      0x35b modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_abbrev  0x000000000000bc0e      0x5aa modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_abbrev  0x000000000000c1b8      0x466 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_abbrev  0x000000000000c61e      0x612 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_abbrev  0x000000000000cc30      0x544 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .debug_abbrev  0x000000000000d174       0x9d zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_abbrev  0x000000000000d211      0x1bc zephyr/kernel/libkernel.a(device.c.obj)
 .debug_abbrev  0x000000000000d3cd      0x4ad zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_abbrev  0x000000000000d87a      0x719 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_abbrev  0x000000000000df93       0x2e zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_abbrev  0x000000000000dfc1      0x57b zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_abbrev  0x000000000000e53c      0x1ee zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_abbrev  0x000000000000e72a      0x4f2 zephyr/kernel/libkernel.a(msg_q.c.obj)
 .debug_abbrev  0x000000000000ec1c      0x4c6 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_abbrev  0x000000000000f0e2      0x445 zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_abbrev  0x000000000000f527      0x565 zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_abbrev  0x000000000000fa8c      0x753 zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_abbrev  0x00000000000101df      0x42f zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .debug_abbrev  0x000000000001060e       0xbe zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_abbrev  0x00000000000106cc      0x524 zephyr/kernel/libkernel.a(timeout.c.obj)
 .debug_abbrev  0x0000000000010bf0      0x4eb zephyr/kernel/libkernel.a(timer.c.obj)
 .debug_abbrev  0x00000000000110db      0x48a zephyr/kernel/libkernel.a(kheap.c.obj)
 .debug_abbrev  0x0000000000011565      0x11a zephyr/arch/common/libisr_tables.a(isr_tables.c.obj)

.debug_line     0x0000000000000000    0x2dc1e
 *(SORT_BY_ALIGNMENT(.debug_line) SORT_BY_ALIGNMENT(.debug_line.*) SORT_BY_ALIGNMENT(.debug_line_end))
 .debug_line    0x0000000000000000      0x111 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_line    0x0000000000000111      0x4a3 app/libapp.a(main.c.obj)
 .debug_line    0x00000000000005b4      0x45e app/libapp.a(button.c.obj)
 .debug_line    0x0000000000000a12      0xf9b zephyr/libzephyr.a(heap.c.obj)
 .debug_line    0x00000000000019ad     0x1598 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_line    0x0000000000002f45      0x385 zephyr/libzephyr.a(printk.c.obj)
 .debug_line    0x00000000000032ca      0x3ea zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_line    0x00000000000036b4     0x1983 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .debug_line    0x0000000000005037     0x1691 zephyr/libzephyr.a(onoff.c.obj)
 .debug_line    0x00000000000066c8      0x24b zephyr/libzephyr.a(notify.c.obj)
 .debug_line    0x0000000000006913      0x118 zephyr/libzephyr.a(last_section_id.c.obj)
 .debug_line    0x0000000000006a2b      0x2f3 zephyr/libzephyr.a(configs.c.obj)
 .debug_line    0x0000000000006d1e     0x15c9 zephyr/libzephyr.a(log_core.c.obj)
 .debug_line    0x00000000000082e7      0xc3b zephyr/libzephyr.a(log_mgmt.c.obj)
 .debug_line    0x0000000000008f22      0xd3b zephyr/libzephyr.a(log_msg.c.obj)
 .debug_line    0x0000000000009c5d      0xb51 zephyr/libzephyr.a(log_output.c.obj)
 .debug_line    0x000000000000a7ae      0x643 zephyr/libzephyr.a(log_backend_uart.c.obj)
 .debug_line    0x000000000000adf1      0x243 zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_line    0x000000000000b034       0xa9 zephyr/libzephyr.a(banner.c.obj)
 .debug_line    0x000000000000b0dd      0x207 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_line    0x000000000000b2e4      0x7a6 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_line    0x000000000000ba8a      0x673 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_line    0x000000000000c0fd       0xa3 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_line    0x000000000000c1a0       0x63 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .debug_line    0x000000000000c203      0x3e6 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .debug_line    0x000000000000c5e9      0x318 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_line    0x000000000000c901      0xe8f zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_line    0x000000000000d790       0x70 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .debug_line    0x000000000000d800       0x8e zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .debug_line    0x000000000000d88e      0x47b zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_line    0x000000000000dd09      0x3c9 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_line    0x000000000000e0d2       0xc7 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .debug_line    0x000000000000e199      0x37f zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_line    0x000000000000e518      0x2f3 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_line    0x000000000000e80b      0x580 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_line    0x000000000000ed8b      0x3bc zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_line    0x000000000000f147      0x233 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_line    0x000000000000f37a      0x23d zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_line    0x000000000000f5b7       0x71 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
 .debug_line    0x000000000000f628      0x313 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_line    0x000000000000f93b      0x416 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_line    0x000000000000fd51     0x106b zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_line    0x0000000000010dbc      0x212 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .debug_line    0x0000000000010fce      0x224 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(cbprintf.c.obj)
 .debug_line    0x00000000000111f2      0x2fe zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .debug_line    0x00000000000114f0      0x6c6 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .debug_line    0x0000000000011bb6      0x852 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_line    0x0000000000012408      0x486 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_line    0x000000000001288e     0x15ef zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_line    0x0000000000013e7d      0x408 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .debug_line    0x0000000000014285      0xd90 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_line    0x0000000000015015     0x2454 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .debug_line    0x0000000000017469      0xf7b zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .debug_line    0x00000000000183e4      0x1d1 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_line    0x00000000000185b5      0x6f1 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_line    0x0000000000018ca6     0x1091 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .debug_line    0x0000000000019d37     0x169e zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_line    0x000000000001b3d5      0x248 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_line    0x000000000001b61d      0x587 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_line    0x000000000001bba4      0x2b6 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_line    0x000000000001be5a      0x2e4 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_line    0x000000000001c13e      0x85a modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_line    0x000000000001c998      0xf3f modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_line    0x000000000001d8d7      0xc7d modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_line    0x000000000001e554     0x22d3 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_line    0x0000000000020827     0x1204 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .debug_line    0x0000000000021a2b      0x173 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_line    0x0000000000021b9e      0x34a zephyr/kernel/libkernel.a(device.c.obj)
 .debug_line    0x0000000000021ee8      0xa2b zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_line    0x0000000000022913      0xca0 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_line    0x00000000000235b3       0x5c zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_line    0x000000000002360f      0xa3b zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_line    0x000000000002404a      0x306 zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_line    0x0000000000024350      0xf1d zephyr/kernel/libkernel.a(msg_q.c.obj)
 .debug_line    0x000000000002526d      0xaef zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_line    0x0000000000025d5c      0x9ce zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_line    0x000000000002672a      0xa83 zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_line    0x00000000000271ad     0x3af0 zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_line    0x000000000002ac9d      0x6d5 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .debug_line    0x000000000002b372      0x154 zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_line    0x000000000002b4c6     0x10c3 zephyr/kernel/libkernel.a(timeout.c.obj)
 .debug_line    0x000000000002c589      0xc5e zephyr/kernel/libkernel.a(timer.c.obj)
 .debug_line    0x000000000002d1e7      0x8db zephyr/kernel/libkernel.a(kheap.c.obj)
 .debug_line    0x000000000002dac2      0x15c zephyr/arch/common/libisr_tables.a(isr_tables.c.obj)

.debug_frame    0x0000000000000000     0x41f4
 *(SORT_BY_ALIGNMENT(.debug_frame))
 .debug_frame   0x0000000000000000       0x20 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_frame   0x0000000000000020       0x38 app/libapp.a(main.c.obj)
 .debug_frame   0x0000000000000058       0x58 app/libapp.a(button.c.obj)
 .debug_frame   0x00000000000000b0      0x2a0 zephyr/libzephyr.a(heap.c.obj)
 .debug_frame   0x0000000000000350       0xf4 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_frame   0x0000000000000444       0x9c zephyr/libzephyr.a(printk.c.obj)
 .debug_frame   0x00000000000004e0       0x28 zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_frame   0x0000000000000508      0x290 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .debug_frame   0x0000000000000798      0x190 zephyr/libzephyr.a(onoff.c.obj)
 .debug_frame   0x0000000000000928       0x30 zephyr/libzephyr.a(notify.c.obj)
 .debug_frame   0x0000000000000958       0x20 zephyr/libzephyr.a(configs.c.obj)
 .debug_frame   0x0000000000000978      0x3f8 zephyr/libzephyr.a(log_core.c.obj)
 .debug_frame   0x0000000000000d70      0x1b0 zephyr/libzephyr.a(log_mgmt.c.obj)
 .debug_frame   0x0000000000000f20      0x160 zephyr/libzephyr.a(log_msg.c.obj)
 .debug_frame   0x0000000000001080      0x16c zephyr/libzephyr.a(log_output.c.obj)
 .debug_frame   0x00000000000011ec       0xa4 zephyr/libzephyr.a(log_backend_uart.c.obj)
 .debug_frame   0x0000000000001290       0x30 zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_frame   0x00000000000012c0       0x2c zephyr/libzephyr.a(banner.c.obj)
 .debug_frame   0x00000000000012ec       0x30 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_frame   0x000000000000131c       0x90 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_frame   0x00000000000013ac       0x64 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_frame   0x0000000000001410       0x2c zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_frame   0x000000000000143c       0x30 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .debug_frame   0x000000000000146c       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_frame   0x000000000000148c       0xb0 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_frame   0x000000000000153c       0x48 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_frame   0x0000000000001584       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_frame   0x00000000000015a4       0x60 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_frame   0x0000000000001604       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_frame   0x000000000000162c       0x68 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_frame   0x0000000000001694       0x50 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_frame   0x00000000000016e4       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_frame   0x0000000000001704       0x2c zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_frame   0x0000000000001730       0x70 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_frame   0x00000000000017a0       0x3c zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_frame   0x00000000000017dc      0x144 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_frame   0x0000000000001920       0x4c zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(cbprintf.c.obj)
 .debug_frame   0x000000000000196c       0x60 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .debug_frame   0x00000000000019cc       0xf0 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .debug_frame   0x0000000000001abc       0x84 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_frame   0x0000000000001b40       0x80 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_frame   0x0000000000001bc0      0x264 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_frame   0x0000000000001e24       0x54 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .debug_frame   0x0000000000001e78      0x104 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_frame   0x0000000000001f7c      0x1f0 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .debug_frame   0x000000000000216c       0x80 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .debug_frame   0x00000000000021ec       0x2c zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_frame   0x0000000000002218       0x50 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_frame   0x0000000000002268       0x94 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .debug_frame   0x00000000000022fc      0x268 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_frame   0x0000000000002564       0x3c modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_frame   0x00000000000025a0       0x68 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_frame   0x0000000000002608       0x48 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_frame   0x0000000000002650       0x54 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_frame   0x00000000000026a4      0x1c0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_frame   0x0000000000002864      0x10c modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_frame   0x0000000000002970      0x140 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_frame   0x0000000000002ab0      0x4c0 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_frame   0x0000000000002f70      0x138 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .debug_frame   0x00000000000030a8       0x20 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_frame   0x00000000000030c8       0x5c zephyr/kernel/libkernel.a(device.c.obj)
 .debug_frame   0x0000000000003124       0x7c zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_frame   0x00000000000031a0      0x114 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_frame   0x00000000000032b4       0x20 zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_frame   0x00000000000032d4       0xb8 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_frame   0x000000000000338c       0x38 zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_frame   0x00000000000033c4      0x120 zephyr/kernel/libkernel.a(msg_q.c.obj)
 .debug_frame   0x00000000000034e4       0x78 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_frame   0x000000000000355c       0x90 zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_frame   0x00000000000035ec      0x148 zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_frame   0x0000000000003734      0x4ac zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_frame   0x0000000000003be0       0x94 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .debug_frame   0x0000000000003c74       0x2c zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_frame   0x0000000000003ca0      0x1bc zephyr/kernel/libkernel.a(timeout.c.obj)
 .debug_frame   0x0000000000003e5c       0xb8 zephyr/kernel/libkernel.a(timer.c.obj)
 .debug_frame   0x0000000000003f14      0x104 zephyr/kernel/libkernel.a(kheap.c.obj)
 .debug_frame   0x0000000000004018       0x28 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memcpy.c.o)
 .debug_frame   0x0000000000004040       0x20 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memset.c.o)
 .debug_frame   0x0000000000004060       0x20 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strlen.S.o)
 .debug_frame   0x0000000000004080       0x28 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_memcmp.c.o)
 .debug_frame   0x00000000000040a8       0x28 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strnlen.c.o)
 .debug_frame   0x00000000000040d0       0x44 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_snprintf.c.o)
 .debug_frame   0x0000000000004114       0x60 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
 .debug_frame   0x0000000000004174       0x20 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_filestrput.c.o)
 .debug_frame   0x0000000000004194       0x2c c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
 .debug_frame   0x00000000000041c0       0x34 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)

.debug_str      0x0000000000000000     0xfd96
 *(SORT_BY_ALIGNMENT(.debug_str))
 .debug_str     0x0000000000000000      0x386 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
                                        0x3bf (size before relaxing)
 .debug_str     0x0000000000000386      0x6bd app/libapp.a(main.c.obj)
                                        0x958 (size before relaxing)
 .debug_str     0x0000000000000a43       0xfd app/libapp.a(button.c.obj)
                                        0x8d3 (size before relaxing)
 .debug_str     0x0000000000000b40      0x3db zephyr/libzephyr.a(heap.c.obj)
                                        0x744 (size before relaxing)
 .debug_str     0x0000000000000f1b      0x6ab zephyr/libzephyr.a(cbprintf_packaged.c.obj)
                                        0xb5c (size before relaxing)
 .debug_str     0x00000000000015c6       0xd3 zephyr/libzephyr.a(printk.c.obj)
                                        0x5ee (size before relaxing)
 .debug_str     0x0000000000001699      0x1f5 zephyr/libzephyr.a(thread_entry.c.obj)
                                        0x7d1 (size before relaxing)
 .debug_str     0x000000000000188e      0x3d9 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
                                        0x7f8 (size before relaxing)
 .debug_str     0x0000000000001c67      0x407 zephyr/libzephyr.a(onoff.c.obj)
                                        0x872 (size before relaxing)
 .debug_str     0x000000000000206e       0x3e zephyr/libzephyr.a(notify.c.obj)
                                        0x394 (size before relaxing)
 .debug_str     0x00000000000020ac       0x39 zephyr/libzephyr.a(last_section_id.c.obj)
                                        0x2c0 (size before relaxing)
 .debug_str     0x00000000000020e5       0x8c zephyr/libzephyr.a(configs.c.obj)
                                        0x26f (size before relaxing)
 .debug_str     0x0000000000002171      0xb3e zephyr/libzephyr.a(log_core.c.obj)
                                       0x1c83 (size before relaxing)
 .debug_str     0x0000000000002caf      0x559 zephyr/libzephyr.a(log_mgmt.c.obj)
                                        0xfef (size before relaxing)
 .debug_str     0x0000000000003208      0x11e zephyr/libzephyr.a(log_msg.c.obj)
                                        0xc79 (size before relaxing)
 .debug_str     0x0000000000003326      0x27f zephyr/libzephyr.a(log_output.c.obj)
                                        0xc3e (size before relaxing)
 .debug_str     0x00000000000035a5      0x1eb zephyr/libzephyr.a(log_backend_uart.c.obj)
                                        0xdf9 (size before relaxing)
 .debug_str     0x0000000000003790       0xab zephyr/libzephyr.a(mem_attr.c.obj)
                                        0x591 (size before relaxing)
 .debug_str     0x000000000000383b       0x37 zephyr/libzephyr.a(banner.c.obj)
                                        0x2bc (size before relaxing)
 .debug_str     0x0000000000003872      0x116 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
                                        0x5cc (size before relaxing)
 .debug_str     0x0000000000003988      0x49f zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                                        0xcf9 (size before relaxing)
 .debug_str     0x0000000000003e27      0x10b zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
                                        0xa7b (size before relaxing)
 .debug_str     0x0000000000003f32       0x52 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
                                        0x2d0 (size before relaxing)
 .debug_str     0x0000000000003f84       0x3e zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
                                         0x9b (size before relaxing)
 .debug_str     0x0000000000003fc2       0x98 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
                                        0x7c7 (size before relaxing)
 .debug_str     0x000000000000405a      0x19b zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
                                        0x6b1 (size before relaxing)
 .debug_str     0x00000000000041f5      0x668 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
                                       0x1273 (size before relaxing)
 .debug_str     0x000000000000485d       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
                                         0x9f (size before relaxing)
 .debug_str     0x0000000000004893       0x34 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
                                         0x9d (size before relaxing)
 .debug_str     0x00000000000048c7      0x17f zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
                                        0x5e5 (size before relaxing)
 .debug_str     0x0000000000004a46       0x59 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
                                        0x6fb (size before relaxing)
 .debug_str     0x0000000000004a9f       0x3a zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
                                         0xa3 (size before relaxing)
 .debug_str     0x0000000000004ad9      0x354 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
                                        0x858 (size before relaxing)
 .debug_str     0x0000000000004e2d       0xb1 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
                                        0x50b (size before relaxing)
 .debug_str     0x0000000000004ede      0x110 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
                                        0x757 (size before relaxing)
 .debug_str     0x0000000000004fee       0x86 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
                                        0x4a3 (size before relaxing)
 .debug_str     0x0000000000005074       0x37 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
                                        0x717 (size before relaxing)
 .debug_str     0x00000000000050ab       0x66 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
                                        0x33c (size before relaxing)
 .debug_str     0x0000000000005111       0x3e zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
                                         0xa7 (size before relaxing)
 .debug_str     0x000000000000514f      0x185 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
                                        0x454 (size before relaxing)
 .debug_str     0x00000000000052d4      0x1bf zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
                                        0x950 (size before relaxing)
 .debug_str     0x0000000000005493      0x3cb zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
                                       0x10b6 (size before relaxing)
 .debug_str     0x000000000000585e       0x39 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
                                        0x3fb (size before relaxing)
 .debug_str     0x0000000000005897       0x4a zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(cbprintf.c.obj)
                                        0x36a (size before relaxing)
 .debug_str     0x00000000000058e1       0xa7 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
                                        0x5be (size before relaxing)
 .debug_str     0x0000000000005988      0x2c0 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
                                        0x829 (size before relaxing)
 .debug_str     0x0000000000005c48      0xfa9 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
                                       0x1685 (size before relaxing)
 .debug_str     0x0000000000006bf1      0x269 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
                                        0x9ad (size before relaxing)
 .debug_str     0x0000000000006e5a     0x107c zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
                                       0x227e (size before relaxing)
 .debug_str     0x0000000000007ed6       0xfe zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
                                        0x5b6 (size before relaxing)
 .debug_str     0x0000000000007fd4      0xa9e zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
                                       0x16af (size before relaxing)
 .debug_str     0x0000000000008a72      0xe60 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
                                       0x1f71 (size before relaxing)
 .debug_str     0x00000000000098d2      0x288 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
                                        0xf72 (size before relaxing)
 .debug_str     0x0000000000009b5a       0x2e zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
                                        0x35b (size before relaxing)
 .debug_str     0x0000000000009b88      0x7ce zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
                                       0x10b3 (size before relaxing)
 .debug_str     0x000000000000a356      0xc3c zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
                                       0x1f80 (size before relaxing)
 .debug_str     0x000000000000af92      0xaea zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
                                       0x1747 (size before relaxing)
 .debug_str     0x000000000000ba7c       0x80 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
                                        0x5fe (size before relaxing)
 .debug_str     0x000000000000bafc      0x40b modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
                                        0xe22 (size before relaxing)
 .debug_str     0x000000000000bf07       0x82 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
                                        0x4d7 (size before relaxing)
 .debug_str     0x000000000000bf89       0xaa modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
                                        0x522 (size before relaxing)
 .debug_str     0x000000000000c033      0x92a modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
                                       0x10c3 (size before relaxing)
 .debug_str     0x000000000000c95d      0x285 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
                                       0x1954 (size before relaxing)
 .debug_str     0x000000000000cbe2      0x189 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
                                        0xef4 (size before relaxing)
 .debug_str     0x000000000000cd6b     0x11ec modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
                                       0x27a9 (size before relaxing)
 .debug_str     0x000000000000df57      0x593 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
                                       0x1d80 (size before relaxing)
 .debug_str     0x000000000000e4ea       0x28 zephyr/kernel/libkernel.a(busy_wait.c.obj)
                                        0x2e9 (size before relaxing)
 .debug_str     0x000000000000e512       0xa5 zephyr/kernel/libkernel.a(device.c.obj)
                                        0x3b0 (size before relaxing)
 .debug_str     0x000000000000e5b7       0xad zephyr/kernel/libkernel.a(fatal.c.obj)
                                        0xdad (size before relaxing)
 .debug_str     0x000000000000e664      0x53c zephyr/kernel/libkernel.a(init.c.obj)
                                       0x13b0 (size before relaxing)
 .debug_str     0x000000000000eba0       0x2a zephyr/kernel/libkernel.a(init_static.c.obj)
                                        0x21b (size before relaxing)
 .debug_str     0x000000000000ebca      0x24e zephyr/kernel/libkernel.a(mem_slab.c.obj)
                                        0xbf3 (size before relaxing)
 .debug_str     0x000000000000ee18       0x47 zephyr/kernel/libkernel.a(idle.c.obj)
                                        0x3da (size before relaxing)
 .debug_str     0x000000000000ee5f      0x10d zephyr/kernel/libkernel.a(msg_q.c.obj)
                                        0x987 (size before relaxing)
 .debug_str     0x000000000000ef6c      0x115 zephyr/kernel/libkernel.a(mutex.c.obj)
                                        0xd16 (size before relaxing)
 .debug_str     0x000000000000f081       0x48 zephyr/kernel/libkernel.a(sem.c.obj)
                                        0x9f1 (size before relaxing)
 .debug_str     0x000000000000f0c9      0x20c zephyr/kernel/libkernel.a(thread.c.obj)
                                        0xe03 (size before relaxing)
 .debug_str     0x000000000000f2d5      0x6b1 zephyr/kernel/libkernel.a(sched.c.obj)
                                       0x1643 (size before relaxing)
 .debug_str     0x000000000000f986       0xb3 zephyr/kernel/libkernel.a(timeslicing.c.obj)
                                        0xa57 (size before relaxing)
 .debug_str     0x000000000000fa39       0x9b zephyr/kernel/libkernel.a(xip.c.obj)
                                        0x33b (size before relaxing)
 .debug_str     0x000000000000fad4      0x113 zephyr/kernel/libkernel.a(timeout.c.obj)
                                        0x908 (size before relaxing)
 .debug_str     0x000000000000fbe7       0x7d zephyr/kernel/libkernel.a(timer.c.obj)
                                        0xa88 (size before relaxing)
 .debug_str     0x000000000000fc64       0xd9 zephyr/kernel/libkernel.a(kheap.c.obj)
                                        0x8bb (size before relaxing)
 .debug_str     0x000000000000fd3d       0x59 zephyr/arch/common/libisr_tables.a(isr_tables.c.obj)
                                        0x3d5 (size before relaxing)

.debug_loc      0x0000000000000000    0x2f4b4
 *(SORT_BY_ALIGNMENT(.debug_loc))
 .debug_loc     0x0000000000000000      0x131 app/libapp.a(main.c.obj)
 .debug_loc     0x0000000000000131      0x278 app/libapp.a(button.c.obj)
 .debug_loc     0x00000000000003a9     0x2282 zephyr/libzephyr.a(heap.c.obj)
 .debug_loc     0x000000000000262b     0x2573 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_loc     0x0000000000004b9e      0x11c zephyr/libzephyr.a(printk.c.obj)
 .debug_loc     0x0000000000004cba       0x9d zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_loc     0x0000000000004d57     0x1678 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .debug_loc     0x00000000000063cf     0x2057 zephyr/libzephyr.a(onoff.c.obj)
 .debug_loc     0x0000000000008426      0x135 zephyr/libzephyr.a(notify.c.obj)
 .debug_loc     0x000000000000855b     0x11f2 zephyr/libzephyr.a(log_core.c.obj)
 .debug_loc     0x000000000000974d      0xc61 zephyr/libzephyr.a(log_mgmt.c.obj)
 .debug_loc     0x000000000000a3ae     0x11ef zephyr/libzephyr.a(log_msg.c.obj)
 .debug_loc     0x000000000000b59d     0x1727 zephyr/libzephyr.a(log_output.c.obj)
 .debug_loc     0x000000000000ccc4      0x48e zephyr/libzephyr.a(log_backend_uart.c.obj)
 .debug_loc     0x000000000000d152       0x6f zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_loc     0x000000000000d1c1       0x15 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_loc     0x000000000000d1d6      0x340 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_loc     0x000000000000d516      0x9cf zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_loc     0x000000000000dee5       0x85 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .debug_loc     0x000000000000df6a     0x19db zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_loc     0x000000000000f945       0xbc zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_loc     0x000000000000fa01       0x3a zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_loc     0x000000000000fa3b      0x1a1 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_loc     0x000000000000fbdc      0x1b7 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_loc     0x000000000000fd93       0x78 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_loc     0x000000000000fe0b       0x6a zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_loc     0x000000000000fe75       0x3f zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_loc     0x000000000000feb4      0x5f9 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_loc     0x00000000000104ad       0x25 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_loc     0x00000000000104d2      0xf66 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_loc     0x0000000000011438      0x128 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(cbprintf.c.obj)
 .debug_loc     0x0000000000011560       0xd3 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .debug_loc     0x0000000000011633      0x45a zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .debug_loc     0x0000000000011a8d      0x5f0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_loc     0x000000000001207d      0x2b3 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_loc     0x0000000000012330     0x1915 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_loc     0x0000000000013c45       0x85 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .debug_loc     0x0000000000013cca     0x189e zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_loc     0x0000000000015568     0x2731 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .debug_loc     0x0000000000017c99      0xf32 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .debug_loc     0x0000000000018bcb       0x42 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_loc     0x0000000000018c0d      0xa22 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_loc     0x000000000001962f     0x1290 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .debug_loc     0x000000000001a8bf     0x1c42 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_loc     0x000000000001c501      0x182 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_loc     0x000000000001c683      0x258 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_loc     0x000000000001c8db      0x221 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_loc     0x000000000001cafc      0x996 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_loc     0x000000000001d492     0x10b4 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_loc     0x000000000001e546      0xf8f modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_loc     0x000000000001f4d5     0x3eaa modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_loc     0x000000000002337f     0x16e4 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .debug_loc     0x0000000000024a63       0x32 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_loc     0x0000000000024a95       0xea zephyr/kernel/libkernel.a(device.c.obj)
 .debug_loc     0x0000000000024b7f      0x8e2 zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_loc     0x0000000000025461      0x83a zephyr/kernel/libkernel.a(init.c.obj)
 .debug_loc     0x0000000000025c9b      0x682 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_loc     0x000000000002631d       0x86 zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_loc     0x00000000000263a3      0xec1 zephyr/kernel/libkernel.a(msg_q.c.obj)
 .debug_loc     0x0000000000027264      0x884 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_loc     0x0000000000027ae8      0x6e4 zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_loc     0x00000000000281cc      0x9f9 zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_loc     0x0000000000028bc5     0x44cb zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_loc     0x000000000002d090      0x3fe zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .debug_loc     0x000000000002d48e      0xe76 zephyr/kernel/libkernel.a(timeout.c.obj)
 .debug_loc     0x000000000002e304      0x95f zephyr/kernel/libkernel.a(timer.c.obj)
 .debug_loc     0x000000000002ec63      0x851 zephyr/kernel/libkernel.a(kheap.c.obj)

.debug_macinfo
 *(SORT_BY_ALIGNMENT(.debug_macinfo))

.debug_weaknames
 *(SORT_BY_ALIGNMENT(.debug_weaknames))

.debug_funcnames
 *(SORT_BY_ALIGNMENT(.debug_funcnames))

.debug_typenames
 *(SORT_BY_ALIGNMENT(.debug_typenames))

.debug_varnames
 *(SORT_BY_ALIGNMENT(.debug_varnames))

.debug_pubtypes
 *(SORT_BY_ALIGNMENT(.debug_pubtypes))

.debug_ranges   0x0000000000000000     0x8348
 *(SORT_BY_ALIGNMENT(.debug_ranges))
 .debug_ranges  0x0000000000000000       0x20 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .debug_ranges  0x0000000000000020       0x10 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .debug_ranges  0x0000000000000030       0x30 app/libapp.a(main.c.obj)
 .debug_ranges  0x0000000000000060       0x80 app/libapp.a(button.c.obj)
 .debug_ranges  0x00000000000000e0      0x420 zephyr/libzephyr.a(heap.c.obj)
 .debug_ranges  0x0000000000000500      0x360 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .debug_ranges  0x0000000000000860       0x38 zephyr/libzephyr.a(printk.c.obj)
 .debug_ranges  0x0000000000000898       0x28 zephyr/libzephyr.a(thread_entry.c.obj)
 .debug_ranges  0x00000000000008c0      0x4d0 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .debug_ranges  0x0000000000000d90      0x498 zephyr/libzephyr.a(onoff.c.obj)
 .debug_ranges  0x0000000000001228       0x30 zephyr/libzephyr.a(notify.c.obj)
 .debug_ranges  0x0000000000001258       0x10 zephyr/libzephyr.a(configs.c.obj)
 .debug_ranges  0x0000000000001268      0x4c8 zephyr/libzephyr.a(log_core.c.obj)
 .debug_ranges  0x0000000000001730      0x320 zephyr/libzephyr.a(log_mgmt.c.obj)
 .debug_ranges  0x0000000000001a50      0x270 zephyr/libzephyr.a(log_msg.c.obj)
 .debug_ranges  0x0000000000001cc0      0x270 zephyr/libzephyr.a(log_output.c.obj)
 .debug_ranges  0x0000000000001f30       0xe8 zephyr/libzephyr.a(log_backend_uart.c.obj)
 .debug_ranges  0x0000000000002018       0x18 zephyr/libzephyr.a(mem_attr.c.obj)
 .debug_ranges  0x0000000000002030       0x10 zephyr/libzephyr.a(banner.c.obj)
 .debug_ranges  0x0000000000002040       0x18 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .debug_ranges  0x0000000000002058       0x78 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .debug_ranges  0x00000000000020d0      0x100 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .debug_ranges  0x00000000000021d0       0x10 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .debug_ranges  0x00000000000021e0       0x50 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .debug_ranges  0x0000000000002230       0x10 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .debug_ranges  0x0000000000002240      0x468 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .debug_ranges  0x00000000000026a8       0x68 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .debug_ranges  0x0000000000002710       0x10 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .debug_ranges  0x0000000000002720       0x48 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .debug_ranges  0x0000000000002768       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .debug_ranges  0x0000000000002790       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .debug_ranges  0x00000000000027c0       0x50 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .debug_ranges  0x0000000000002810       0x30 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .debug_ranges  0x0000000000002840       0x28 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .debug_ranges  0x0000000000002868       0xd8 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .debug_ranges  0x0000000000002940       0x18 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .debug_ranges  0x0000000000002958      0x468 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .debug_ranges  0x0000000000002dc0       0x18 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(cbprintf.c.obj)
 .debug_ranges  0x0000000000002dd8       0x40 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .debug_ranges  0x0000000000002e18       0x80 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .debug_ranges  0x0000000000002e98      0x168 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .debug_ranges  0x0000000000003000       0xb0 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .debug_ranges  0x00000000000030b0      0x488 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .debug_ranges  0x0000000000003538       0x38 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .debug_ranges  0x0000000000003570      0x2e8 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .debug_ranges  0x0000000000003858      0x530 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .debug_ranges  0x0000000000003d88      0x388 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .debug_ranges  0x0000000000004110       0x10 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .debug_ranges  0x0000000000004120      0x120 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .debug_ranges  0x0000000000004240      0x260 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .debug_ranges  0x00000000000044a0      0x5e8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .debug_ranges  0x0000000000004a88       0x30 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .debug_ranges  0x0000000000004ab8       0x30 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .debug_ranges  0x0000000000004ae8       0x20 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .debug_ranges  0x0000000000004b08       0x50 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .debug_ranges  0x0000000000004b58       0xf8 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .debug_ranges  0x0000000000004c50      0x278 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .debug_ranges  0x0000000000004ec8      0x328 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .debug_ranges  0x00000000000051f0      0xb68 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .debug_ranges  0x0000000000005d58      0x330 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .debug_ranges  0x0000000000006088       0x10 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .debug_ranges  0x0000000000006098       0x88 zephyr/kernel/libkernel.a(device.c.obj)
 .debug_ranges  0x0000000000006120      0x2e8 zephyr/kernel/libkernel.a(fatal.c.obj)
 .debug_ranges  0x0000000000006408      0x278 zephyr/kernel/libkernel.a(init.c.obj)
 .debug_ranges  0x0000000000006680       0x10 zephyr/kernel/libkernel.a(init_static.c.obj)
 .debug_ranges  0x0000000000006690      0x150 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .debug_ranges  0x00000000000067e0       0x48 zephyr/kernel/libkernel.a(idle.c.obj)
 .debug_ranges  0x0000000000006828      0x240 zephyr/kernel/libkernel.a(msg_q.c.obj)
 .debug_ranges  0x0000000000006a68      0x150 zephyr/kernel/libkernel.a(mutex.c.obj)
 .debug_ranges  0x0000000000006bb8      0x1c8 zephyr/kernel/libkernel.a(sem.c.obj)
 .debug_ranges  0x0000000000006d80      0x1a8 zephyr/kernel/libkernel.a(thread.c.obj)
 .debug_ranges  0x0000000000006f28      0xe28 zephyr/kernel/libkernel.a(sched.c.obj)
 .debug_ranges  0x0000000000007d50       0xa8 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .debug_ranges  0x0000000000007df8       0x10 zephyr/kernel/libkernel.a(xip.c.obj)
 .debug_ranges  0x0000000000007e08      0x320 zephyr/kernel/libkernel.a(timeout.c.obj)
 .debug_ranges  0x0000000000008128      0x158 zephyr/kernel/libkernel.a(timer.c.obj)
 .debug_ranges  0x0000000000008280       0xc8 zephyr/kernel/libkernel.a(kheap.c.obj)

.debug_addr
 *(SORT_BY_ALIGNMENT(.debug_addr))

.debug_line_str
 *(SORT_BY_ALIGNMENT(.debug_line_str))

.debug_loclists
 *(SORT_BY_ALIGNMENT(.debug_loclists))

.debug_macro
 *(SORT_BY_ALIGNMENT(.debug_macro))

.debug_names
 *(SORT_BY_ALIGNMENT(.debug_names))

.debug_rnglists
 *(SORT_BY_ALIGNMENT(.debug_rnglists))

.debug_str_offsets
 *(SORT_BY_ALIGNMENT(.debug_str_offsets))

.debug_sup
 *(SORT_BY_ALIGNMENT(.debug_sup))

/DISCARD/
 *(SORT_BY_ALIGNMENT(.note.GNU-stack))

.ARM.attributes
                0x0000000000000000       0x38
 *(SORT_BY_ALIGNMENT(.ARM.attributes))
 .ARM.attributes
                0x0000000000000000       0x36 zephyr/CMakeFiles/zephyr_pre0.dir/misc/empty_file.c.obj
 .ARM.attributes
                0x0000000000000036       0x36 zephyr/CMakeFiles/offsets.dir/./arch/arm/core/offsets/offsets.c.obj
 .ARM.attributes
                0x000000000000006c       0x36 app/libapp.a(main.c.obj)
 .ARM.attributes
                0x00000000000000a2       0x36 app/libapp.a(button.c.obj)
 .ARM.attributes
                0x00000000000000d8       0x36 zephyr/libzephyr.a(heap.c.obj)
 .ARM.attributes
                0x000000000000010e       0x36 zephyr/libzephyr.a(cbprintf_packaged.c.obj)
 .ARM.attributes
                0x0000000000000144       0x36 zephyr/libzephyr.a(printk.c.obj)
 .ARM.attributes
                0x000000000000017a       0x36 zephyr/libzephyr.a(sem.c.obj)
 .ARM.attributes
                0x00000000000001b0       0x36 zephyr/libzephyr.a(thread_entry.c.obj)
 .ARM.attributes
                0x00000000000001e6       0x36 zephyr/libzephyr.a(cbprintf_complete.c.obj)
 .ARM.attributes
                0x000000000000021c       0x36 zephyr/libzephyr.a(assert.c.obj)
 .ARM.attributes
                0x0000000000000252       0x36 zephyr/libzephyr.a(mpsc_pbuf.c.obj)
 .ARM.attributes
                0x0000000000000288       0x36 zephyr/libzephyr.a(dec.c.obj)
 .ARM.attributes
                0x00000000000002be       0x36 zephyr/libzephyr.a(hex.c.obj)
 .ARM.attributes
                0x00000000000002f4       0x36 zephyr/libzephyr.a(rb.c.obj)
 .ARM.attributes
                0x000000000000032a       0x36 zephyr/libzephyr.a(timeutil.c.obj)
 .ARM.attributes
                0x0000000000000360       0x36 zephyr/libzephyr.a(bitarray.c.obj)
 .ARM.attributes
                0x0000000000000396       0x36 zephyr/libzephyr.a(onoff.c.obj)
 .ARM.attributes
                0x00000000000003cc       0x36 zephyr/libzephyr.a(notify.c.obj)
 .ARM.attributes
                0x0000000000000402       0x36 zephyr/libzephyr.a(last_section_id.c.obj)
 .ARM.attributes
                0x0000000000000438       0x36 zephyr/libzephyr.a(configs.c.obj)
 .ARM.attributes
                0x000000000000046e       0x36 zephyr/libzephyr.a(log_core.c.obj)
 .ARM.attributes
                0x00000000000004a4       0x36 zephyr/libzephyr.a(log_mgmt.c.obj)
 .ARM.attributes
                0x00000000000004da       0x36 zephyr/libzephyr.a(log_cache.c.obj)
 .ARM.attributes
                0x0000000000000510       0x36 zephyr/libzephyr.a(log_msg.c.obj)
 .ARM.attributes
                0x0000000000000546       0x36 zephyr/libzephyr.a(log_output.c.obj)
 .ARM.attributes
                0x000000000000057c       0x36 zephyr/libzephyr.a(log_backend_uart.c.obj)
 .ARM.attributes
                0x00000000000005b2       0x36 zephyr/libzephyr.a(mem_attr.c.obj)
 .ARM.attributes
                0x00000000000005e8       0x36 zephyr/libzephyr.a(tracing_none.c.obj)
 .ARM.attributes
                0x000000000000061e       0x36 zephyr/libzephyr.a(banner.c.obj)
 .ARM.attributes
                0x0000000000000654       0x36 zephyr/libzephyr.a(flash_map_partition_manager.c.obj)
 .ARM.attributes
                0x000000000000068a       0x36 zephyr/libzephyr.a(nrf_cc3xx_platform_abort_zephyr.c.obj)
 .ARM.attributes
                0x00000000000006c0       0x36 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
 .ARM.attributes
                0x00000000000006f6       0x36 zephyr/arch/common/libarch__common.a(sw_isr_common.c.obj)
 .ARM.attributes
                0x000000000000072c       0x36 zephyr/arch/arch/arm/core/libarch__arm__core.a(fatal.c.obj)
 .ARM.attributes
                0x0000000000000762       0x36 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi.c.obj)
 .ARM.attributes
                0x0000000000000798       0x22 zephyr/arch/arch/arm/core/libarch__arm__core.a(nmi_on_reset.S.obj)
 .ARM.attributes
                0x00000000000007ba       0x36 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
 .ARM.attributes
                0x00000000000007f0       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(exc_exit.c.obj)
 .ARM.attributes
                0x0000000000000826       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault.c.obj)
 .ARM.attributes
                0x000000000000085c       0x22 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fault_s.S.obj)
 .ARM.attributes
                0x000000000000087e       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(fpu.c.obj)
 .ARM.attributes
                0x00000000000008b4       0x22 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(reset.S.obj)
 .ARM.attributes
                0x00000000000008d6       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(scb.c.obj)
 .ARM.attributes
                0x000000000000090c       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread_abort.c.obj)
 .ARM.attributes
                0x0000000000000942       0x24 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(vector_table.S.obj)
 .ARM.attributes
                0x0000000000000966       0x22 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(swap_helper.S.obj)
 .ARM.attributes
                0x0000000000000988       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_manage.c.obj)
 .ARM.attributes
                0x00000000000009be       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(prep_c.c.obj)
 .ARM.attributes
                0x00000000000009f4       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(thread.c.obj)
 .ARM.attributes
                0x0000000000000a2a       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(cpu_idle.c.obj)
 .ARM.attributes
                0x0000000000000a60       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(irq_init.c.obj)
 .ARM.attributes
                0x0000000000000a96       0x36 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(isr_wrapper.c.obj)
 .ARM.attributes
                0x0000000000000acc       0x22 zephyr/arch/arch/arm/core/cortex_m/libarch__arm__core__cortex_m.a(__aeabi_read_tp.S.obj)
 .ARM.attributes
                0x0000000000000aee       0x36 zephyr/arch/arch/arm/core/cortex_m/cmse/libarch__arm__core__cortex_m__cmse.a(arm_core_cmse.c.obj)
 .ARM.attributes
                0x0000000000000b24       0x36 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_core_mpu.c.obj)
 .ARM.attributes
                0x0000000000000b5a       0x36 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .ARM.attributes
                0x0000000000000b90       0x36 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu_regions.c.obj)
 .ARM.attributes
                0x0000000000000bc6       0x36 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(assert.c.obj)
 .ARM.attributes
                0x0000000000000bfc       0x36 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(cbprintf.c.obj)
 .ARM.attributes
                0x0000000000000c32       0x36 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(chk_fail.c.obj)
 .ARM.attributes
                0x0000000000000c68       0x36 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(errno_wrap.c.obj)
 .ARM.attributes
                0x0000000000000c9e       0x36 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(exit.c.obj)
 .ARM.attributes
                0x0000000000000cd4       0x36 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(locks.c.obj)
 .ARM.attributes
                0x0000000000000d0a       0x36 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .ARM.attributes
                0x0000000000000d40       0x36 zephyr/lib/libc/common/liblib__libc__common.a(abort.c.obj)
 .ARM.attributes
                0x0000000000000d76       0x36 zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .ARM.attributes
                0x0000000000000dac       0x36 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_base_addresses.c.obj)
 .ARM.attributes
                0x0000000000000de2       0x36 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_binding_headers.c.obj)
 .ARM.attributes
                0x0000000000000e18       0x36 zephyr/soc/soc/nrf5340/libsoc__nordic.a(validate_enabled_instances.c.obj)
 .ARM.attributes
                0x0000000000000e4e       0x36 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .ARM.attributes
                0x0000000000000e84       0x36 zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .ARM.attributes
                0x0000000000000eba       0x36 zephyr/soc/soc/nrf5340/libsoc__nordic.a(reboot.c.obj)
 .ARM.attributes
                0x0000000000000ef0       0x36 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .ARM.attributes
                0x0000000000000f26       0x36 zephyr/drivers/console/libdrivers__console.a(uart_console.c.obj)
 .ARM.attributes
                0x0000000000000f5c       0x36 zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .ARM.attributes
                0x0000000000000f92       0x36 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_common.c.obj)
 .ARM.attributes
                0x0000000000000fc8       0x36 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .ARM.attributes
                0x0000000000000ffe       0x36 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 .ARM.attributes
                0x0000000000001034       0x36 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(common.c.obj)
 .ARM.attributes
                0x000000000000106a       0x36 zephyr/drivers/pinctrl/libdrivers__pinctrl.a(pinctrl_nrf.c.obj)
 .ARM.attributes
                0x00000000000010a0       0x36 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .ARM.attributes
                0x00000000000010d6       0x36 zephyr/drivers/timer/libdrivers__timer.a(sys_clock_init.c.obj)
 .ARM.attributes
                0x000000000000110c       0x36 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .ARM.attributes
                0x0000000000001142       0x36 modules/nrf/drivers/hw_cc3xx/lib..__nrf__drivers__hw_cc3xx.a(hw_cc3xx.c.obj)
 .ARM.attributes
                0x0000000000001178       0x36 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(system_nrf5340_application.c.obj)
 .ARM.attributes
                0x00000000000011ae       0x36 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_glue.c.obj)
 .ARM.attributes
                0x00000000000011e4       0x36 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_flag32_allocator.c.obj)
 .ARM.attributes
                0x000000000000121a       0x36 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_ram_ctrl.c.obj)
 .ARM.attributes
                0x0000000000001250       0x36 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_dppi.c.obj)
 .ARM.attributes
                0x0000000000001286       0x36 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gppi_ppi.c.obj)
 .ARM.attributes
                0x00000000000012bc       0x36 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .ARM.attributes
                0x00000000000012f2       0x36 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_dppi.c.obj)
 .ARM.attributes
                0x0000000000001328       0x36 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_gpiote.c.obj)
 .ARM.attributes
                0x000000000000135e       0x36 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .ARM.attributes
                0x0000000000001394       0x36 zephyr/kernel/libkernel.a(busy_wait.c.obj)
 .ARM.attributes
                0x00000000000013ca       0x36 zephyr/kernel/libkernel.a(device.c.obj)
 .ARM.attributes
                0x0000000000001400       0x36 zephyr/kernel/libkernel.a(fatal.c.obj)
 .ARM.attributes
                0x0000000000001436       0x36 zephyr/kernel/libkernel.a(init.c.obj)
 .ARM.attributes
                0x000000000000146c       0x36 zephyr/kernel/libkernel.a(init_static.c.obj)
 .ARM.attributes
                0x00000000000014a2       0x36 zephyr/kernel/libkernel.a(mem_slab.c.obj)
 .ARM.attributes
                0x00000000000014d8       0x36 zephyr/kernel/libkernel.a(idle.c.obj)
 .ARM.attributes
                0x000000000000150e       0x36 zephyr/kernel/libkernel.a(msg_q.c.obj)
 .ARM.attributes
                0x0000000000001544       0x36 zephyr/kernel/libkernel.a(mutex.c.obj)
 .ARM.attributes
                0x000000000000157a       0x36 zephyr/kernel/libkernel.a(sem.c.obj)
 .ARM.attributes
                0x00000000000015b0       0x36 zephyr/kernel/libkernel.a(thread.c.obj)
 .ARM.attributes
                0x00000000000015e6       0x36 zephyr/kernel/libkernel.a(sched.c.obj)
 .ARM.attributes
                0x000000000000161c       0x36 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .ARM.attributes
                0x0000000000001652       0x36 zephyr/kernel/libkernel.a(xip.c.obj)
 .ARM.attributes
                0x0000000000001688       0x36 zephyr/kernel/libkernel.a(timeout.c.obj)
 .ARM.attributes
                0x00000000000016be       0x36 zephyr/kernel/libkernel.a(timer.c.obj)
 .ARM.attributes
                0x00000000000016f4       0x36 zephyr/kernel/libkernel.a(mempool.c.obj)
 .ARM.attributes
                0x000000000000172a       0x36 zephyr/kernel/libkernel.a(kheap.c.obj)
 .ARM.attributes
                0x0000000000001760       0x36 zephyr/arch/common/libisr_tables.a(isr_tables.c.obj)
 .ARM.attributes
                0x0000000000001796       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .ARM.attributes
                0x00000000000017ca       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_abort.c.obj)
 .ARM.attributes
                0x00000000000017fe       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .ARM.attributes
                0x0000000000001832       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_lib.c.obj)
 .ARM.attributes
                0x0000000000001866       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_hal.c.obj)
 .ARM.attributes
                0x000000000000189a       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal.c.obj)
 .ARM.attributes
                0x00000000000018ce       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_dma.c.obj)
 .ARM.attributes
                0x0000000000001902       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_interrupt_ctrl.c.obj)
 .ARM.attributes
                0x0000000000001936       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mem.c.obj)
 .ARM.attributes
                0x000000000000196a       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_mutex.c.obj)
 .ARM.attributes
                0x000000000000199e       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .ARM.attributes
                0x00000000000019d2       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
 .ARM.attributes
                0x0000000000001a06       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_hmac_drbg.c.obj)
 .ARM.attributes
                0x0000000000001a3a       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_common.c.obj)
 .ARM.attributes
                0x0000000000001a6e       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd_trng90b.c.obj)
 .ARM.attributes
                0x0000000000001aa2       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(ctr_drbg.c.obj)
 .ARM.attributes
                0x0000000000001ad6       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(custom_entropy.c.obj)
 .ARM.attributes
                0x0000000000001b0a       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hmac_drbg_alt.c.obj)
 .ARM.attributes
                0x0000000000001b3e       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_rng_plat.c.obj)
 .ARM.attributes
                0x0000000000001b72       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_trng.c.obj)
 .ARM.attributes
                0x0000000000001ba6       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(threading_alt.c.obj)
 .ARM.attributes
                0x0000000000001bda       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(llf_rnd.c.obj)
 .ARM.attributes
                0x0000000000001c0e       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(trng_api.c.obj)
 .ARM.attributes
                0x0000000000001c42       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_alt.c.obj)
 .ARM.attributes
                0x0000000000001c76       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256_alt.c.obj)
 .ARM.attributes
                0x0000000000001caa       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(mbedtls_hash_common.c.obj)
 .ARM.attributes
                0x0000000000001cde       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(sha256.c.obj)
 .ARM.attributes
                0x0000000000001d12       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(driver_common.c.obj)
 .ARM.attributes
                0x0000000000001d46       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(hash_driver.c.obj)
 .ARM.attributes
                0x0000000000001d7a       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(aes_driver.c.obj)
 .ARM.attributes
                0x0000000000001dae       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(kmu_shared.c.obj)
 .ARM.attributes
                0x0000000000001de2       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_platform_keys.c.obj)
 .ARM.attributes
                0x0000000000001e16       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_util_cmac.c.obj)
 .ARM.attributes
                0x0000000000001e4a       0x34 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_buff_attr.c.obj)
 .ARM.attributes
                0x0000000000001e7e       0x20 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strcmp.S.o)
 .ARM.attributes
                0x0000000000001e9e       0x36 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memcpy.c.o)
 .ARM.attributes
                0x0000000000001ed4       0x36 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memmove.c.o)
 .ARM.attributes
                0x0000000000001f0a       0x36 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(memset.c.o)
 .ARM.attributes
                0x0000000000001f40       0x20 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(strlen.S.o)
 .ARM.attributes
                0x0000000000001f60       0x36 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_errno_errno.c.o)
 .ARM.attributes
                0x0000000000001f96       0x36 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_memcmp.c.o)
 .ARM.attributes
                0x0000000000001fcc       0x36 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strnlen.c.o)
 .ARM.attributes
                0x0000000000002002       0x36 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_puts.c.o)
 .ARM.attributes
                0x0000000000002038       0x36 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_snprintf.c.o)
 .ARM.attributes
                0x000000000000206e       0x36 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflprintf.c.o)
 .ARM.attributes
                0x00000000000020a4       0x36 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_vflscanf.c.o)
 .ARM.attributes
                0x00000000000020da       0x36 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_string_strchr.c.o)
 .ARM.attributes
                0x0000000000002110       0x36 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_fgetc.c.o)
 .ARM.attributes
                0x0000000000002146       0x36 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_filestrput.c.o)
 .ARM.attributes
                0x000000000000217c       0x36 c:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk\arm-zephyr-eabi\bin\../lib/gcc/../../picolibc/arm-zephyr-eabi/lib/thumb/v8-m.main/nofp\libc.a(libc_tinystdio_ungetc.c.o)
 .ARM.attributes
                0x00000000000021b2       0x32 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(cmse.o)
 .ARM.attributes
                0x00000000000021e4       0x22 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldf3.o)
 .ARM.attributes
                0x0000000000002206       0x22 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_addsubdf3.o)
 .ARM.attributes
                0x0000000000002228       0x22 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_muldivdf3.o)
 .ARM.attributes
                0x000000000000224a       0x22 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_truncdfsf2.o)
 .ARM.attributes
                0x000000000000226c       0x22 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpsf2.o)
 .ARM.attributes
                0x000000000000228e       0x22 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_ldivmod.o)
 .ARM.attributes
                0x00000000000022b0       0x22 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_aeabi_uldivmod.o)
 .ARM.attributes
                0x00000000000022d2       0x32 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_popcountsi2.o)
 .ARM.attributes
                0x0000000000002304       0x32 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixdfdi.o)
 .ARM.attributes
                0x0000000000002336       0x32 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_fixunsdfdi.o)
 .ARM.attributes
                0x0000000000002368       0x32 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_udivmoddi4.o)
 .ARM.attributes
                0x000000000000239a       0x22 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_dvmd_tls.o)
 .ARM.attributes
                0x00000000000023bc       0x22 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_cmpdf2.o)
 .ARM.attributes
                0x00000000000023de       0x22 c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/../lib/gcc/arm-zephyr-eabi/12.2.0/thumb/v8-m.main/nofp\libgcc.a(_arm_fixunsdfsi.o)
 *(SORT_BY_ALIGNMENT(.gnu.attributes))

.last_section   0x000000000000a194        0x4
 *(SORT_BY_ALIGNMENT(.last_section))
 .last_section  0x000000000000a194        0x4 zephyr/libzephyr.a(last_section_id.c.obj)
                0x000000000000a198                _flash_used = ((LOADADDR (.last_section) + SIZEOF (.last_section)) - __rom_region_start)

bss             0x00000000200002f8      0xfa2
                0x00000000200002f8                . = ALIGN (0x4)
                0x00000000200002f8                __bss_start = .
                0x00000000200002f8                __kernel_ram_start = .
 *(SORT_BY_ALIGNMENT(.bss))
 *(SORT_BY_ALIGNMENT(.bss.*))
 .bss.logging_thread
                0x00000000200002f8       0x80 zephyr/libzephyr.a(log_core.c.obj)
                0x00000000200002f8                logging_thread
 .bss.buf32     0x0000000020000378      0x400 zephyr/libzephyr.a(log_core.c.obj)
 .bss.last_failure_report
                0x0000000020000778        0x8 zephyr/libzephyr.a(log_core.c.obj)
 .bss.log_process_thread_timer
                0x0000000020000780       0x38 zephyr/libzephyr.a(log_core.c.obj)
 .bss.cc_data   0x00000000200007b8       0x10 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.last_count
                0x00000000200007c8        0x8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.anchor    0x00000000200007d0        0x8 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.z_idle_threads
                0x00000000200007d8       0x80 zephyr/kernel/libkernel.a(init.c.obj)
                0x00000000200007d8                z_idle_threads
 .bss.z_main_thread
                0x0000000020000858       0x80 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000020000858                z_main_thread
 .bss._thread_dummy
                0x00000000200008d8       0x80 zephyr/kernel/libkernel.a(sched.c.obj)
                0x00000000200008d8                _thread_dummy
 .bss.slice_timeouts
                0x0000000020000958       0x18 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .bss.curr_tick
                0x0000000020000970        0x8 zephyr/kernel/libkernel.a(timeout.c.obj)
 .bss.button_cb_data
                0x0000000020000978        0xc app/libapp.a(button.c.obj)
 .bss.curr_log_buffer
                0x0000000020000984        0x4 zephyr/libzephyr.a(log_core.c.obj)
 .bss.proc_tid  0x0000000020000988        0x4 zephyr/libzephyr.a(log_core.c.obj)
 .bss.dropped_cnt
                0x000000002000098c        0x4 zephyr/libzephyr.a(log_core.c.obj)
 .bss.buffered_cnt
                0x0000000020000990        0x4 zephyr/libzephyr.a(log_core.c.obj)
 .bss.initialized
                0x0000000020000994        0x4 zephyr/libzephyr.a(log_core.c.obj)
 .bss.timestamp_div
                0x0000000020000998        0x4 zephyr/libzephyr.a(log_output.c.obj)
 .bss.freq      0x000000002000099c        0x4 zephyr/libzephyr.a(log_output.c.obj)
 .bss.lbu_data  0x00000000200009a0       0x18 zephyr/libzephyr.a(log_backend_uart.c.obj)
 .bss.lbu_output_control_block
                0x00000000200009b8        0xc zephyr/libzephyr.a(log_backend_uart.c.obj)
 .bss.mutex_slab_buffer
                0x00000000200009c4      0x500 zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x00000000200009c4                mutex_slab_buffer
 .bss.mutex_slab
                0x0000000020000ec4       0x1c zephyr/libzephyr.a(nrf_cc3xx_platform_mutex_zephyr.c.obj)
                0x0000000020000ec4                mutex_slab
 .bss.z_arm_tls_ptr
                0x0000000020000ee0        0x4 zephyr/arch/arch/arm/core/libarch__arm__core.a(tls.c.obj)
                0x0000000020000ee0                z_arm_tls_ptr
 .bss.dyn_reg_info
                0x0000000020000ee4       0x14 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .bss._stdout_hook
                0x0000000020000ef8        0x4 zephyr/lib/libc/picolibc/liblib__libc__picolibc.a(stdio.c.obj)
 .bss.z_malloc_heap
                0x0000000020000efc        0xc zephyr/lib/libc/common/liblib__libc__common.a(malloc.c.obj)
 .bss.timestamps.2
                0x0000000020000f08       0x14 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .bss.cpunet_mgr
                0x0000000020000f1c       0x1c zephyr/soc/soc/nrf5340/libsoc__nordic.a(nrf53_cpunet_mgmt.c.obj)
 .bss.cli.1     0x0000000020000f38       0x10 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .bss.on.2      0x0000000020000f48        0x4 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .bss.data      0x0000000020000f4c       0xa0 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .bss.hfclk_users
                0x0000000020000fec        0x4 zephyr/drivers/clock_control/libdrivers__clock_control.a(clock_control_nrf.c.obj)
 .bss.gpio_nrfx_p1_data
                0x0000000020000ff0        0xc zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .bss.gpio_nrfx_p0_data
                0x0000000020000ffc        0xc zephyr/drivers/gpio/libdrivers__gpio.a(gpio_nrfx.c.obj)
 .bss.rx_msgs0  0x0000000020001008       0x20 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .bss.tx_msgs0  0x0000000020001028       0x20 zephyr/drivers/i2s/libdrivers__i2s.a(i2s_nrfx.c.obj)
 .bss.uarte_0_data
                0x0000000020001048        0xc zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .bss.force_isr_mask
                0x0000000020001054        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.int_mask  0x0000000020001058        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.overflow_cnt
                0x000000002000105c        0x4 zephyr/drivers/timer/libdrivers__timer.a(nrf_rtc_timer.c.obj)
 .bss.m_clock_cb
                0x0000000020001060        0x8 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_clock.c.obj)
 .bss.m_cb      0x0000000020001068       0x20 modules/hal_nordic/modules/hal_nordic/nrfx/libmodules__hal_nordic__nrfx.a(nrfx_i2s.c.obj)
 .bss._kernel   0x0000000020001088       0x20 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000020001088                _kernel
 .bss.pending_current
                0x00000000200010a8        0x4 zephyr/kernel/libkernel.a(timeslicing.c.obj)
                0x00000000200010a8                pending_current
 .bss.slice_max_prio
                0x00000000200010ac        0x4 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .bss.slice_ticks
                0x00000000200010b0        0x4 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 .bss.announce_remaining
                0x00000000200010b4        0x4 zephyr/kernel/libkernel.a(timeout.c.obj)
 .bss.nrf_cc3xx_platform_initialized
                0x00000000200010b8        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform.c.obj)
 .bss.power_mutex_int
                0x00000000200010bc        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss.rng_mutex_int
                0x00000000200010c0        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss.asym_mutex_int
                0x00000000200010c4        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss.sym_mutex_int
                0x00000000200010c8        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_mutex.c.obj)
 .bss.use_count
                0x00000000200010cc        0x4 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(cc_pal_pm.c.obj)
 .bss.nrf_cc3xx_platform_ctr_drbg_global_ctx
                0x00000000200010d0      0x1c0 C:/ncs/v3.0.2/nrfxlib/crypto/nrf_cc312_platform/lib/cortex-m33/soft-float/no-interrupts/libnrf_cc312_platform_0.9.19.a(nrf_cc3xx_platform_ctr_drbg.c.obj)
                0x00000000200010d0                nrf_cc3xx_platform_ctr_drbg_global_ctx
 .bss.backend_attached
                0x0000000020001290        0x1 zephyr/libzephyr.a(log_core.c.obj)
 .bss.panic_mode
                0x0000000020001291        0x1 zephyr/libzephyr.a(log_core.c.obj)
 .bss.lbu_buffer
                0x0000000020001292        0x1 zephyr/libzephyr.a(log_backend_uart.c.obj)
 .bss.static_regions_num
                0x0000000020001293        0x1 zephyr/arch/arch/arm/core/mpu/libarch__arm__core__mpu.a(arm_mpu.c.obj)
 .bss.timestamps_filled.3
                0x0000000020001294        0x1 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .bss.current.4
                0x0000000020001295        0x1 zephyr/soc/soc/nrf5340/libsoc__nordic.a(soc.c.obj)
 .bss.uarte0_poll_in_byte
                0x0000000020001296        0x1 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .bss.uarte0_poll_out_byte
                0x0000000020001297        0x1 zephyr/drivers/serial/libdrivers__serial.a(uart_nrfx_uarte.c.obj)
 .bss.z_sys_post_kernel
                0x0000000020001298        0x1 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000020001298                z_sys_post_kernel
 .bss.lock      0x0000000020001299        0x0 zephyr/kernel/libkernel.a(mutex.c.obj)
 .bss.lock      0x0000000020001299        0x0 zephyr/kernel/libkernel.a(sem.c.obj)
 .bss._sched_spinlock
                0x0000000020001299        0x0 zephyr/kernel/libkernel.a(sched.c.obj)
                0x0000000020001299                _sched_spinlock
 .bss.slice_expired
                0x0000000020001299        0x1 zephyr/kernel/libkernel.a(timeslicing.c.obj)
 *(SORT_BY_ALIGNMENT(COMMON))
 *(SORT_BY_ALIGNMENT(.kernel_bss.*))
                0x000000002000129c                __bss_end = ALIGN (0x4)

noinit          0x00000000200012a0     0x15c8
 *(SORT_BY_ALIGNMENT(.noinit))
 *(SORT_BY_ALIGNMENT(.noinit.*))
 .noinit."WEST_TOPDIR/zephyr/subsys/logging/log_core.c".0
                0x00000000200012a0      0x300 zephyr/libzephyr.a(log_core.c.obj)
                0x00000000200012a0                logging_stack
 .noinit."WEST_TOPDIR/zephyr/kernel/init.c".2
                0x00000000200015a0      0x800 zephyr/kernel/libkernel.a(init.c.obj)
                0x00000000200015a0                z_interrupt_stacks
 .noinit."WEST_TOPDIR/zephyr/kernel/init.c".1
                0x0000000020001da0      0x140 zephyr/kernel/libkernel.a(init.c.obj)
 .noinit."WEST_TOPDIR/zephyr/kernel/init.c".0
                0x0000000020001ee0      0x400 zephyr/kernel/libkernel.a(init.c.obj)
                0x0000000020001ee0                z_main_stack
 .noinit."WEST_TOPDIR/zephyr/drivers/led_strip/ws2812_i2s.c".k_mem_slab_buf_ws2812_i2s_0_slab
                0x00000000200022e0      0x588 zephyr/drivers/led_strip/libdrivers__led_strip.a(ws2812_i2s.c.obj)
 *(SORT_BY_ALIGNMENT(.kernel_noinit.*))
                0x0000000020070000                __kernel_ram_end = 0x20070000
                0x000000000006fd08                __kernel_ram_size = (__kernel_ram_end - __kernel_ram_start)

.last_ram_section
                0x0000000020002868        0x0
                0x0000000020002868                _image_ram_end = .
                0x0000000000002868                _image_ram_size = (_image_ram_end - _image_ram_start)
                0x0000000020002868                _end = .
                0x0000000020002868                z_mapped_end = .
OUTPUT(zephyr\zephyr_pre0.elf elf32-littlearm)
LOAD linker stubs
