/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_GNSS_H
#define Z_INCLUDE_SYSCALLS_GNSS_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_gnss_set_fix_rate(const struct device * dev, uint32_t fix_interval_ms);

__pinned_func
static inline int gnss_set_fix_rate(const struct device * dev, uint32_t fix_interval_ms)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = fix_interval_ms };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_GNSS_SET_FIX_RATE);
	}
#endif
	compiler_barrier();
	return z_impl_gnss_set_fix_rate(dev, fix_interval_ms);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define gnss_set_fix_rate(dev, fix_interval_ms) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_GNSS_SET_FIX_RATE, gnss_set_fix_rate, dev, fix_interval_ms); 	syscall__retval = gnss_set_fix_rate(dev, fix_interval_ms); 	sys_port_trace_syscall_exit(K_SYSCALL_GNSS_SET_FIX_RATE, gnss_set_fix_rate, dev, fix_interval_ms, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_gnss_get_fix_rate(const struct device * dev, uint32_t * fix_interval_ms);

__pinned_func
static inline int gnss_get_fix_rate(const struct device * dev, uint32_t * fix_interval_ms)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t * val; } parm1 = { .val = fix_interval_ms };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_GNSS_GET_FIX_RATE);
	}
#endif
	compiler_barrier();
	return z_impl_gnss_get_fix_rate(dev, fix_interval_ms);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define gnss_get_fix_rate(dev, fix_interval_ms) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_GNSS_GET_FIX_RATE, gnss_get_fix_rate, dev, fix_interval_ms); 	syscall__retval = gnss_get_fix_rate(dev, fix_interval_ms); 	sys_port_trace_syscall_exit(K_SYSCALL_GNSS_GET_FIX_RATE, gnss_get_fix_rate, dev, fix_interval_ms, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_gnss_set_navigation_mode(const struct device * dev, enum gnss_navigation_mode mode);

__pinned_func
static inline int gnss_set_navigation_mode(const struct device * dev, enum gnss_navigation_mode mode)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; enum gnss_navigation_mode val; } parm1 = { .val = mode };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_GNSS_SET_NAVIGATION_MODE);
	}
#endif
	compiler_barrier();
	return z_impl_gnss_set_navigation_mode(dev, mode);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define gnss_set_navigation_mode(dev, mode) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_GNSS_SET_NAVIGATION_MODE, gnss_set_navigation_mode, dev, mode); 	syscall__retval = gnss_set_navigation_mode(dev, mode); 	sys_port_trace_syscall_exit(K_SYSCALL_GNSS_SET_NAVIGATION_MODE, gnss_set_navigation_mode, dev, mode, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_gnss_get_navigation_mode(const struct device * dev, enum gnss_navigation_mode * mode);

__pinned_func
static inline int gnss_get_navigation_mode(const struct device * dev, enum gnss_navigation_mode * mode)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; enum gnss_navigation_mode * val; } parm1 = { .val = mode };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_GNSS_GET_NAVIGATION_MODE);
	}
#endif
	compiler_barrier();
	return z_impl_gnss_get_navigation_mode(dev, mode);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define gnss_get_navigation_mode(dev, mode) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_GNSS_GET_NAVIGATION_MODE, gnss_get_navigation_mode, dev, mode); 	syscall__retval = gnss_get_navigation_mode(dev, mode); 	sys_port_trace_syscall_exit(K_SYSCALL_GNSS_GET_NAVIGATION_MODE, gnss_get_navigation_mode, dev, mode, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_gnss_set_enabled_systems(const struct device * dev, gnss_systems_t systems);

__pinned_func
static inline int gnss_set_enabled_systems(const struct device * dev, gnss_systems_t systems)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; gnss_systems_t val; } parm1 = { .val = systems };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_GNSS_SET_ENABLED_SYSTEMS);
	}
#endif
	compiler_barrier();
	return z_impl_gnss_set_enabled_systems(dev, systems);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define gnss_set_enabled_systems(dev, systems) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_GNSS_SET_ENABLED_SYSTEMS, gnss_set_enabled_systems, dev, systems); 	syscall__retval = gnss_set_enabled_systems(dev, systems); 	sys_port_trace_syscall_exit(K_SYSCALL_GNSS_SET_ENABLED_SYSTEMS, gnss_set_enabled_systems, dev, systems, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_gnss_get_enabled_systems(const struct device * dev, gnss_systems_t * systems);

__pinned_func
static inline int gnss_get_enabled_systems(const struct device * dev, gnss_systems_t * systems)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; gnss_systems_t * val; } parm1 = { .val = systems };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_GNSS_GET_ENABLED_SYSTEMS);
	}
#endif
	compiler_barrier();
	return z_impl_gnss_get_enabled_systems(dev, systems);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define gnss_get_enabled_systems(dev, systems) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_GNSS_GET_ENABLED_SYSTEMS, gnss_get_enabled_systems, dev, systems); 	syscall__retval = gnss_get_enabled_systems(dev, systems); 	sys_port_trace_syscall_exit(K_SYSCALL_GNSS_GET_ENABLED_SYSTEMS, gnss_get_enabled_systems, dev, systems, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_gnss_get_supported_systems(const struct device * dev, gnss_systems_t * systems);

__pinned_func
static inline int gnss_get_supported_systems(const struct device * dev, gnss_systems_t * systems)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; gnss_systems_t * val; } parm1 = { .val = systems };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_GNSS_GET_SUPPORTED_SYSTEMS);
	}
#endif
	compiler_barrier();
	return z_impl_gnss_get_supported_systems(dev, systems);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define gnss_get_supported_systems(dev, systems) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_GNSS_GET_SUPPORTED_SYSTEMS, gnss_get_supported_systems, dev, systems); 	syscall__retval = gnss_get_supported_systems(dev, systems); 	sys_port_trace_syscall_exit(K_SYSCALL_GNSS_GET_SUPPORTED_SYSTEMS, gnss_get_supported_systems, dev, systems, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_gnss_get_latest_timepulse(const struct device * dev, k_ticks_t * timestamp);

__pinned_func
static inline int gnss_get_latest_timepulse(const struct device * dev, k_ticks_t * timestamp)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; k_ticks_t * val; } parm1 = { .val = timestamp };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_GNSS_GET_LATEST_TIMEPULSE);
	}
#endif
	compiler_barrier();
	return z_impl_gnss_get_latest_timepulse(dev, timestamp);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define gnss_get_latest_timepulse(dev, timestamp) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_GNSS_GET_LATEST_TIMEPULSE, gnss_get_latest_timepulse, dev, timestamp); 	syscall__retval = gnss_get_latest_timepulse(dev, timestamp); 	sys_port_trace_syscall_exit(K_SYSCALL_GNSS_GET_LATEST_TIMEPULSE, gnss_get_latest_timepulse, dev, timestamp, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
