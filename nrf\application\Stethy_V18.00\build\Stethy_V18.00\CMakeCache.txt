# This is the CMakeCache file.
# For build in directory: c:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00
# It was generated by CMake: C:/ncs/toolchains/0b393f9e1b/opt/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Application Binary Directory
APPLICATION_BINARY_DIR:PATH=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00

//The application configuration folder
APPLICATION_CONFIG_DIR:PATH=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00

//Application Source Directory
APPLICATION_SOURCE_DIR:PATH=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00

//Selected board
BOARD:STRING=nrf5340dk/nrf5340/cpuapp

//Main board directory for board (nrf5340dk)
BOARD_DIR:PATH=C:/ncs/v3.0.2/zephyr/boards/nordic/nrf5340dk

//Support board extensions
BOARD_EXTENSIONS:BOOL=ON

//Path to a program.
BOSSAC:FILEPATH=BOSSAC-NOTFOUND

//Kernel binary file
BYPRODUCT_KERNEL_BIN_NAME:FILEPATH=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/zephyr.bin

//Kernel elf file
BYPRODUCT_KERNEL_ELF_NAME:FILEPATH=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/zephyr.elf

//Kernel hex file
BYPRODUCT_KERNEL_HEX_NAME:FILEPATH=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/zephyr.hex

//Selected board
CACHED_BOARD:STRING=nrf5340dk/nrf5340/cpuapp

//Selected shield
CACHED_SHIELD:STRING=

//Selected snippet
CACHED_SNIPPET:STRING=

//Path to a program.
CCACHE_FOUND:FILEPATH=CCACHE_FOUND-NOTFOUND

//Path to a program.
CCACHE_PROGRAM:FILEPATH=CCACHE_PROGRAM-NOTFOUND

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-addr2line.exe

//Path to a program.
CMAKE_AR:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-ar.exe

//Path to a program.
CMAKE_AS:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-as.exe

//ASM compiler
CMAKE_ASM_COMPILER:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc.exe

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_ASM_COMPILER_AR:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar.exe

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_ASM_COMPILER_RANLIB:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib.exe

//Flags used by the ASM compiler during all build types.
CMAKE_ASM_FLAGS:STRING=

//Flags used by the ASM compiler during DEBUG builds.
CMAKE_ASM_FLAGS_DEBUG:STRING=-g

//Flags used by the ASM compiler during MINSIZEREL builds.
CMAKE_ASM_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the ASM compiler during RELEASE builds.
CMAKE_ASM_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the ASM compiler during RELWITHDEBINFO builds.
CMAKE_ASM_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=

//CXX compiler
CMAKE_CXX_COMPILER:STRING=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc.exe

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_AR:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar.exe

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib.exe

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:STRING=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc.exe

//A wrapper around 'ar' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_AR:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ar.exe

//A wrapper around 'ranlib' adding the appropriate '--plugin' option
// for the GCC compiler
CMAKE_C_COMPILER_RANLIB:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcc-ranlib.exe

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-g

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=C:/ProgramData/chocolatey/bin/dlltool.exe

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Export CMake compile commands. Used by gen_app_partitions.py
// script
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=TRUE

//Path to a program.
CMAKE_GCOV:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gcov.exe

//Path to a program.
CMAKE_GDB:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gdb.exe

//Path to a program.
CMAKE_GDB_NO_PY:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gdb.exe

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/Zephyr-Kernel

//Program used to build from build.ninja files.
CMAKE_MAKE_PROGRAM:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/bin/ninja.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-nm.exe

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-objcopy.exe

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-objdump.exe

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=Stethy_V18.00

//Value Computed by CMake
CMAKE_PROJECT_VERSION:STATIC=4.0.99

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MAJOR:STATIC=4

//Value Computed by CMake
CMAKE_PROJECT_VERSION_MINOR:STATIC=0

//Value Computed by CMake
CMAKE_PROJECT_VERSION_PATCH:STATIC=99

//Value Computed by CMake
CMAKE_PROJECT_VERSION_TWEAK:STATIC=

//Path to a program.
CMAKE_RANLIB:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-ranlib.exe

//Path to a program.
CMAKE_READELF:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-readelf.exe

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-strip.exe

//No help, variable specified on the command line.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//If desired, you can build the application usingthe configuration
// settings specified in an alternate .conf file using this parameter.
// These settings will override the settings in the application’s
// .config file or its default .conf file.Multiple files may be
// listed, e.g. CONF_FILE="prj1.conf;prj2.conf" The CACHED_CONF_FILE
// is internal Zephyr variable used between CMake runs. To change
// CONF_FILE, use the CONF_FILE variable.
CONF_FILE:STRING=prj.conf

//Path to a program.
DTC:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/bin/dtc.exe

//If desired, you can build the application using the DT configuration
// settings specified in an alternate .overlay file using this
// parameter. These settings will override the settings in the
// board's .dts file. Multiple files may be listed, e.g. DTC_OVERLAY_FILE="dts1.overlay
// dts2.overlay"
DTC_OVERLAY_FILE:STRING=boards/nrf5340dk_nrf5340_cpuapp.overlay

//No help, variable specified on the command line.
EXTRA_KCONFIG_TARGETS:UNINITIALIZED=

//No help, variable specified on the command line.
FORCED_CONF_FILE:FILEPATH=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/.config.sysbuild

//Git command line client
GIT_EXECUTABLE:FILEPATH=C:/ncs/toolchains/0b393f9e1b/mingw64/bin/git.exe

//Linker BFD compatibility (compiler reported)
GNULD_LINKER_IS_BFD:BOOL=ON

//GNU ld version
GNULD_VERSION_STRING:STRING=2.38

//Path to a program.
GPERF:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/bin/gperf.exe

//Path to a program.
IMGTOOL:FILEPATH=C:/ncs/v3.0.2/bootloader/mcuboot/scripts/imgtool.py

//NRFXLIB root directory
NRFXLIB_DIR:PATH=C:/ncs/v3.0.2/nrfxlib

//nrfx Directory
NRFX_DIR:PATH=C:/ncs/v3.0.2/modules/hal/nordic/nrfx

//NCS root directory
NRF_DIR:PATH=C:/ncs/v3.0.2/nrf

//Path to a program.
OPENOCD:FILEPATH=C:/Program Files/OpenOCD/bin/openocd.exe

//Path to a program.
PAHOLE:FILEPATH=PAHOLE-NOTFOUND

//Path to a program.
PTY_INTERFACE:FILEPATH=PTY_INTERFACE-NOTFOUND

//Path to a program.
PUNCOVER:FILEPATH=PUNCOVER-NOTFOUND

//Value Computed by CMake
Picolibc_BINARY_DIR:STATIC=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/modules/picolibc

//Value Computed by CMake
Picolibc_IS_TOP_LEVEL:STATIC=OFF

//Value Computed by CMake
Picolibc_SOURCE_DIR:STATIC=C:/ncs/v3.0.2/modules/lib/picolibc

//Path to a program.
Python3_EXECUTABLE:FILEPATH=C:/ncs/toolchains/0b393f9e1b/opt/bin/python.exe

//Path to the SoC directory.
SOC_FULL_DIR:PATH=C:/ncs/v3.0.2/zephyr/soc/nordic

//Path to a CMSIS-SVD file
SOC_SVD_FILE:FILEPATH=C:/ncs/v3.0.2/modules/hal/nordic/nrfx/mdk/nrf5340_application.svd

//No help, variable specified on the command line.
SYSBUILD:BOOL=True

//No help, variable specified on the command line.
SYSBUILD_CACHE:FILEPATH=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00_sysbuild_cache.txt

//Value Computed by CMake
Stethy_V18.00_BINARY_DIR:STATIC=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00

//Value Computed by CMake
Stethy_V18.00_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
Stethy_V18.00_SOURCE_DIR:STATIC=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00

//True if toolchain supports newlib
TOOLCHAIN_HAS_NEWLIB:BOOL=ON

//True if toolchain supports picolibc
TOOLCHAIN_HAS_PICOLIBC:BOOL=ON

//Zephyr toolchain root
TOOLCHAIN_ROOT:STRING=C:/ncs/v3.0.2/zephyr

//Zephyr base
ZEPHYR_BASE:PATH=C:/ncs/v3.0.2/zephyr

//Path to Zephyr git repository index file
ZEPHYR_GIT_INDEX:PATH=C:/ncs/v3.0.2/zephyr/.git/index

//Value Computed by CMake
Zephyr-Kernel_BINARY_DIR:STATIC=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00

//Value Computed by CMake
Zephyr-Kernel_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
Zephyr-Kernel_SOURCE_DIR:STATIC=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00

//The directory containing a CMake configuration file for Zephyr-sdk.
Zephyr-sdk_DIR:PATH=C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/cmake

//The directory containing a CMake configuration file for ZephyrAppConfiguration.
ZephyrAppConfiguration_DIR:PATH=ZephyrAppConfiguration_DIR-NOTFOUND

//The directory containing a CMake configuration file for ZephyrBuildConfiguration.
ZephyrBuildConfiguration_DIR:PATH=C:/ncs/v3.0.2/nrf/share/zephyrbuild-package/cmake

//The directory containing a CMake configuration file for Zephyr.
Zephyr_DIR:PATH=C:/ncs/v3.0.2/zephyr/share/zephyr-package/cmake


########################
# INTERNAL cache entries
########################

//List of board directories for board (nrf5340dk)
BOARD_DIRECTORIES:INTERNAL=C:/ncs/v3.0.2/zephyr/boards/nordic/nrf5340dk
//DT bindings root directories
CACHED_DTS_ROOT_BINDINGS:INTERNAL=C:/ncs/v3.0.2/nrf/dts/bindings;C:/ncs/v3.0.2/zephyr/dts/bindings
//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_COMPILER
CMAKE_ASM_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_COMPILER_AR
CMAKE_ASM_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_COMPILER_RANLIB
CMAKE_ASM_COMPILER_RANLIB-ADVANCED:INTERNAL=1
CMAKE_ASM_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS
CMAKE_ASM_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_DEBUG
CMAKE_ASM_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_MINSIZEREL
CMAKE_ASM_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_RELEASE
CMAKE_ASM_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_ASM_FLAGS_RELWITHDEBINFO
CMAKE_ASM_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=c:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=21
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=0
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/ncs/toolchains/0b393f9e1b/opt/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/ncs/toolchains/0b393f9e1b/opt/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/ncs/toolchains/0b393f9e1b/opt/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=ELF
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=162
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/ncs/toolchains/0b393f9e1b/opt/share/cmake-3.21
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//The build type
CONF_FILE_BUILD_TYPE:INTERNAL=
//Details about finding Dtc
FIND_PACKAGE_MESSAGE_DETAILS_Dtc:INTERNAL=[C:/ncs/toolchains/0b393f9e1b/opt/bin/dtc.exe][v1.4.7(1.4.6)]
//Details about finding GnuLd
FIND_PACKAGE_MESSAGE_DETAILS_GnuLd:INTERNAL=[c:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/arm-zephyr-eabi/bin/ld.bfd.exe][v2.38()]
//Details about finding Python3
FIND_PACKAGE_MESSAGE_DETAILS_Python3:INTERNAL=[C:/ncs/toolchains/0b393f9e1b/opt/bin/python.exe][cfound components: Interpreter ][v3.12.4(3.10)]
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//Zephyr hardware model version
HWM:INTERNAL=v2
//Zephyr hardware model
HWMv2:INTERNAL=True
KERNEL_META_PATH:INTERNAL=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr/zephyr.meta
//shared var
PM_YML_DEP_FILES:INTERNAL=C:/ncs/v3.0.2/nrf/subsys/partition_manager/pm.yml.rpmsg_nrf53
//shared var
PM_YML_FILES:INTERNAL=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/modules/nrf/subsys/partition_manager/pm.yml.rpmsg_nrf53
//List of SoC directories for SoC (nrf5340)
SOC_DIRECTORIES:INTERNAL=C:/ncs/v3.0.2/zephyr/soc/nordic
//SoC Linker script
SOC_LINKER_SCRIPT:INTERNAL=C:/ncs/v3.0.2/zephyr/include/zephyr/arch/arm/cortex_m/scripts/linker.ld
//West
WEST:INTERNAL=C:/ncs/toolchains/0b393f9e1b/opt/bin/python.exe;-m;west
//shared var
ZEPHYR_BINARY_DIR:INTERNAL=C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/Stethy_V18.00/zephyr
//Cached environment variable ZEPHYR_SDK_INSTALL_DIR
ZEPHYR_SDK_INSTALL_DIR:INTERNAL=C:\ncs\toolchains\0b393f9e1b\opt\zephyr-sdk
//Cached environment variable ZEPHYR_TOOLCHAIN_VARIANT
ZEPHYR_TOOLCHAIN_VARIANT:INTERNAL=zephyr
_Python3_EXECUTABLE:INTERNAL=C:/ncs/toolchains/0b393f9e1b/opt/bin/python.exe
//Python3 Properties
_Python3_INTERPRETER_PROPERTIES:INTERNAL=Python;3;12;4;64;;cp312-win_amd64.pyd;C:\ncs\toolchains\0b393f9e1b\opt\bin\Lib;C:\ncs\toolchains\0b393f9e1b\opt\bin\Lib;C:\ncs\toolchains\0b393f9e1b\opt\bin\Lib\site-packages;C:\ncs\toolchains\0b393f9e1b\opt\bin\Lib\site-packages
_Python3_INTERPRETER_SIGNATURE:INTERNAL=789456af6c61fc011023bd90a8135e32

