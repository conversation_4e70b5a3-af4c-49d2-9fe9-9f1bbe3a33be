/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_SOCKET_SERVICE_H
#define Z_INCLUDE_SYSCALLS_SOCKET_SERVICE_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_net_socket_service_register(const struct net_socket_service_desc * service, struct zsock_pollfd * fds, int len, void * user_data);

__pinned_func
static inline int net_socket_service_register(const struct net_socket_service_desc * service, struct zsock_pollfd * fds, int len, void * user_data)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct net_socket_service_desc * val; } parm0 = { .val = service };
		union { uintptr_t x; struct zsock_pollfd * val; } parm1 = { .val = fds };
		union { uintptr_t x; int val; } parm2 = { .val = len };
		union { uintptr_t x; void * val; } parm3 = { .val = user_data };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_NET_SOCKET_SERVICE_REGISTER);
	}
#endif
	compiler_barrier();
	return z_impl_net_socket_service_register(service, fds, len, user_data);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define net_socket_service_register(service, fds, len, user_data) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_NET_SOCKET_SERVICE_REGISTER, net_socket_service_register, service, fds, len, user_data); 	syscall__retval = net_socket_service_register(service, fds, len, user_data); 	sys_port_trace_syscall_exit(K_SYSCALL_NET_SOCKET_SERVICE_REGISTER, net_socket_service_register, service, fds, len, user_data, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
