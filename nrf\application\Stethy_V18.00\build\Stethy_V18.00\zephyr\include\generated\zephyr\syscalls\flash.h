/* auto-generated by gen_syscalls.py, don't edit */

#ifndef Z_INCLUDE_SYSCALLS_FLASH_H
#define Z_INCLUDE_SYSCALLS_FLASH_H


#include <zephyr/tracing/tracing_syscall.h>

#ifndef _ASMLANGUAGE

#include <stdarg.h>

#include <zephyr/syscall_list.h>
#include <zephyr/syscall.h>

#include <zephyr/linker/sections.h>


#ifdef __cplusplus
extern "C" {
#endif

extern int z_impl_flash_read(const struct device * dev, off_t offset, void * data, size_t len);

__pinned_func
static inline int flash_read(const struct device * dev, off_t offset, void * data, size_t len)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; off_t val; } parm1 = { .val = offset };
		union { uintptr_t x; void * val; } parm2 = { .val = data };
		union { uintptr_t x; size_t val; } parm3 = { .val = len };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_FLASH_READ);
	}
#endif
	compiler_barrier();
	return z_impl_flash_read(dev, offset, data, len);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define flash_read(dev, offset, data, len) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_FLASH_READ, flash_read, dev, offset, data, len); 	syscall__retval = flash_read(dev, offset, data, len); 	sys_port_trace_syscall_exit(K_SYSCALL_FLASH_READ, flash_read, dev, offset, data, len, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_flash_write(const struct device * dev, off_t offset, const void * data, size_t len);

__pinned_func
static inline int flash_write(const struct device * dev, off_t offset, const void * data, size_t len)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; off_t val; } parm1 = { .val = offset };
		union { uintptr_t x; const void * val; } parm2 = { .val = data };
		union { uintptr_t x; size_t val; } parm3 = { .val = len };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_FLASH_WRITE);
	}
#endif
	compiler_barrier();
	return z_impl_flash_write(dev, offset, data, len);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define flash_write(dev, offset, data, len) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_FLASH_WRITE, flash_write, dev, offset, data, len); 	syscall__retval = flash_write(dev, offset, data, len); 	sys_port_trace_syscall_exit(K_SYSCALL_FLASH_WRITE, flash_write, dev, offset, data, len, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_flash_erase(const struct device * dev, off_t offset, size_t size);

__pinned_func
static inline int flash_erase(const struct device * dev, off_t offset, size_t size)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; off_t val; } parm1 = { .val = offset };
		union { uintptr_t x; size_t val; } parm2 = { .val = size };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_FLASH_ERASE);
	}
#endif
	compiler_barrier();
	return z_impl_flash_erase(dev, offset, size);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define flash_erase(dev, offset, size) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_FLASH_ERASE, flash_erase, dev, offset, size); 	syscall__retval = flash_erase(dev, offset, size); 	sys_port_trace_syscall_exit(K_SYSCALL_FLASH_ERASE, flash_erase, dev, offset, size, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_flash_get_size(const struct device * dev, uint64_t * size);

__pinned_func
static inline int flash_get_size(const struct device * dev, uint64_t * size)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint64_t * val; } parm1 = { .val = size };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_FLASH_GET_SIZE);
	}
#endif
	compiler_barrier();
	return z_impl_flash_get_size(dev, size);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define flash_get_size(dev, size) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_FLASH_GET_SIZE, flash_get_size, dev, size); 	syscall__retval = flash_get_size(dev, size); 	sys_port_trace_syscall_exit(K_SYSCALL_FLASH_GET_SIZE, flash_get_size, dev, size, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_flash_fill(const struct device * dev, uint8_t val, off_t offset, size_t size);

__pinned_func
static inline int flash_fill(const struct device * dev, uint8_t val, off_t offset, size_t size)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint8_t val; } parm1 = { .val = val };
		union { uintptr_t x; off_t val; } parm2 = { .val = offset };
		union { uintptr_t x; size_t val; } parm3 = { .val = size };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_FLASH_FILL);
	}
#endif
	compiler_barrier();
	return z_impl_flash_fill(dev, val, offset, size);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define flash_fill(dev, val, offset, size) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_FLASH_FILL, flash_fill, dev, val, offset, size); 	syscall__retval = flash_fill(dev, val, offset, size); 	sys_port_trace_syscall_exit(K_SYSCALL_FLASH_FILL, flash_fill, dev, val, offset, size, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_flash_flatten(const struct device * dev, off_t offset, size_t size);

__pinned_func
static inline int flash_flatten(const struct device * dev, off_t offset, size_t size)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; off_t val; } parm1 = { .val = offset };
		union { uintptr_t x; size_t val; } parm2 = { .val = size };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_FLASH_FLATTEN);
	}
#endif
	compiler_barrier();
	return z_impl_flash_flatten(dev, offset, size);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define flash_flatten(dev, offset, size) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_FLASH_FLATTEN, flash_flatten, dev, offset, size); 	syscall__retval = flash_flatten(dev, offset, size); 	sys_port_trace_syscall_exit(K_SYSCALL_FLASH_FLATTEN, flash_flatten, dev, offset, size, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_flash_get_page_info_by_offs(const struct device * dev, off_t offset, struct flash_pages_info * info);

__pinned_func
static inline int flash_get_page_info_by_offs(const struct device * dev, off_t offset, struct flash_pages_info * info)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; off_t val; } parm1 = { .val = offset };
		union { uintptr_t x; struct flash_pages_info * val; } parm2 = { .val = info };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_FLASH_GET_PAGE_INFO_BY_OFFS);
	}
#endif
	compiler_barrier();
	return z_impl_flash_get_page_info_by_offs(dev, offset, info);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define flash_get_page_info_by_offs(dev, offset, info) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_FLASH_GET_PAGE_INFO_BY_OFFS, flash_get_page_info_by_offs, dev, offset, info); 	syscall__retval = flash_get_page_info_by_offs(dev, offset, info); 	sys_port_trace_syscall_exit(K_SYSCALL_FLASH_GET_PAGE_INFO_BY_OFFS, flash_get_page_info_by_offs, dev, offset, info, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_flash_get_page_info_by_idx(const struct device * dev, uint32_t page_index, struct flash_pages_info * info);

__pinned_func
static inline int flash_get_page_info_by_idx(const struct device * dev, uint32_t page_index, struct flash_pages_info * info)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint32_t val; } parm1 = { .val = page_index };
		union { uintptr_t x; struct flash_pages_info * val; } parm2 = { .val = info };
		return (int) arch_syscall_invoke3(parm0.x, parm1.x, parm2.x, K_SYSCALL_FLASH_GET_PAGE_INFO_BY_IDX);
	}
#endif
	compiler_barrier();
	return z_impl_flash_get_page_info_by_idx(dev, page_index, info);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define flash_get_page_info_by_idx(dev, page_index, info) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_FLASH_GET_PAGE_INFO_BY_IDX, flash_get_page_info_by_idx, dev, page_index, info); 	syscall__retval = flash_get_page_info_by_idx(dev, page_index, info); 	sys_port_trace_syscall_exit(K_SYSCALL_FLASH_GET_PAGE_INFO_BY_IDX, flash_get_page_info_by_idx, dev, page_index, info, syscall__retval); 	syscall__retval; })
#endif
#endif


extern size_t z_impl_flash_get_page_count(const struct device * dev);

__pinned_func
static inline size_t flash_get_page_count(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (size_t) arch_syscall_invoke1(parm0.x, K_SYSCALL_FLASH_GET_PAGE_COUNT);
	}
#endif
	compiler_barrier();
	return z_impl_flash_get_page_count(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define flash_get_page_count(dev) ({ 	size_t syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_FLASH_GET_PAGE_COUNT, flash_get_page_count, dev); 	syscall__retval = flash_get_page_count(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_FLASH_GET_PAGE_COUNT, flash_get_page_count, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_flash_sfdp_read(const struct device * dev, off_t offset, void * data, size_t len);

__pinned_func
static inline int flash_sfdp_read(const struct device * dev, off_t offset, void * data, size_t len)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; off_t val; } parm1 = { .val = offset };
		union { uintptr_t x; void * val; } parm2 = { .val = data };
		union { uintptr_t x; size_t val; } parm3 = { .val = len };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_FLASH_SFDP_READ);
	}
#endif
	compiler_barrier();
	return z_impl_flash_sfdp_read(dev, offset, data, len);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define flash_sfdp_read(dev, offset, data, len) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_FLASH_SFDP_READ, flash_sfdp_read, dev, offset, data, len); 	syscall__retval = flash_sfdp_read(dev, offset, data, len); 	sys_port_trace_syscall_exit(K_SYSCALL_FLASH_SFDP_READ, flash_sfdp_read, dev, offset, data, len, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_flash_read_jedec_id(const struct device * dev, uint8_t * id);

__pinned_func
static inline int flash_read_jedec_id(const struct device * dev, uint8_t * id)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint8_t * val; } parm1 = { .val = id };
		return (int) arch_syscall_invoke2(parm0.x, parm1.x, K_SYSCALL_FLASH_READ_JEDEC_ID);
	}
#endif
	compiler_barrier();
	return z_impl_flash_read_jedec_id(dev, id);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define flash_read_jedec_id(dev, id) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_FLASH_READ_JEDEC_ID, flash_read_jedec_id, dev, id); 	syscall__retval = flash_read_jedec_id(dev, id); 	sys_port_trace_syscall_exit(K_SYSCALL_FLASH_READ_JEDEC_ID, flash_read_jedec_id, dev, id, syscall__retval); 	syscall__retval; })
#endif
#endif


extern size_t z_impl_flash_get_write_block_size(const struct device * dev);

__pinned_func
static inline size_t flash_get_write_block_size(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (size_t) arch_syscall_invoke1(parm0.x, K_SYSCALL_FLASH_GET_WRITE_BLOCK_SIZE);
	}
#endif
	compiler_barrier();
	return z_impl_flash_get_write_block_size(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define flash_get_write_block_size(dev) ({ 	size_t syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_FLASH_GET_WRITE_BLOCK_SIZE, flash_get_write_block_size, dev); 	syscall__retval = flash_get_write_block_size(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_FLASH_GET_WRITE_BLOCK_SIZE, flash_get_write_block_size, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern const struct flash_parameters * z_impl_flash_get_parameters(const struct device * dev);

__pinned_func
static inline const struct flash_parameters * flash_get_parameters(const struct device * dev)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		return (const struct flash_parameters *) arch_syscall_invoke1(parm0.x, K_SYSCALL_FLASH_GET_PARAMETERS);
	}
#endif
	compiler_barrier();
	return z_impl_flash_get_parameters(dev);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define flash_get_parameters(dev) ({ 	const struct flash_parameters * syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_FLASH_GET_PARAMETERS, flash_get_parameters, dev); 	syscall__retval = flash_get_parameters(dev); 	sys_port_trace_syscall_exit(K_SYSCALL_FLASH_GET_PARAMETERS, flash_get_parameters, dev, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_flash_ex_op(const struct device * dev, uint16_t code, const uintptr_t in, void * out);

__pinned_func
static inline int flash_ex_op(const struct device * dev, uint16_t code, const uintptr_t in, void * out)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = dev };
		union { uintptr_t x; uint16_t val; } parm1 = { .val = code };
		union { uintptr_t x; const uintptr_t val; } parm2 = { .val = in };
		union { uintptr_t x; void * val; } parm3 = { .val = out };
		return (int) arch_syscall_invoke4(parm0.x, parm1.x, parm2.x, parm3.x, K_SYSCALL_FLASH_EX_OP);
	}
#endif
	compiler_barrier();
	return z_impl_flash_ex_op(dev, code, in, out);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define flash_ex_op(dev, code, in, out) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_FLASH_EX_OP, flash_ex_op, dev, code, in, out); 	syscall__retval = flash_ex_op(dev, code, in, out); 	sys_port_trace_syscall_exit(K_SYSCALL_FLASH_EX_OP, flash_ex_op, dev, code, in, out, syscall__retval); 	syscall__retval; })
#endif
#endif


extern int z_impl_flash_copy(const struct device * src_dev, off_t src_offset, const struct device * dst_dev, off_t dst_offset, off_t size, uint8_t * buf, size_t buf_size);

__pinned_func
static inline int flash_copy(const struct device * src_dev, off_t src_offset, const struct device * dst_dev, off_t dst_offset, off_t size, uint8_t * buf, size_t buf_size)
{
#ifdef CONFIG_USERSPACE
	if (z_syscall_trap()) {
		union { uintptr_t x; const struct device * val; } parm0 = { .val = src_dev };
		union { uintptr_t x; off_t val; } parm1 = { .val = src_offset };
		union { uintptr_t x; const struct device * val; } parm2 = { .val = dst_dev };
		union { uintptr_t x; off_t val; } parm3 = { .val = dst_offset };
		union { uintptr_t x; off_t val; } parm4 = { .val = size };
		union { uintptr_t x; uint8_t * val; } parm5 = { .val = buf };
		union { uintptr_t x; size_t val; } parm6 = { .val = buf_size };
		uintptr_t more[] = {
			parm5.x,
			parm6.x
		};
		return (int) arch_syscall_invoke6(parm0.x, parm1.x, parm2.x, parm3.x, parm4.x, (uintptr_t) &more, K_SYSCALL_FLASH_COPY);
	}
#endif
	compiler_barrier();
	return z_impl_flash_copy(src_dev, src_offset, dst_dev, dst_offset, size, buf, buf_size);
}

#if defined(CONFIG_TRACING_SYSCALL)
#ifndef DISABLE_SYSCALL_TRACING

#define flash_copy(src_dev, src_offset, dst_dev, dst_offset, size, buf, buf_size) ({ 	int syscall__retval; 	sys_port_trace_syscall_enter(K_SYSCALL_FLASH_COPY, flash_copy, src_dev, src_offset, dst_dev, dst_offset, size, buf, buf_size); 	syscall__retval = flash_copy(src_dev, src_offset, dst_dev, dst_offset, size, buf, buf_size); 	sys_port_trace_syscall_exit(K_SYSCALL_FLASH_COPY, flash_copy, src_dev, src_offset, dst_dev, dst_offset, size, buf, buf_size, syscall__retval); 	syscall__retval; })
#endif
#endif


#ifdef __cplusplus
}
#endif

#endif
#endif /* include guard */
