ITERABLE_SECTION_ROM(gpio_driver_api, 4)
ITERABLE_SECTION_ROM(i2s_driver_api, 4)
ITERABLE_SECTION_ROM(shared_irq_driver_api, 4)
ITERABLE_SECTION_ROM(crypto_driver_api, 4)
ITERABLE_SECTION_ROM(adc_driver_api, 4)
ITERABLE_SECTION_ROM(auxdisplay_driver_api, 4)
ITERABLE_SECTION_ROM(bbram_driver_api, 4)
ITERABLE_SECTION_ROM(bt_hci_driver_api, 4)
ITERABLE_SECTION_ROM(can_driver_api, 4)
ITERABLE_SECTION_ROM(cellular_driver_api, 4)
ITERABLE_SECTION_ROM(charger_driver_api, 4)
ITERABLE_SECTION_ROM(clock_control_driver_api, 4)
ITERABLE_SECTION_ROM(comparator_driver_api, 4)
ITERABLE_SECTION_ROM(coredump_driver_api, 4)
ITERABLE_SECTION_ROM(counter_driver_api, 4)
ITERABLE_SECTION_ROM(dac_driver_api, 4)
ITERABLE_SECTION_ROM(dai_driver_api, 4)
ITERABLE_SECTION_ROM(display_driver_api, 4)
ITERABLE_SECTION_ROM(dma_driver_api, 4)
ITERABLE_SECTION_ROM(edac_driver_api, 4)
ITERABLE_SECTION_ROM(eeprom_driver_api, 4)
ITERABLE_SECTION_ROM(emul_bbram_driver_api, 4)
ITERABLE_SECTION_ROM(fuel_gauge_emul_driver_api, 4)
ITERABLE_SECTION_ROM(emul_sensor_driver_api, 4)
ITERABLE_SECTION_ROM(entropy_driver_api, 4)
ITERABLE_SECTION_ROM(espi_driver_api, 4)
ITERABLE_SECTION_ROM(espi_saf_driver_api, 4)
ITERABLE_SECTION_ROM(flash_driver_api, 4)
ITERABLE_SECTION_ROM(fpga_driver_api, 4)
ITERABLE_SECTION_ROM(fuel_gauge_driver_api, 4)
ITERABLE_SECTION_ROM(gnss_driver_api, 4)
ITERABLE_SECTION_ROM(haptics_driver_api, 4)
ITERABLE_SECTION_ROM(hwspinlock_driver_api, 4)
ITERABLE_SECTION_ROM(i2c_driver_api, 4)
ITERABLE_SECTION_ROM(i2c_target_driver_api, 4)
ITERABLE_SECTION_ROM(i3c_driver_api, 4)
ITERABLE_SECTION_ROM(ipm_driver_api, 4)
ITERABLE_SECTION_ROM(kscan_driver_api, 4)
ITERABLE_SECTION_ROM(led_driver_api, 4)
ITERABLE_SECTION_ROM(led_strip_driver_api, 4)
ITERABLE_SECTION_ROM(lora_driver_api, 4)
ITERABLE_SECTION_ROM(mbox_driver_api, 4)
ITERABLE_SECTION_ROM(mdio_driver_api, 4)
ITERABLE_SECTION_ROM(mipi_dbi_driver_api, 4)
ITERABLE_SECTION_ROM(mipi_dsi_driver_api, 4)
ITERABLE_SECTION_ROM(mspi_driver_api, 4)
ITERABLE_SECTION_ROM(peci_driver_api, 4)
ITERABLE_SECTION_ROM(ps2_driver_api, 4)
ITERABLE_SECTION_ROM(ptp_clock_driver_api, 4)
ITERABLE_SECTION_ROM(pwm_driver_api, 4)
ITERABLE_SECTION_ROM(regulator_parent_driver_api, 4)
ITERABLE_SECTION_ROM(regulator_driver_api, 4)
ITERABLE_SECTION_ROM(reset_driver_api, 4)
ITERABLE_SECTION_ROM(retained_mem_driver_api, 4)
ITERABLE_SECTION_ROM(rtc_driver_api, 4)
ITERABLE_SECTION_ROM(sdhc_driver_api, 4)
ITERABLE_SECTION_ROM(sensor_driver_api, 4)
ITERABLE_SECTION_ROM(smbus_driver_api, 4)
ITERABLE_SECTION_ROM(spi_driver_api, 4)
ITERABLE_SECTION_ROM(stepper_driver_api, 4)
ITERABLE_SECTION_ROM(syscon_driver_api, 4)
ITERABLE_SECTION_ROM(tee_driver_api, 4)
ITERABLE_SECTION_ROM(video_driver_api, 4)
ITERABLE_SECTION_ROM(w1_driver_api, 4)
ITERABLE_SECTION_ROM(wdt_driver_api, 4)
ITERABLE_SECTION_ROM(can_transceiver_driver_api, 4)
ITERABLE_SECTION_ROM(nrf_clock_control_driver_api, 4)
ITERABLE_SECTION_ROM(i3c_target_driver_api, 4)
ITERABLE_SECTION_ROM(its_driver_api, 4)
ITERABLE_SECTION_ROM(vtd_driver_api, 4)
ITERABLE_SECTION_ROM(tgpio_driver_api, 4)
ITERABLE_SECTION_ROM(pcie_ctrl_driver_api, 4)
ITERABLE_SECTION_ROM(pcie_ep_driver_api, 4)
ITERABLE_SECTION_ROM(svc_driver_api, 4)
ITERABLE_SECTION_ROM(uart_driver_api, 4)
ITERABLE_SECTION_ROM(bc12_emul_driver_api, 4)
ITERABLE_SECTION_ROM(bc12_driver_api, 4)
ITERABLE_SECTION_ROM(usbc_ppc_driver_api, 4)
ITERABLE_SECTION_ROM(tcpc_driver_api, 4)
ITERABLE_SECTION_ROM(usbc_vbus_driver_api, 4)
ITERABLE_SECTION_ROM(ivshmem_driver_api, 4)
ITERABLE_SECTION_ROM(ethphy_driver_api, 4)
