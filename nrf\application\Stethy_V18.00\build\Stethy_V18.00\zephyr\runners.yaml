# Available runners configured by board.cmake.
runners:
- nrfutil
- nrfjprog
- jlink

# Default flash runner if --runner is not given.
flash-runner: nrfutil

# Default debug runner if --runner is not given.
debug-runner: jlink

# Common runner configuration values.
config:
  board_dir: C:/ncs/v3.0.2/zephyr/boards/nordic/nrf5340dk
  # Build outputs:
  elf_file: zephyr.elf
  hex_file: C:/Users/<USER>/OneDrive/Documents/Projects/nrf/application/Stethy_V18.00/build/merged.hex
  bin_file: zephyr.bin
  # Host tools:
  gdb: C:/ncs/toolchains/0b393f9e1b/opt/zephyr-sdk/arm-zephyr-eabi/bin/arm-zephyr-eabi-gdb.exe
  openocd: C:/Program Files/OpenOCD/bin/openocd.exe
  openocd_search:
    - C:\Program Files/OpenOCD/share/openocd/scripts

# Runner specific arguments
args:
  nrfutil:
    - --ext-mem-config-file=C:/ncs/v3.0.2/zephyr/boards/nordic/nrf5340dk/support/nrf5340dk_qspi_nrfutil_config.json
  nrfjprog:
    []

  jlink:
    - --dt-flash=y
    - --device=nrf5340_xxaa_app
    - --speed=4000
